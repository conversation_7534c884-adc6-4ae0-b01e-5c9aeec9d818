<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1500"
   version = "1.7">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "27A81E9A2B4304B5007DD04D"
               BuildableName = "&#x7f51;&#x683c;&#x4ea4;&#x6613;&#x5de5;&#x5177;&#x7bb1;.app"
               BlueprintName = "&#x7f51;&#x683c;&#x4ea4;&#x6613;&#x5de5;&#x5177;&#x7bb1;"
               ReferencedContainer = "container:GridTradingToolbox.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "YES"
      shouldAutocreateTestPlan = "YES">
      <Testables>
         <TestableReference
            skipped = "NO"
            parallelizable = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "27A81EAE2B4304B6007DD04D"
               BuildableName = "GridTradingToolboxTests.xctest"
               BlueprintName = "GridTradingToolboxTests"
               ReferencedContainer = "container:GridTradingToolbox.xcodeproj">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "27A81EB82B4304B6007DD04D"
               BuildableName = "GridTradingToolboxUITests.xctest"
               BlueprintName = "GridTradingToolboxUITests"
               ReferencedContainer = "container:GridTradingToolbox.xcodeproj">
            </BuildableReference>
         </TestableReference>
      </Testables>
   </TestAction>
   <LaunchAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "27A81E9A2B4304B5007DD04D"
            BuildableName = "&#x7f51;&#x683c;&#x4ea4;&#x6613;&#x5de5;&#x5177;&#x7bb1;.app"
            BlueprintName = "&#x7f51;&#x683c;&#x4ea4;&#x6613;&#x5de5;&#x5177;&#x7bb1;"
            ReferencedContainer = "container:GridTradingToolbox.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
      <CommandLineArguments>
         <CommandLineArgument
            argument = "-com.apple.CoreData.SQLDebug 1 "
            isEnabled = "YES">
         </CommandLineArgument>
      </CommandLineArguments>
      <AdditionalOptions>
         <AdditionalOption
            key = "NSZombieEnabled"
            value = "YES"
            isEnabled = "YES">
         </AdditionalOption>
      </AdditionalOptions>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Release"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "27A81E9A2B4304B5007DD04D"
            BuildableName = "&#x7f51;&#x683c;&#x4ea4;&#x6613;&#x5de5;&#x5177;&#x7bb1;.app"
            BlueprintName = "&#x7f51;&#x683c;&#x4ea4;&#x6613;&#x5de5;&#x5177;&#x7bb1;"
            ReferencedContainer = "container:GridTradingToolbox.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>
