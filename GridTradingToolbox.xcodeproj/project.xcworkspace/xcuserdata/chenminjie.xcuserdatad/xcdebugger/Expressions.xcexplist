<?xml version="1.0" encoding="UTF-8"?>
<VariablesViewState
   version = "1.0">
   <ContextStates>
      <ContextState
         contextName = "Strategy.costs.getter:Strategy.swift">
      </ContextState>
      <ContextState
         contextName = "DataHandler.updateBuyTradeLog(grid:tradePrice:tradeShares:tradeAt:mood:note:):DataHandler.swift">
         <PersistentStrings>
            <PersistentString
               value = "grid.holdShares">
            </PersistentString>
            <PersistentString
               value = "buyTradeLog.tradeShares">
            </PersistentString>
            <PersistentString
               value = "grid.sellTradeLog!.tradeShares">
            </PersistentString>
         </PersistentStrings>
      </ContextState>
      <ContextState
         contextName = "Strategy.getNextTradeGrid(gridType:):Strategy.swift">
         <PersistentStrings>
            <PersistentString
               value = "print(grids[grids.count - 1].grade - curInterval/100)">
            </PersistentString>
            <PersistentString
               value = "interval">
            </PersistentString>
            <PersistentString
               value = "curGrids[1].grade">
            </PersistentString>
            <PersistentString
               value = "curGrids[0].grade">
            </PersistentString>
            <PersistentString
               value = "print(grid.grade)">
            </PersistentString>
         </PersistentStrings>
      </ContextState>
      <ContextState
         contextName = "static TradeGrid.from(syncGrid:strategyId:):TradeGrid.swift">
         <PersistentStrings>
            <PersistentString
               value = "grid.tradeLogs.count">
            </PersistentString>
         </PersistentStrings>
      </ContextState>
      <ContextState
         contextName = "static TradeLog.from(syncTradeLog:strategyId:gridId:):TradeLog.swift">
         <PersistentStrings>
            <PersistentString
               value = "tradeLog.tradePrice">
            </PersistentString>
         </PersistentStrings>
      </ContextState>
      <ContextState
         contextName = "TradeGrid.displayProfit.getter:TradeGrid.swift">
         <PersistentStrings>
            <PersistentString
               value = "tradeLogs.count">
            </PersistentString>
         </PersistentStrings>
      </ContextState>
      <ContextState
         contextName = "SchemaV1.Strategy.avgHoldDaysOfGrids.getter:Strategy.swift">
         <PersistentStrings>
            <PersistentString
               value = "grid.strategy!.grids!.count">
            </PersistentString>
         </PersistentStrings>
      </ContextState>
      <ContextState
         contextName = "HomeView.summary():HomeView.swift">
         <PersistentStrings>
            <PersistentString
               value = "streategies.count">
            </PersistentString>
         </PersistentStrings>
      </ContextState>
      <ContextState
         contextName = "StrategyView.init(strategy:):StrategyView.swift">
         <PersistentStrings>
            <PersistentString
               value = "strategy.interest">
            </PersistentString>
            <PersistentString
               value = "strategy.soldGrids[2].tradeLogs.count">
            </PersistentString>
         </PersistentStrings>
      </ContextState>
      <ContextState
         contextName = "closure #1 in closure #1 in closure #1 in closure #3 in closure #1 in closure #1 in closure #1 in StrategyView.body.getter:StrategyView.swift">
         <PersistentStrings>
            <PersistentString
               value = "$0.price">
            </PersistentString>
            <PersistentString
               value = "$0.tradeDay">
            </PersistentString>
         </PersistentStrings>
      </ContextState>
      <ContextState
         contextName = "SellGridView.recordSellOut():SellGridView.swift">
         <PersistentStrings>
            <PersistentString
               value = "isEditMode">
            </PersistentString>
         </PersistentStrings>
      </ContextState>
      <ContextState
         contextName = "StrategySummaryCardView.displayProfit.getter:StrategyView.swift">
         <PersistentStrings>
            <PersistentString
               value = "strategy.realTotalProfit">
            </PersistentString>
         </PersistentStrings>
      </ContextState>
      <ContextState
         contextName = "SellGridView.init(strategy:grid:):SellGridView.swift">
         <PersistentStrings>
            <PersistentString
               value = "grid.displayTheoreticalSellPrice">
            </PersistentString>
         </PersistentStrings>
      </ContextState>
      <ContextState
         contextName = "StressTestView.init(grids:mediumLargeSwitch:):StressTestView.swift">
         <PersistentStrings>
            <PersistentString
               value = "self.smallGrids.count">
            </PersistentString>
            <PersistentString
               value = "grids.count">
            </PersistentString>
            <PersistentString
               value = "small.count == 7">
            </PersistentString>
         </PersistentStrings>
      </ContextState>
      <ContextState
         contextName = "main:GridTradingToolboxApp.swift">
      </ContextState>
      <ContextState
         contextName = "TradeGrid.getSellPriceWithInterest(calInterest:period:interest:):TradeGrid.swift">
         <PersistentStrings>
            <PersistentString
               value = "pow(Double(interest! + 100000) / 100000.0, Double(times))">
            </PersistentString>
         </PersistentStrings>
      </ContextState>
      <ContextState
         contextName = "SellGridView.init(strategy:grid:confettiFlag:):SellGridView.swift">
         <PersistentStrings>
            <PersistentString
               value = "grid.holdShares">
            </PersistentString>
         </PersistentStrings>
      </ContextState>
      <ContextState
         contextName = "BuyGridView.recordBuyIn():BuyGridView.swift">
         <PersistentStrings>
            <PersistentString
               value = "buyShares">
            </PersistentString>
         </PersistentStrings>
      </ContextState>
      <ContextState
         contextName = "Strategy.retainShares.getter:Strategy.swift">
         <PersistentStrings>
            <PersistentString
               value = "grid.holdShares">
            </PersistentString>
            <PersistentString
               value = "grid.grade">
            </PersistentString>
         </PersistentStrings>
      </ContextState>
      <ContextState
         contextName = "closure #2 in closure #1 in closure #1 in SellGridView.body.getter:SellGridView.swift">
         <PersistentStrings>
            <PersistentString
               value = "grid.holdShares">
            </PersistentString>
         </PersistentStrings>
      </ContextState>
   </ContextStates>
</VariablesViewState>
