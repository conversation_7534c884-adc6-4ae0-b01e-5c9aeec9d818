// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 60;
	objects = {

/* Begin PBXBuildFile section */
		271062292C5D07890067078D /* FeatureUpdateView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 271062282C5D07890067078D /* FeatureUpdateView.swift */; };
		2710622E2C6A44970067078D /* SessionManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2710622D2C6A44970067078D /* SessionManager.swift */; };
		27340B442B94354700709051 /* Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27340B432B94354700709051 /* Extensions.swift */; };
		2741AFA22B89BD40001774A7 /* Launch Screen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 2741AFA12B89BD40001774A7 /* Launch Screen.storyboard */; };
		2750F3752BEF1B6C007AB780 /* StockAccount.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2750F3742BEF1B6C007AB780 /* StockAccount.swift */; };
		2750F3772BEF6A2C007AB780 /* StockAccountView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2750F3762BEF6A2C007AB780 /* StockAccountView.swift */; };
		2750F37A2BEF9038007AB780 /* CreateStockAccountView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2750F3792BEF9038007AB780 /* CreateStockAccountView.swift */; };
		2750F37D2BF772FD007AB780 /* ArchiveStrategyListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2750F37C2BF772FD007AB780 /* ArchiveStrategyListView.swift */; };
		27538DB62D8FBE9500AB7332 /* IndexHelpView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27538DB52D8FBE9500AB7332 /* IndexHelpView.swift */; };
		27538DBC2D9B890D00AB7332 /* IndexFilterViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27538DBB2D9B890D00AB7332 /* IndexFilterViewModel.swift */; };
		27538DBE2D9B891B00AB7332 /* IndexFilterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27538DBD2D9B891B00AB7332 /* IndexFilterView.swift */; };
		27538DC02D9B894000AB7332 /* FilteredIndex.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27538DBF2D9B894000AB7332 /* FilteredIndex.swift */; };
		2754C8522C47AFFE00C9D1A8 /* GridCalendarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2754C8512C47AFFE00C9D1A8 /* GridCalendarView.swift */; };
		2754C8552C47B57B00C9D1A8 /* MijickCalendarView in Frameworks */ = {isa = PBXBuildFile; productRef = 2754C8542C47B57B00C9D1A8 /* MijickCalendarView */; };
		275643E62CCFB2A600AA1D2A /* EditSellRetainShareView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 275643E52CCFB2A600AA1D2A /* EditSellRetainShareView.swift */; };
		275643E92CD79E1500AA1D2A /* IndexView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 275643E82CD79E1500AA1D2A /* IndexView.swift */; };
		275643EB2CD7A48700AA1D2A /* IndexViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 275643EA2CD7A48700AA1D2A /* IndexViewModel.swift */; };
		275643ED2CDB752700AA1D2A /* IndexSearchView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 275643EC2CDB752700AA1D2A /* IndexSearchView.swift */; };
		275643EF2CDB780D00AA1D2A /* IndexSearchViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 275643EE2CDB780D00AA1D2A /* IndexSearchViewModel.swift */; };
		275AD13F2BC3E66C00C3FEDE /* StocksPriceViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 275AD13E2BC3E66C00C3FEDE /* StocksPriceViewModel.swift */; };
		275AD1412BC3EA8000C3FEDE /* TriggerGridCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 275AD1402BC3EA8000C3FEDE /* TriggerGridCardView.swift */; };
		275AD1432BC6D7F500C3FEDE /* RealtimeGridPriceView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 275AD1422BC6D7F500C3FEDE /* RealtimeGridPriceView.swift */; };
		275EA1B12D28245700FA8A08 /* ICloudSyncStatusView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 275EA1B02D28245700FA8A08 /* ICloudSyncStatusView.swift */; };
		278F5E7E2B5CD72F00BCD3D0 /* SellGridView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 278F5E7D2B5CD72F00BCD3D0 /* SellGridView.swift */; };
		278F5E8D2B5D055200BCD3D0 /* ConfettiSwiftUI in Frameworks */ = {isa = PBXBuildFile; productRef = 278F5E8C2B5D055200BCD3D0 /* ConfettiSwiftUI */; };
		278F5E8F2B5FE17300BCD3D0 /* TradeHistoryView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 278F5E8E2B5FE17300BCD3D0 /* TradeHistoryView.swift */; };
		278F5E912B64AFDA00BCD3D0 /* Utils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 278F5E902B64AFDA00BCD3D0 /* Utils.swift */; };
		278F5E942B652CD200BCD3D0 /* Settings.swift in Sources */ = {isa = PBXBuildFile; fileRef = 278F5E932B652CD200BCD3D0 /* Settings.swift */; };
		278F5E982B6534C200BCD3D0 /* LandingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 278F5E972B6534C200BCD3D0 /* LandingView.swift */; };
		278F5E9C2B66567800BCD3D0 /* ImportDataView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 278F5E9B2B66567800BCD3D0 /* ImportDataView.swift */; };
		278F5E9E2B67CB9F00BCD3D0 /* Response.swift in Sources */ = {isa = PBXBuildFile; fileRef = 278F5E9D2B67CB9F00BCD3D0 /* Response.swift */; };
		279728452D33A13A006665D8 /* StrategySummaryCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 279728442D33A13A006665D8 /* StrategySummaryCardView.swift */; };
		2797284A2D35224F006665D8 /* D-DIN.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 279728492D35224F006665D8 /* D-DIN.ttf */; };
		2797284E2D35272A006665D8 /* D-DIN-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 2797284D2D35272A006665D8 /* D-DIN-Bold.ttf */; };
		27A81E9F2B4304B5007DD04D /* GridTradingToolboxApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27A81E9E2B4304B5007DD04D /* GridTradingToolboxApp.swift */; };
		27A81EA52B4304B6007DD04D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 27A81EA42B4304B6007DD04D /* Assets.xcassets */; };
		27A81EA82B4304B6007DD04D /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 27A81EA72B4304B6007DD04D /* Preview Assets.xcassets */; };
		27A81EB42B4304B6007DD04D /* GridTradingToolboxTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27A81EB32B4304B6007DD04D /* GridTradingToolboxTests.swift */; };
		27A81EBE2B4304B6007DD04D /* GridTradingToolboxUITests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27A81EBD2B4304B6007DD04D /* GridTradingToolboxUITests.swift */; };
		27A81EC02B4304B6007DD04D /* GridTradingToolboxUITestsLaunchTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27A81EBF2B4304B6007DD04D /* GridTradingToolboxUITestsLaunchTests.swift */; };
		27A81ECD2B4436DE007DD04D /* HomeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27A81ECC2B4436DE007DD04D /* HomeView.swift */; };
		27A81ECF2B44411C007DD04D /* CreateStrategyView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27A81ECE2B44411C007DD04D /* CreateStrategyView.swift */; };
		27A81ED12B444EF8007DD04D /* Strategy.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27A81ED02B444EF8007DD04D /* Strategy.swift */; };
		27A81ED42B482E9D007DD04D /* StocksSearchView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27A81ED32B482E9D007DD04D /* StocksSearchView.swift */; };
		27A81ED72B483F64007DD04D /* Stock.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27A81ED62B483F64007DD04D /* Stock.swift */; };
		27A81EE22B4A94E7007DD04D /* TradeGrid.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27A81EE12B4A94E7007DD04D /* TradeGrid.swift */; };
		27A81EE62B539EDD007DD04D /* StressTestView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27A81EE52B539EDD007DD04D /* StressTestView.swift */; };
		27A81EE82B541B48007DD04D /* StrategyView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27A81EE72B541B48007DD04D /* StrategyView.swift */; };
		27A81EEA2B56B3E0007DD04D /* TradeLog.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27A81EE92B56B3E0007DD04D /* TradeLog.swift */; };
		27A81EEC2B56C7FB007DD04D /* BuyGridView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27A81EEB2B56C7FB007DD04D /* BuyGridView.swift */; };
		27A81EEE2B5B88D5007DD04D /* PriceChartView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27A81EED2B5B88D5007DD04D /* PriceChartView.swift */; };
		27B395782B9AF954003BE784 /* AppInfoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27B395772B9AF954003BE784 /* AppInfoView.swift */; };
		27B3957E2BA30407003BE784 /* icon.png in Resources */ = {isa = PBXBuildFile; fileRef = 27B3957D2BA30407003BE784 /* icon.png */; };
		27B841DF2B80F2C10096972A /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 27B841DE2B80F2C10096972A /* StoreKit.framework */; };
		27B841E12B80FF650096972A /* ProMemberView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27B841E02B80FF650096972A /* ProMemberView.swift */; };
		27B841E32B822D680096972A /* Store.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27B841E22B822D680096972A /* Store.swift */; };
		27C706C62C806E5F0027ED73 /* Alamofire in Frameworks */ = {isa = PBXBuildFile; productRef = 27C706C52C806E5F0027ED73 /* Alamofire */; };
		27CE99F72BD3C63B00E0D823 /* StockPriceViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27CE99F62BD3C63B00E0D823 /* StockPriceViewModel.swift */; };
		27CFBB852D37E41C00A2928F /* SummaryCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27CFBB842D37E41C00A2928F /* SummaryCardView.swift */; };
		27D6277E2BB6AFD4006E8F71 /* SellRetainShareView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27D6277D2BB6AFD4006E8F71 /* SellRetainShareView.swift */; };
		27D627802BB6B815006E8F71 /* StrategySummaryPostView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27D6277F2BB6B815006E8F71 /* StrategySummaryPostView.swift */; };
		27D627822BB92A08006E8F71 /* FlexibleTradeLog.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27D627812BB92A08006E8F71 /* FlexibleTradeLog.swift */; };
		27D627842BB98A5A006E8F71 /* StrategyStatisticsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27D627832BB98A5A006E8F71 /* StrategyStatisticsView.swift */; };
		27D627862BBA63E2006E8F71 /* RetainTradeHistoryView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27D627852BBA63E2006E8F71 /* RetainTradeHistoryView.swift */; };
		27D627882BBC16DC006E8F71 /* MoreView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27D627872BBC16DC006E8F71 /* MoreView.swift */; };
		27E316912B6A82BF00456BD7 /* PopupView in Frameworks */ = {isa = PBXBuildFile; productRef = 27E316902B6A82BF00456BD7 /* PopupView */; };
		27E316932B6A85AE00456BD7 /* SharePostView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27E316922B6A85AE00456BD7 /* SharePostView.swift */; };
		27E316952B6E1E5F00456BD7 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27E316942B6E1E5F00456BD7 /* AppDelegate.swift */; };
		27E4D0212CEC4B53004B8872 /* DataHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27E4D0202CEC4B53004B8872 /* DataHandler.swift */; };
		27E4D0252CEC518F004B8872 /* DataProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27E4D0242CEC518F004B8872 /* DataProvider.swift */; };
		27FDE6B02BBD421800083BDF /* UserGuideView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27FDE6AF2BBD421800083BDF /* UserGuideView.swift */; };
		27FDE6B62BC1419100083BDF /* SearchStockViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27FDE6B52BC1419100083BDF /* SearchStockViewModel.swift */; };
		27FF3D832D72F2EA00EDDFAB /* VersionSchema.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27FF3D822D72F2EA00EDDFAB /* VersionSchema.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		27A81EB02B4304B6007DD04D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 27A81E932B4304B5007DD04D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 27A81E9A2B4304B5007DD04D;
			remoteInfo = GridTradingToolbox;
		};
		27A81EBA2B4304B6007DD04D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 27A81E932B4304B5007DD04D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 27A81E9A2B4304B5007DD04D;
			remoteInfo = GridTradingToolbox;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		271062282C5D07890067078D /* FeatureUpdateView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FeatureUpdateView.swift; sourceTree = "<group>"; };
		2710622D2C6A44970067078D /* SessionManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SessionManager.swift; sourceTree = "<group>"; };
		27340B432B94354700709051 /* Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Extensions.swift; sourceTree = "<group>"; };
		2741AFA12B89BD40001774A7 /* Launch Screen.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = "Launch Screen.storyboard"; sourceTree = "<group>"; };
		2750F3742BEF1B6C007AB780 /* StockAccount.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StockAccount.swift; sourceTree = "<group>"; };
		2750F3762BEF6A2C007AB780 /* StockAccountView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StockAccountView.swift; sourceTree = "<group>"; };
		2750F3792BEF9038007AB780 /* CreateStockAccountView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CreateStockAccountView.swift; sourceTree = "<group>"; };
		2750F37C2BF772FD007AB780 /* ArchiveStrategyListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ArchiveStrategyListView.swift; sourceTree = "<group>"; };
		27538DB52D8FBE9500AB7332 /* IndexHelpView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IndexHelpView.swift; sourceTree = "<group>"; };
		27538DBB2D9B890D00AB7332 /* IndexFilterViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IndexFilterViewModel.swift; sourceTree = "<group>"; };
		27538DBD2D9B891B00AB7332 /* IndexFilterView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IndexFilterView.swift; sourceTree = "<group>"; };
		27538DBF2D9B894000AB7332 /* FilteredIndex.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilteredIndex.swift; sourceTree = "<group>"; };
		2754C8512C47AFFE00C9D1A8 /* GridCalendarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GridCalendarView.swift; sourceTree = "<group>"; };
		275643E52CCFB2A600AA1D2A /* EditSellRetainShareView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditSellRetainShareView.swift; sourceTree = "<group>"; };
		275643E82CD79E1500AA1D2A /* IndexView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IndexView.swift; sourceTree = "<group>"; };
		275643EA2CD7A48700AA1D2A /* IndexViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IndexViewModel.swift; sourceTree = "<group>"; };
		275643EC2CDB752700AA1D2A /* IndexSearchView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IndexSearchView.swift; sourceTree = "<group>"; };
		275643EE2CDB780D00AA1D2A /* IndexSearchViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IndexSearchViewModel.swift; sourceTree = "<group>"; };
		275AD13E2BC3E66C00C3FEDE /* StocksPriceViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StocksPriceViewModel.swift; sourceTree = "<group>"; };
		275AD1402BC3EA8000C3FEDE /* TriggerGridCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TriggerGridCardView.swift; sourceTree = "<group>"; };
		275AD1422BC6D7F500C3FEDE /* RealtimeGridPriceView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RealtimeGridPriceView.swift; sourceTree = "<group>"; };
		275EA1B02D28245700FA8A08 /* ICloudSyncStatusView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ICloudSyncStatusView.swift; sourceTree = "<group>"; };
		278F5E7D2B5CD72F00BCD3D0 /* SellGridView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SellGridView.swift; sourceTree = "<group>"; };
		278F5E8E2B5FE17300BCD3D0 /* TradeHistoryView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TradeHistoryView.swift; sourceTree = "<group>"; };
		278F5E902B64AFDA00BCD3D0 /* Utils.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Utils.swift; sourceTree = "<group>"; };
		278F5E932B652CD200BCD3D0 /* Settings.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Settings.swift; sourceTree = "<group>"; };
		278F5E972B6534C200BCD3D0 /* LandingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LandingView.swift; sourceTree = "<group>"; };
		278F5E9B2B66567800BCD3D0 /* ImportDataView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImportDataView.swift; sourceTree = "<group>"; };
		278F5E9D2B67CB9F00BCD3D0 /* Response.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Response.swift; sourceTree = "<group>"; };
		279728442D33A13A006665D8 /* StrategySummaryCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StrategySummaryCardView.swift; sourceTree = "<group>"; };
		279728492D35224F006665D8 /* D-DIN.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "D-DIN.ttf"; sourceTree = "<group>"; };
		2797284D2D35272A006665D8 /* D-DIN-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "D-DIN-Bold.ttf"; sourceTree = "<group>"; };
		27A81E9B2B4304B5007DD04D /* 网格交易工具箱.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "网格交易工具箱.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		27A81E9E2B4304B5007DD04D /* GridTradingToolboxApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GridTradingToolboxApp.swift; sourceTree = "<group>"; };
		27A81EA42B4304B6007DD04D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		27A81EA72B4304B6007DD04D /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		27A81EA92B4304B6007DD04D /* GridTradingToolbox.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = GridTradingToolbox.entitlements; sourceTree = "<group>"; };
		27A81EAA2B4304B6007DD04D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		27A81EAF2B4304B6007DD04D /* GridTradingToolboxTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = GridTradingToolboxTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		27A81EB32B4304B6007DD04D /* GridTradingToolboxTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GridTradingToolboxTests.swift; sourceTree = "<group>"; };
		27A81EB92B4304B6007DD04D /* GridTradingToolboxUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = GridTradingToolboxUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		27A81EBD2B4304B6007DD04D /* GridTradingToolboxUITests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GridTradingToolboxUITests.swift; sourceTree = "<group>"; };
		27A81EBF2B4304B6007DD04D /* GridTradingToolboxUITestsLaunchTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GridTradingToolboxUITestsLaunchTests.swift; sourceTree = "<group>"; };
		27A81ECC2B4436DE007DD04D /* HomeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeView.swift; sourceTree = "<group>"; };
		27A81ECE2B44411C007DD04D /* CreateStrategyView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CreateStrategyView.swift; sourceTree = "<group>"; };
		27A81ED02B444EF8007DD04D /* Strategy.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Strategy.swift; sourceTree = "<group>"; };
		27A81ED32B482E9D007DD04D /* StocksSearchView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StocksSearchView.swift; sourceTree = "<group>"; };
		27A81ED62B483F64007DD04D /* Stock.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Stock.swift; sourceTree = "<group>"; };
		27A81EE12B4A94E7007DD04D /* TradeGrid.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TradeGrid.swift; sourceTree = "<group>"; };
		27A81EE52B539EDD007DD04D /* StressTestView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StressTestView.swift; sourceTree = "<group>"; };
		27A81EE72B541B48007DD04D /* StrategyView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StrategyView.swift; sourceTree = "<group>"; };
		27A81EE92B56B3E0007DD04D /* TradeLog.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TradeLog.swift; sourceTree = "<group>"; };
		27A81EEB2B56C7FB007DD04D /* BuyGridView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BuyGridView.swift; sourceTree = "<group>"; };
		27A81EED2B5B88D5007DD04D /* PriceChartView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PriceChartView.swift; sourceTree = "<group>"; };
		27B395772B9AF954003BE784 /* AppInfoView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppInfoView.swift; sourceTree = "<group>"; };
		27B3957D2BA30407003BE784 /* icon.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon.png; sourceTree = "<group>"; };
		27B841DE2B80F2C10096972A /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		27B841E02B80FF650096972A /* ProMemberView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProMemberView.swift; sourceTree = "<group>"; };
		27B841E22B822D680096972A /* Store.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Store.swift; sourceTree = "<group>"; };
		27CE99F62BD3C63B00E0D823 /* StockPriceViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StockPriceViewModel.swift; sourceTree = "<group>"; };
		27CFBB842D37E41C00A2928F /* SummaryCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SummaryCardView.swift; sourceTree = "<group>"; };
		27D6277D2BB6AFD4006E8F71 /* SellRetainShareView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SellRetainShareView.swift; sourceTree = "<group>"; };
		27D6277F2BB6B815006E8F71 /* StrategySummaryPostView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StrategySummaryPostView.swift; sourceTree = "<group>"; };
		27D627812BB92A08006E8F71 /* FlexibleTradeLog.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FlexibleTradeLog.swift; sourceTree = "<group>"; };
		27D627832BB98A5A006E8F71 /* StrategyStatisticsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StrategyStatisticsView.swift; sourceTree = "<group>"; };
		27D627852BBA63E2006E8F71 /* RetainTradeHistoryView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RetainTradeHistoryView.swift; sourceTree = "<group>"; };
		27D627872BBC16DC006E8F71 /* MoreView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MoreView.swift; sourceTree = "<group>"; };
		27E316922B6A85AE00456BD7 /* SharePostView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SharePostView.swift; sourceTree = "<group>"; };
		27E316942B6E1E5F00456BD7 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		27E4D0202CEC4B53004B8872 /* DataHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataHandler.swift; sourceTree = "<group>"; };
		27E4D0242CEC518F004B8872 /* DataProvider.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataProvider.swift; sourceTree = "<group>"; };
		27FDE6AF2BBD421800083BDF /* UserGuideView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserGuideView.swift; sourceTree = "<group>"; };
		27FDE6B52BC1419100083BDF /* SearchStockViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchStockViewModel.swift; sourceTree = "<group>"; };
		27FF3D822D72F2EA00EDDFAB /* VersionSchema.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VersionSchema.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		27A81E982B4304B5007DD04D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				27C706C62C806E5F0027ED73 /* Alamofire in Frameworks */,
				27B841DF2B80F2C10096972A /* StoreKit.framework in Frameworks */,
				27E316912B6A82BF00456BD7 /* PopupView in Frameworks */,
				2754C8552C47B57B00C9D1A8 /* MijickCalendarView in Frameworks */,
				278F5E8D2B5D055200BCD3D0 /* ConfettiSwiftUI in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		27A81EAC2B4304B6007DD04D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		27A81EB62B4304B6007DD04D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		271062272C5D07400067078D /* FeatureUpdate */ = {
			isa = PBXGroup;
			children = (
				271062282C5D07890067078D /* FeatureUpdateView.swift */,
			);
			path = FeatureUpdate;
			sourceTree = "<group>";
		};
		2750F3782BEF900D007AB780 /* StockAccount */ = {
			isa = PBXGroup;
			children = (
				2750F3762BEF6A2C007AB780 /* StockAccountView.swift */,
				2750F3792BEF9038007AB780 /* CreateStockAccountView.swift */,
			);
			path = StockAccount;
			sourceTree = "<group>";
		};
		2750F37B2BF771F1007AB780 /* Strategy */ = {
			isa = PBXGroup;
			children = (
				27A81EE72B541B48007DD04D /* StrategyView.swift */,
				2750F37C2BF772FD007AB780 /* ArchiveStrategyListView.swift */,
				279728442D33A13A006665D8 /* StrategySummaryCardView.swift */,
			);
			path = Strategy;
			sourceTree = "<group>";
		};
		275643E42CCFB27300AA1D2A /* RetainShare */ = {
			isa = PBXGroup;
			children = (
				27D6277D2BB6AFD4006E8F71 /* SellRetainShareView.swift */,
				275643E52CCFB2A600AA1D2A /* EditSellRetainShareView.swift */,
			);
			path = RetainShare;
			sourceTree = "<group>";
		};
		275643E72CD79DF700AA1D2A /* Index */ = {
			isa = PBXGroup;
			children = (
				27538DBD2D9B891B00AB7332 /* IndexFilterView.swift */,
				27538DB52D8FBE9500AB7332 /* IndexHelpView.swift */,
				275643E82CD79E1500AA1D2A /* IndexView.swift */,
				275643EC2CDB752700AA1D2A /* IndexSearchView.swift */,
			);
			path = Index;
			sourceTree = "<group>";
		};
		275EA1AF2D28241100FA8A08 /* ICloud */ = {
			isa = PBXGroup;
			children = (
				275EA1B02D28245700FA8A08 /* ICloudSyncStatusView.swift */,
			);
			path = ICloud;
			sourceTree = "<group>";
		};
		278F5E922B652CA800BCD3D0 /* viewModels */ = {
			isa = PBXGroup;
			children = (
				27538DBB2D9B890D00AB7332 /* IndexFilterViewModel.swift */,
				278F5E932B652CD200BCD3D0 /* Settings.swift */,
				27B841E22B822D680096972A /* Store.swift */,
				27FDE6B52BC1419100083BDF /* SearchStockViewModel.swift */,
				275AD13E2BC3E66C00C3FEDE /* StocksPriceViewModel.swift */,
				27CE99F62BD3C63B00E0D823 /* StockPriceViewModel.swift */,
				2710622D2C6A44970067078D /* SessionManager.swift */,
				275643EA2CD7A48700AA1D2A /* IndexViewModel.swift */,
				275643EE2CDB780D00AA1D2A /* IndexSearchViewModel.swift */,
			);
			path = viewModels;
			sourceTree = "<group>";
		};
		279728462D3520B9006665D8 /* font */ = {
			isa = PBXGroup;
			children = (
				2797284D2D35272A006665D8 /* D-DIN-Bold.ttf */,
				279728492D35224F006665D8 /* D-DIN.ttf */,
			);
			path = font;
			sourceTree = "<group>";
		};
		27A81E922B4304B4007DD04D = {
			isa = PBXGroup;
			children = (
				27A81E9D2B4304B5007DD04D /* GridTradingToolbox */,
				27A81EB22B4304B6007DD04D /* GridTradingToolboxTests */,
				27A81EBC2B4304B6007DD04D /* GridTradingToolboxUITests */,
				27A81E9C2B4304B5007DD04D /* Products */,
				27B841DD2B80F2C10096972A /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		27A81E9C2B4304B5007DD04D /* Products */ = {
			isa = PBXGroup;
			children = (
				27A81E9B2B4304B5007DD04D /* 网格交易工具箱.app */,
				27A81EAF2B4304B6007DD04D /* GridTradingToolboxTests.xctest */,
				27A81EB92B4304B6007DD04D /* GridTradingToolboxUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		27A81E9D2B4304B5007DD04D /* GridTradingToolbox */ = {
			isa = PBXGroup;
			children = (
				279728462D3520B9006665D8 /* font */,
				27B3957D2BA30407003BE784 /* icon.png */,
				278F5E922B652CA800BCD3D0 /* viewModels */,
				27A81ED52B483E2D007DD04D /* models */,
				27A81ED22B482DD1007DD04D /* views */,
				27A81E9E2B4304B5007DD04D /* GridTradingToolboxApp.swift */,
				27A81EA42B4304B6007DD04D /* Assets.xcassets */,
				27A81EA92B4304B6007DD04D /* GridTradingToolbox.entitlements */,
				27A81EAA2B4304B6007DD04D /* Info.plist */,
				27A81EA62B4304B6007DD04D /* Preview Content */,
				278F5E902B64AFDA00BCD3D0 /* Utils.swift */,
				27E316942B6E1E5F00456BD7 /* AppDelegate.swift */,
				2741AFA12B89BD40001774A7 /* Launch Screen.storyboard */,
				27340B432B94354700709051 /* Extensions.swift */,
			);
			path = GridTradingToolbox;
			sourceTree = "<group>";
		};
		27A81EA62B4304B6007DD04D /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				27A81EA72B4304B6007DD04D /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		27A81EB22B4304B6007DD04D /* GridTradingToolboxTests */ = {
			isa = PBXGroup;
			children = (
				27A81EB32B4304B6007DD04D /* GridTradingToolboxTests.swift */,
			);
			path = GridTradingToolboxTests;
			sourceTree = "<group>";
		};
		27A81EBC2B4304B6007DD04D /* GridTradingToolboxUITests */ = {
			isa = PBXGroup;
			children = (
				27A81EBD2B4304B6007DD04D /* GridTradingToolboxUITests.swift */,
				27A81EBF2B4304B6007DD04D /* GridTradingToolboxUITestsLaunchTests.swift */,
			);
			path = GridTradingToolboxUITests;
			sourceTree = "<group>";
		};
		27A81ED22B482DD1007DD04D /* views */ = {
			isa = PBXGroup;
			children = (
				27CFBB832D37E3C200A2928F /* Home */,
				275EA1AF2D28241100FA8A08 /* ICloud */,
				275643E72CD79DF700AA1D2A /* Index */,
				275643E42CCFB27300AA1D2A /* RetainShare */,
				271062272C5D07400067078D /* FeatureUpdate */,
				27C3DAFD2C47AF3C001BB0B8 /* GridCalendar */,
				2750F37B2BF771F1007AB780 /* Strategy */,
				2750F3782BEF900D007AB780 /* StockAccount */,
				27A81ECE2B44411C007DD04D /* CreateStrategyView.swift */,
				27A81ED32B482E9D007DD04D /* StocksSearchView.swift */,
				27A81EE52B539EDD007DD04D /* StressTestView.swift */,
				27A81EEB2B56C7FB007DD04D /* BuyGridView.swift */,
				27A81EED2B5B88D5007DD04D /* PriceChartView.swift */,
				278F5E7D2B5CD72F00BCD3D0 /* SellGridView.swift */,
				278F5E8E2B5FE17300BCD3D0 /* TradeHistoryView.swift */,
				278F5E972B6534C200BCD3D0 /* LandingView.swift */,
				278F5E9B2B66567800BCD3D0 /* ImportDataView.swift */,
				27E316922B6A85AE00456BD7 /* SharePostView.swift */,
				27B841E02B80FF650096972A /* ProMemberView.swift */,
				27B395772B9AF954003BE784 /* AppInfoView.swift */,
				27D6277F2BB6B815006E8F71 /* StrategySummaryPostView.swift */,
				27D627832BB98A5A006E8F71 /* StrategyStatisticsView.swift */,
				27D627852BBA63E2006E8F71 /* RetainTradeHistoryView.swift */,
				27D627872BBC16DC006E8F71 /* MoreView.swift */,
				27FDE6AF2BBD421800083BDF /* UserGuideView.swift */,
				275AD1402BC3EA8000C3FEDE /* TriggerGridCardView.swift */,
				275AD1422BC6D7F500C3FEDE /* RealtimeGridPriceView.swift */,
			);
			path = views;
			sourceTree = "<group>";
		};
		27A81ED52B483E2D007DD04D /* models */ = {
			isa = PBXGroup;
			children = (
				27538DBF2D9B894000AB7332 /* FilteredIndex.swift */,
				27FF3D812D72F2BD00EDDFAB /* migration */,
				27A81ED02B444EF8007DD04D /* Strategy.swift */,
				27A81ED62B483F64007DD04D /* Stock.swift */,
				27A81EE12B4A94E7007DD04D /* TradeGrid.swift */,
				27A81EE92B56B3E0007DD04D /* TradeLog.swift */,
				278F5E9D2B67CB9F00BCD3D0 /* Response.swift */,
				27D627812BB92A08006E8F71 /* FlexibleTradeLog.swift */,
				2750F3742BEF1B6C007AB780 /* StockAccount.swift */,
				27E4D0202CEC4B53004B8872 /* DataHandler.swift */,
				27E4D0242CEC518F004B8872 /* DataProvider.swift */,
			);
			path = models;
			sourceTree = "<group>";
		};
		27B841DD2B80F2C10096972A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				27B841DE2B80F2C10096972A /* StoreKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		27C3DAFD2C47AF3C001BB0B8 /* GridCalendar */ = {
			isa = PBXGroup;
			children = (
				2754C8512C47AFFE00C9D1A8 /* GridCalendarView.swift */,
			);
			path = GridCalendar;
			sourceTree = "<group>";
		};
		27CFBB832D37E3C200A2928F /* Home */ = {
			isa = PBXGroup;
			children = (
				27A81ECC2B4436DE007DD04D /* HomeView.swift */,
				27CFBB842D37E41C00A2928F /* SummaryCardView.swift */,
			);
			path = Home;
			sourceTree = "<group>";
		};
		27FF3D812D72F2BD00EDDFAB /* migration */ = {
			isa = PBXGroup;
			children = (
				27FF3D822D72F2EA00EDDFAB /* VersionSchema.swift */,
			);
			path = migration;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		27A81E9A2B4304B5007DD04D /* 网格交易工具箱 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 27A81EC32B4304B6007DD04D /* Build configuration list for PBXNativeTarget "网格交易工具箱" */;
			buildPhases = (
				27A81E972B4304B5007DD04D /* Sources */,
				27A81E982B4304B5007DD04D /* Frameworks */,
				27A81E992B4304B5007DD04D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "网格交易工具箱";
			packageProductDependencies = (
				278F5E8C2B5D055200BCD3D0 /* ConfettiSwiftUI */,
				27E316902B6A82BF00456BD7 /* PopupView */,
				2754C8542C47B57B00C9D1A8 /* MijickCalendarView */,
				27C706C52C806E5F0027ED73 /* Alamofire */,
			);
			productName = GridTradingToolbox;
			productReference = 27A81E9B2B4304B5007DD04D /* 网格交易工具箱.app */;
			productType = "com.apple.product-type.application";
		};
		27A81EAE2B4304B6007DD04D /* GridTradingToolboxTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 27A81EC62B4304B6007DD04D /* Build configuration list for PBXNativeTarget "GridTradingToolboxTests" */;
			buildPhases = (
				27A81EAB2B4304B6007DD04D /* Sources */,
				27A81EAC2B4304B6007DD04D /* Frameworks */,
				27A81EAD2B4304B6007DD04D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				27A81EB12B4304B6007DD04D /* PBXTargetDependency */,
			);
			name = GridTradingToolboxTests;
			productName = GridTradingToolboxTests;
			productReference = 27A81EAF2B4304B6007DD04D /* GridTradingToolboxTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		27A81EB82B4304B6007DD04D /* GridTradingToolboxUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 27A81EC92B4304B6007DD04D /* Build configuration list for PBXNativeTarget "GridTradingToolboxUITests" */;
			buildPhases = (
				27A81EB52B4304B6007DD04D /* Sources */,
				27A81EB62B4304B6007DD04D /* Frameworks */,
				27A81EB72B4304B6007DD04D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				27A81EBB2B4304B6007DD04D /* PBXTargetDependency */,
			);
			name = GridTradingToolboxUITests;
			productName = GridTradingToolboxUITests;
			productReference = 27A81EB92B4304B6007DD04D /* GridTradingToolboxUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		27A81E932B4304B5007DD04D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					27A81E9A2B4304B5007DD04D = {
						CreatedOnToolsVersion = 15.0.1;
					};
					27A81EAE2B4304B6007DD04D = {
						CreatedOnToolsVersion = 15.0.1;
						TestTargetID = 27A81E9A2B4304B5007DD04D;
					};
					27A81EB82B4304B6007DD04D = {
						CreatedOnToolsVersion = 15.0.1;
						TestTargetID = 27A81E9A2B4304B5007DD04D;
					};
				};
			};
			buildConfigurationList = 27A81E962B4304B5007DD04D /* Build configuration list for PBXProject "GridTradingToolbox" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = "zh-Hans";
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				"zh-Hans",
			);
			mainGroup = 27A81E922B4304B4007DD04D;
			packageReferences = (
				278F5E8B2B5D055100BCD3D0 /* XCLocalSwiftPackageReference "../../ConfettiSwiftUI" */,
				27E3168F2B6A82BF00456BD7 /* XCRemoteSwiftPackageReference "PopupView" */,
				2754C8532C47B0BB00C9D1A8 /* XCRemoteSwiftPackageReference "CalendarView" */,
				2710622C2C6A3D220067078D /* XCRemoteSwiftPackageReference "Alamofire" */,
			);
			productRefGroup = 27A81E9C2B4304B5007DD04D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				27A81E9A2B4304B5007DD04D /* 网格交易工具箱 */,
				27A81EAE2B4304B6007DD04D /* GridTradingToolboxTests */,
				27A81EB82B4304B6007DD04D /* GridTradingToolboxUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		27A81E992B4304B5007DD04D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				27A81EA82B4304B6007DD04D /* Preview Assets.xcassets in Resources */,
				2797284A2D35224F006665D8 /* D-DIN.ttf in Resources */,
				2797284E2D35272A006665D8 /* D-DIN-Bold.ttf in Resources */,
				27B3957E2BA30407003BE784 /* icon.png in Resources */,
				27A81EA52B4304B6007DD04D /* Assets.xcassets in Resources */,
				2741AFA22B89BD40001774A7 /* Launch Screen.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		27A81EAD2B4304B6007DD04D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		27A81EB72B4304B6007DD04D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		27A81E972B4304B5007DD04D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				2750F3752BEF1B6C007AB780 /* StockAccount.swift in Sources */,
				27E316952B6E1E5F00456BD7 /* AppDelegate.swift in Sources */,
				27538DB62D8FBE9500AB7332 /* IndexHelpView.swift in Sources */,
				275AD1432BC6D7F500C3FEDE /* RealtimeGridPriceView.swift in Sources */,
				27B841E12B80FF650096972A /* ProMemberView.swift in Sources */,
				27A81EEC2B56C7FB007DD04D /* BuyGridView.swift in Sources */,
				27D6277E2BB6AFD4006E8F71 /* SellRetainShareView.swift in Sources */,
				27A81ECF2B44411C007DD04D /* CreateStrategyView.swift in Sources */,
				27A81ED72B483F64007DD04D /* Stock.swift in Sources */,
				27A81EEA2B56B3E0007DD04D /* TradeLog.swift in Sources */,
				27B841E32B822D680096972A /* Store.swift in Sources */,
				27A81EEE2B5B88D5007DD04D /* PriceChartView.swift in Sources */,
				27FDE6B62BC1419100083BDF /* SearchStockViewModel.swift in Sources */,
				278F5E942B652CD200BCD3D0 /* Settings.swift in Sources */,
				27B395782B9AF954003BE784 /* AppInfoView.swift in Sources */,
				27A81EE82B541B48007DD04D /* StrategyView.swift in Sources */,
				27A81ED42B482E9D007DD04D /* StocksSearchView.swift in Sources */,
				275643E62CCFB2A600AA1D2A /* EditSellRetainShareView.swift in Sources */,
				27CFBB852D37E41C00A2928F /* SummaryCardView.swift in Sources */,
				278F5E7E2B5CD72F00BCD3D0 /* SellGridView.swift in Sources */,
				278F5E912B64AFDA00BCD3D0 /* Utils.swift in Sources */,
				275643EF2CDB780D00AA1D2A /* IndexSearchViewModel.swift in Sources */,
				27FF3D832D72F2EA00EDDFAB /* VersionSchema.swift in Sources */,
				27A81ED12B444EF8007DD04D /* Strategy.swift in Sources */,
				275643EB2CD7A48700AA1D2A /* IndexViewModel.swift in Sources */,
				27A81ECD2B4436DE007DD04D /* HomeView.swift in Sources */,
				278F5E9C2B66567800BCD3D0 /* ImportDataView.swift in Sources */,
				279728452D33A13A006665D8 /* StrategySummaryCardView.swift in Sources */,
				27E4D0212CEC4B53004B8872 /* DataHandler.swift in Sources */,
				27E316932B6A85AE00456BD7 /* SharePostView.swift in Sources */,
				2750F3772BEF6A2C007AB780 /* StockAccountView.swift in Sources */,
				275643ED2CDB752700AA1D2A /* IndexSearchView.swift in Sources */,
				27CE99F72BD3C63B00E0D823 /* StockPriceViewModel.swift in Sources */,
				27A81E9F2B4304B5007DD04D /* GridTradingToolboxApp.swift in Sources */,
				27E4D0252CEC518F004B8872 /* DataProvider.swift in Sources */,
				2754C8522C47AFFE00C9D1A8 /* GridCalendarView.swift in Sources */,
				278F5E982B6534C200BCD3D0 /* LandingView.swift in Sources */,
				275EA1B12D28245700FA8A08 /* ICloudSyncStatusView.swift in Sources */,
				271062292C5D07890067078D /* FeatureUpdateView.swift in Sources */,
				27538DBC2D9B890D00AB7332 /* IndexFilterViewModel.swift in Sources */,
				2710622E2C6A44970067078D /* SessionManager.swift in Sources */,
				27D627882BBC16DC006E8F71 /* MoreView.swift in Sources */,
				2750F37D2BF772FD007AB780 /* ArchiveStrategyListView.swift in Sources */,
				27538DBE2D9B891B00AB7332 /* IndexFilterView.swift in Sources */,
				27FDE6B02BBD421800083BDF /* UserGuideView.swift in Sources */,
				275643E92CD79E1500AA1D2A /* IndexView.swift in Sources */,
				275AD1412BC3EA8000C3FEDE /* TriggerGridCardView.swift in Sources */,
				27D627862BBA63E2006E8F71 /* RetainTradeHistoryView.swift in Sources */,
				275AD13F2BC3E66C00C3FEDE /* StocksPriceViewModel.swift in Sources */,
				27340B442B94354700709051 /* Extensions.swift in Sources */,
				27D627842BB98A5A006E8F71 /* StrategyStatisticsView.swift in Sources */,
				27D627822BB92A08006E8F71 /* FlexibleTradeLog.swift in Sources */,
				278F5E9E2B67CB9F00BCD3D0 /* Response.swift in Sources */,
				27D627802BB6B815006E8F71 /* StrategySummaryPostView.swift in Sources */,
				27A81EE22B4A94E7007DD04D /* TradeGrid.swift in Sources */,
				278F5E8F2B5FE17300BCD3D0 /* TradeHistoryView.swift in Sources */,
				27A81EE62B539EDD007DD04D /* StressTestView.swift in Sources */,
				27538DC02D9B894000AB7332 /* FilteredIndex.swift in Sources */,
				2750F37A2BEF9038007AB780 /* CreateStockAccountView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		27A81EAB2B4304B6007DD04D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				27A81EB42B4304B6007DD04D /* GridTradingToolboxTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		27A81EB52B4304B6007DD04D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				27A81EC02B4304B6007DD04D /* GridTradingToolboxUITestsLaunchTests.swift in Sources */,
				27A81EBE2B4304B6007DD04D /* GridTradingToolboxUITests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		27A81EB12B4304B6007DD04D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 27A81E9A2B4304B5007DD04D /* 网格交易工具箱 */;
			targetProxy = 27A81EB02B4304B6007DD04D /* PBXContainerItemProxy */;
		};
		27A81EBB2B4304B6007DD04D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 27A81E9A2B4304B5007DD04D /* 网格交易工具箱 */;
			targetProxy = 27A81EBA2B4304B6007DD04D /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		27A81EC12B4304B6007DD04D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		27A81EC22B4304B6007DD04D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		27A81EC42B4304B6007DD04D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = GridTradingToolbox/GridTradingToolbox.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"GridTradingToolbox/Preview Content\"";
				DEVELOPMENT_TEAM = GXMXAZ8HKU;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = GridTradingToolbox/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = "Launch Screen.storyboard";
				INFOPLIST_KEY_UIStatusBarStyle = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UIUserInterfaceStyle = Light;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.37;
				PRODUCT_BUNDLE_IDENTIFIER = com.jay.chen.GridTradingToolbox;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		27A81EC52B4304B6007DD04D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = GridTradingToolbox/GridTradingToolbox.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"GridTradingToolbox/Preview Content\"";
				DEVELOPMENT_TEAM = GXMXAZ8HKU;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = GridTradingToolbox/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = "Launch Screen.storyboard";
				INFOPLIST_KEY_UIStatusBarStyle = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UIUserInterfaceStyle = Light;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.37;
				PRODUCT_BUNDLE_IDENTIFIER = com.jay.chen.GridTradingToolbox;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		27A81EC72B4304B6007DD04D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = jay.chen.GridTradingToolboxTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/GridTradingToolbox.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/GridTradingToolbox";
			};
			name = Debug;
		};
		27A81EC82B4304B6007DD04D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = jay.chen.GridTradingToolboxTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/GridTradingToolbox.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/GridTradingToolbox";
			};
			name = Release;
		};
		27A81ECA2B4304B6007DD04D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = jay.chen.GridTradingToolboxUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = GridTradingToolbox;
			};
			name = Debug;
		};
		27A81ECB2B4304B6007DD04D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = jay.chen.GridTradingToolboxUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = GridTradingToolbox;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		27A81E962B4304B5007DD04D /* Build configuration list for PBXProject "GridTradingToolbox" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				27A81EC12B4304B6007DD04D /* Debug */,
				27A81EC22B4304B6007DD04D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		27A81EC32B4304B6007DD04D /* Build configuration list for PBXNativeTarget "网格交易工具箱" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				27A81EC42B4304B6007DD04D /* Debug */,
				27A81EC52B4304B6007DD04D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		27A81EC62B4304B6007DD04D /* Build configuration list for PBXNativeTarget "GridTradingToolboxTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				27A81EC72B4304B6007DD04D /* Debug */,
				27A81EC82B4304B6007DD04D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		27A81EC92B4304B6007DD04D /* Build configuration list for PBXNativeTarget "GridTradingToolboxUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				27A81ECA2B4304B6007DD04D /* Debug */,
				27A81ECB2B4304B6007DD04D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCLocalSwiftPackageReference section */
		278F5E8B2B5D055100BCD3D0 /* XCLocalSwiftPackageReference "../../ConfettiSwiftUI" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = ../../ConfettiSwiftUI;
		};
/* End XCLocalSwiftPackageReference section */

/* Begin XCRemoteSwiftPackageReference section */
		2710622C2C6A3D220067078D /* XCRemoteSwiftPackageReference "Alamofire" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Alamofire/Alamofire.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.9.1;
			};
		};
		2754C8532C47B0BB00C9D1A8 /* XCRemoteSwiftPackageReference "CalendarView" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Mijick/CalendarView.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.0.0;
			};
		};
		27E3168F2B6A82BF00456BD7 /* XCRemoteSwiftPackageReference "PopupView" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/exyte/PopupView.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.8.4;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		2754C8542C47B57B00C9D1A8 /* MijickCalendarView */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2754C8532C47B0BB00C9D1A8 /* XCRemoteSwiftPackageReference "CalendarView" */;
			productName = MijickCalendarView;
		};
		278F5E8C2B5D055200BCD3D0 /* ConfettiSwiftUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = ConfettiSwiftUI;
		};
		27C706C52C806E5F0027ED73 /* Alamofire */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2710622C2C6A3D220067078D /* XCRemoteSwiftPackageReference "Alamofire" */;
			productName = Alamofire;
		};
		27E316902B6A82BF00456BD7 /* PopupView */ = {
			isa = XCSwiftPackageProductDependency;
			package = 27E3168F2B6A82BF00456BD7 /* XCRemoteSwiftPackageReference "PopupView" */;
			productName = PopupView;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 27A81E932B4304B5007DD04D /* Project object */;
}
