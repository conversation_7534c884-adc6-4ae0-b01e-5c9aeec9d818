# Grid Trading Toolbox

Grid Trading Toolbox 是一个专业的股票网格交易策略管理工具，基于 SwiftUI 开发，帮助投资者更好地执行和管理网格交易策略。

## 主要功能

- 🎯 网格交易策略
  - 创建和管理多个网格交易策略
  - 支持买入和卖出网格设置
  - 实时价格监控和网格触发提醒
  - 网格交易日历视图

- 📊 数据分析
  - 策略统计分析
  - 交易历史记录
  - 收益统计和可视化
  - 策略压力测试

- 📱 用户体验
  - 直观的网格价格展示
  - 交易记录导入导出
  - 策略分享功能
  - iCloud 数据同步

## 技术栈

- Swift 5
- SwiftUI
- Core Data
- CloudKit
- Charts Framework

## 项目结构

```
GridTradingToolbox/
├── models/          # 数据模型
├── viewModels/      # 视图模型
├── views/           # UI 视图
├── dependency/      # 第三方依赖
├── font/           # 字体资源
└── Assets.xcassets/ # 资源文件
```

## 系统要求

- iOS 14.0+
- Xcode 12.0+
- macOS 11.0+ (开发环境)

## 开发设置

1. 克隆项目
```bash
git clone [repository-url]
cd GridTradingToolbox
```

2. 使用 Xcode 打开项目
```bash
open GridTradingToolbox.xcodeproj
```

3. 构建和运行
- 选择目标设备或模拟器
- 点击运行按钮或使用 `Cmd + R`

## 主要特性说明

### 网格交易策略管理

- 支持多股票多策略管理
- 灵活的网格区间设置
- 自动计算网格价格和数量
- 实时监控网格触发情况

### 数据分析与统计

- 策略执行效果分析
- 历史交易记录查询
- 收益率计算和展示
- 策略回测与压力测试

### 数据同步

- iCloud 自动同步
- 本地数据备份
- 多设备数据互通
- 安全的数据存储

## 贡献指南

欢迎提交 Issues 和 Pull Requests 来帮助改进项目。在提交 PR 之前，请确保：

1. 代码符合项目的编码规范
2. 更新了相关的文档
3. 添加了必要的测试
4. 通过了所有现有测试

## License

Copyright © 2024 GridTradingToolbox. All rights reserved.