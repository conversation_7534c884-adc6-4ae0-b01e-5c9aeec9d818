# 网格交易工具箱数据模型文档

## 数据模型概览

本文档详细说明了网格交易工具箱的核心数据模型结构和关系。系统主要包含以下几个核心模型：

```mermaid
classDiagram
    Strategy "1" -- "*" TradeGrid : contains
    TradeGrid "1" -- "*" TradeLog : contains
    Strategy "1" -- "*" FlexibleTradeLog : contains
```

## 核心模型说明

### Strategy (策略)

策略是整个系统的核心模型，代表一个完整的网格交易策略。

#### 所有属性
- `id: UUID` - 策略唯一标识
- `name: String` - 策略名称
- `code: String?` - 股票代码
- `interval: Int` - 网格间距(小网格)
- `targetPrice: Int` - 触发价格
- `amount: Int` - 每格投资金额
- `maxFall: Int` - 最大跌幅
- `buyStrategy: Int` - 买入策略
- `incrementalBuyRatio: Int?` - 递增买入比例
- `fixedShares: Int?` - 买入策略-固定股数
- `mediumLargeSwitch: Bool` - 是否启用中大网格
- `mediumInterval: Int?` - 中网格间距
- `largeInterval: Int?` - 大网格间距
- `triggerNotify: Bool` - 是否开启触发通知
- `calculateInterest: Bool` - 是否开启底仓计息
- `interestPeriod: Int?` - 计息周期
- `interest: Int?` - 利息比例
- `retainProfitSwitch: Bool` - 是否开启保留利润
- `retainProfit: Int?` - 保留利润倍数
- `status: Int?` - 策略状态
- `grids: [TradeGrid]?` - 关联的网格
- `flexibleTradeLog: [FlexibleTradeLog]?` - 关联的灵活交易记录
- `nextSellPrice: Int?` - 下一个卖出价格（字段已废弃）
- `nextBuyPrice: Int?` - 下一个买入价格（字段已废弃）
- `totalInvestmentAmount: Int?` - 策略当前总投入金额
- `totalProfitAmount: Int?` - 总盈利金额（使用买入股数计算卖出利润）
- `realTotalProfitAmount: Int?` - 实际总盈利金额（使用实际买卖股数计算，不包含预留利润股数）
- `retainShares: Int?` - 持有保留份额
- `createAt: Date` - 创建时间
- `updateAt: Date` - 更新时间
- `profit: Int?` - 策略利润
- `mediumGridEnabled: Bool?` - 是否启用中网格（暂未启用，后续功能预留）
- `mediumRiseInterval: Int?` - 中网格上涨间距（暂未启用，后续功能预留）
- `mediumFallInterval: Int?` - 中网格下跌间距（暂未启用，后续功能预留）
- `largeGridEnabled: Bool?` - 是否启用大网格（暂未启用，后续功能预留）
- `largeRiseInterval: Int?` - 大网格上涨间距（暂未启用，后续功能预留）
- `largeFallInterval: Int?` - 大网格下跌间距（暂未启用，后续功能预留）
- `gridType: Int` - 网格类型(等差/等比)（暂未启用，后续功能预留）

#### 重要枚举值
```swift
// 网格类型
enum GridType: Int {
    case arithmetic = 1  // 等差
    case geometric      // 等比
}

// 买入策略
enum BuyStrategy: Int {
    case fixedShares = 1      // 等额
    case arithmetic    // 递增
    case fixedShares    // 固定股数
}

// 计息周期
enum InterestPeriod: Int {
    case month = 1     // 月
    case year          // 年
}

// 策略状态
enum StrategyStatus: Int {
    case active = 1    // 激活
    case archive       // 归档
}
```

### TradeGrid (交易网格)

代表策略中的一个网格，记录该网格的买卖价格、持仓等信息。

#### 所有属性
- `id: UUID` - 网格唯一标识
- `strategy: Strategy?` - 关联的策略
- `gridType: Int` - 网格类型
- `grade: Int` - 网格等级
- `holdShares: Int` - 持仓数量
- `theoreticalBuyPrice: Int` - 理论买入价格
- `theoreticalBuyShares: Int` - 理论买入数量
- `theoreticalSellPrice: Int` - 理论卖出价格
- `theoreticalSellShares: Int` - 理论卖出数量
- `triggerAmount: Int` - 触发金额
- `status: Int?` - 状态
- `tradeLogs: [TradeLog]?` - 交易记录
- `notificationId: String?` - 通知ID
- `accountId: String?` - 账户ID
- `createAt: Date` - 创建时间
- `updateAt: Date` - 更新时间

#### 重要枚举值
```swift
enum GridType: Int {
    case small = 0   // 小网格
    case medium      // 中网格
    case large       // 大网格
}
```

### TradeLog (交易记录)

记录每次网格交易的具体信息。

#### 所有属性
- `id: UUID` - 交易记录唯一标识
- `grid: TradeGrid?` - 关联的网格
- `tradeType: Int` - 交易类型
- `tradeShares: Int` - 交易股数
- `tradePrice: Int` - 交易价格
- `tradeAt: Date` - 交易时间
- `createAt: Date` - 创建时间
- `mood: String?` - 交易心情
- `note: String?` - 交易备注

#### 重要枚举值
```swift
enum TradeType: Int {
    case buy = 0      // 买入
    case sell         // 卖出
    case sellRetain   // 卖出保留份额
}
```

### FlexibleTradeLog (灵活交易记录)

记录策略中非网格交易的记录，主要用于记录保留份额的交易。

#### 所有属性
- `id: UUID` - 交易记录唯一标识
- `strategy: Strategy?` - 关联的策略
- `tradeType: Int` - 交易类型
- `tradeShares: Int` - 交易股数
- `tradePrice: Int` - 交易价格
- `tradeAt: Date` - 交易时间
- `createAt: Date` - 创建时间

#### 重要枚举值
```swift
enum TradeType: Int {
    case buy = 0    // 买入
    case sell       // 卖出
}
```

### StockAccount (股票账户)

记录交易账户信息。

#### 所有属性
- `id: String` - 账户唯一标识
- `name: String` - 账户名称
- `transactionRate: Int` - 交易费率
- `minFee: Int` - 最小手续费
- `createAt: Date` - 创建时间
- `updateAt: Date` - 更新时间

## 核心业务流程

### 1. 创建策略流程

1. 用户填写策略基本信息：
   - 策略名称
   - 目标价格
   - 网格间距
   - 每格投资金额
   - 最大跌幅
   - 买入策略(等额/递增/固定股数)
   - 是否启用中大网格等配置

2. 系统校验策略参数合法性：
   - 检查必填字段
   - 校验数值范围
   - 验证配置之间的逻辑关系

3. 策略创建成功后进入激活状态(active)

### 2. 网格交易流程

1. 当价格触发网格买入价时：
   - 系统计算买入股数
   - 创建新的网格(TradeGrid)
   - 记录买入交易日志(TradeLog)
   - 更新策略总投入金额

2. 当价格触发网格卖出价时：
   - 系统计算卖出股数(考虑保留利润设置)
   - 更新网格状态
   - 记录卖出交易日志
   - 更新策略总盈利金额

### 3. 策略归档流程

当策略所有网格都完成交易且无保留股份时：
1. 状态更新为归档(archive)
2. 计算并记录最终盈利金额

## 重要计算方法

### 1. 网格等级计算
```swift
// grade = 1000 表示 100%
// 买入等级按照网格间距递减，例如：
// 间距 1% 时：1000 -> 990 -> 980 -> 970...
let nextGrade = currentGrade - interval/100
```

### 2. 网格价格计算
```swift
// 买入价格
let buyPrice = targetPrice * grade / 1000

// 卖出价格
let sellPrice = targetPrice * (grade + interval) / 1000
```

### 3. 买入数量计算

根据不同买入策略：

```swift
// 等额买入
let shares = amount / buyPrice / 100 * 100

// 递增买入
let times = (1000 - grade) / (interval/100)
let increasedAmount = amount * (100000 + times * incrementalRatio) / 100000
let shares = increasedAmount / buyPrice / 100 * 100

// 固定股数
let shares = fixedShares
```

### 4. 卖出数量计算

考虑保留利润的情况：

```swift
var sellShares = buyShares
if retainProfitSwitch {
    // 计算需要保留的股数
    let profit = (sellPrice - buyPrice) * buyShares
    let retainShares = profit / sellPrice * retainProfitRatio
    sellShares -= retainShares
}
```

## 示例

### 策略配置示例

```swift
let strategy = Strategy(
    name: "示例策略",
    code: "600519",
    interval: "1.000",          // 1%网格间距
    targetPrice: "1800.000",    // 目标价格1800元
    amount: "20000.000",        // 每格2万元
    maxFall: "20.000",         // 最大跌幅20%
    buyStrategy: "1",          // 等额买入
    mediumLargeSwitch: true,   // 启用中大网格
    mediumInterval: "3.000",   // 中网格3%间距
    largeInterval: "5.000",    // 大网格5%间距
    triggerNotify: true,       // 开启通知
    calculateInterest: false,   // 不计算利息
    retainProfitSwitch: true,  // 开启保留利润
    retainProfit: "1"     // 保留1倍利润
)
```

### 网格生成示例

以上述策略为例，当价格下跌时会生成如下网格：

```
小网格:
Grade: 100% -> 理论买入价: 1800.00 -> 理论卖出价: 1818.00
Grade: 99% -> 理论买入价: 1782.00 -> 理论卖出价: 1800.00
Grade: 98% -> 理论买入价: 1764.00 -> 理论卖出价: 1782.00
...

中网格:
Grade: 97% -> 理论买入价: 1746.00 -> 理论卖出价: 1800.00
Grade: 94% -> 理论买入价: 1692.00 -> 理论卖出价: 1746.00
...

大网格:
Grade: 95% -> 理论买入价: 1710.00 -> 理论卖出价: 1800.00
Grade: 90% -> 理论买入价: 1620.00 -> 理论卖出价: 1710.00
...
```

## 注意事项

1. 所有涉及金额的字段都以整数形式存储，实际金额需要除以1000进行显示

2. 网格等级(grade)使用整数表示百分比，1000代表100%

3. 中大网格在启用时会与小网格同时运作，可能出现重叠的情况

4. 策略归档后将无法继续进行交易操作

5. 保留利润功能会影响网格的实际卖出数量，需要注意计算

6. 计息功能会随时间推移逐步提高网格的卖出价格