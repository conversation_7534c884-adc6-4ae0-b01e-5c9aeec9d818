# 指数页面帮助说明功能实现方案

## 功能概述
在指数页面(IndexView)添加帮助按钮,点击后弹出sheet展示指标说明。

## 技术方案

### 1. 新增视图组件
创建 `IndexHelpView.swift`:
```swift
struct IndexHelpView: View {
    @Environment(\.dismiss) var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 1. 百分位说明卡片
                    // 2. 权重说明卡片
                    // 3. 颜色说明卡片
                }
                .padding()
            }
            .navigationTitle("指标说明")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") { dismiss() }
                }
            }
        }
    }
}
```

### 2. IndexView修改
在 `IndexView.swift` 中:
1. 添加状态变量: `@State private var showHelp = false`
2. 在toolbar中添加帮助按钮:
```swift
ToolbarItem(placement: .navigationBarTrailing) {
    HStack {
        Button(action: { showHelp = true }) {
            Image(systemName: "questionmark.circle")
        }
        But<PERSON>(action: { showIndexSearch = true }) {
            Image(systemName: "plus")
        }
    }
}
```
3. 添加sheet显示:
```swift
.sheet(isPresented: $showHelp) {
    IndexHelpView()
}
```

### 3. 具体UI实现
每个说明卡片的结构:
```swift
struct HelpCard: View {
    let title: String
    let content: String
    let image: String? = nil
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.headline)
            if let image = image {
                Image(image)
                    .resizable()
                    .scaledToFit()
            }
            Text(content)
                .font(.subheadline)
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }
}
```

### 4. 具体内容

#### 百分位说明
- 标题: "百分位是什么?"
- 内容: 解释五年和十年百分位的计算方法
- 示例: 使用上证50指数作为实际案例

#### 权重说明
- 标题: "不同权重类型的区别"
- 内容: 详细解释市值加权和正数等权的计算方式
- 示例: 使用具体数据展示计算过程

#### 颜色说明
- 标题: "颜色代表什么?"
- 内容: 解释不同颜色区间的含义
- 图示: 使用进度条展示不同区间

### 5. 性能优化
- 使用lazy loading加载图片
- 优化动画效果
- 缓存说明内容

### 6. 测试用例
- 验证Sheet的显示和关闭
- 检查内容的滚动是否顺畅
- 确认深色模式下的显示效果
- 测试不同设备尺寸下的适配

## 下一步行动
1. 切换到Code模式实现IndexHelpView
2. 在IndexView中添加帮助按钮
3. 实现具体的说明内容和UI组件
4. 进行测试和优化