//
//  Extensions.swift
//  GridTradingToolbox
//
//  Created by 陈敏杰 on 2024/3/3.
//

import Foundation
import SwiftUI

#if canImport(UIKit)
extension View {
    func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}
#endif

extension Date {
    func toDayString(format: String = "yyyy-M-d") -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = format
        return dateFormatter.string(from: self)
    }
}

extension Formatter {
    static var thousandSeparator: NumberFormatter {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        formatter.groupingSeparator = ","
        formatter.maximumFractionDigits = 3
        return formatter
    }
}

extension Numeric {
    var formattedWithSeparator: String {
        Formatter.thousandSeparator.string(for: self) ?? ""
    }
}
