//
//  GridTradingToolboxApp.swift
//  GridTradingToolbox
//
//  Created by 陈敏杰 on 2024/1/1.
//

import SwiftUI
import SwiftData

@main
struct GridTradingToolboxApp: App {
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    @StateObject private var settings = Settings()
    @StateObject private var store = Store()
    @StateObject private var sessionManager = SessionManager()
    
    // let dataProvider = DataProvider.shared
    
    var sharedModelContainer: ModelContainer = {
        let schema = Schema([
            Strategy.self,
            Stock.self,
            TradeGrid.self,
            TradeLog.self,
            FlexibleTradeLog.self,
            StockAccount.self,
            FavoriteStock.self,
        ])
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)
        
        do {
            return try ModelContainer(for: schema, configurations: [modelConfiguration])
        } catch {
            fatalError("Could not create ModelContainer: \(error)")
        }
    }()
    
    var body: some Scene {
        WindowGroup {
            if settings.appFirstLaunch {
                LandingView()
            } else {
                HomeView()
            }
        }
        .environmentObject(store)
        .environmentObject(settings)
        .environmentObject(sessionManager)
        .modelContainer(sharedModelContainer)
        // .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
        //     // 应用进入前台时验证登录状态
        //     sessionManager.validateSession()
        // }
//        .modelContainer(for: Stock.self) { result in
//            do {
//                    let container = try result.get()
//
//                    // Check we haven't already added our users.
//                    let descriptor = FetchDescriptor<Stock>()
//                    let existingStocks = try container.mainContext.fetchCount(descriptor)
//                    guard existingStocks == 0 else { return }
//
//                    // Load and decode the JSON.
//                    guard let url = Bundle.main.url(forResource: "stocks", withExtension: "json") else {
//                        fatalError("Failed to find stocks.json")
//                    }
//
//                    let data = try Data(contentsOf: url)
//                    let stocks = try JSONDecoder().decode([Stock].self, from: data)
//
//                    // Add all our data to the context.
//                    for stock in stocks {
//                        container.mainContext.insert(stock)
//                    }
//                } catch {
//                    print("Failed to pre-seed database.")
//                }
//        }
        //        .modelContainer(sharedModelContainer)
    }
}
