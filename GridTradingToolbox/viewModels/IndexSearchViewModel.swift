//
//  IndexSearchViewModel.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/11/6.
//

import Foundation

class IndexSearchViewModel: ObservableObject {
    @Published var indexInfos: [IndexInfo] = []
    
    func loadData(searchString: String) {
        fetchData(searchString: searchString) { result in
            DispatchQueue.global().async {
                switch result {
                case .success(let data):
                    print(data)
                    let decoder = JSONDecoder()
                    let dateFormatter = DateFormatter()
                    dateFormatter.locale = Locale(identifier: "zh_CN")
                    dateFormatter.timeZone = TimeZone(identifier: "Asia/Shanghai")
                    dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZZZZZ"
                    decoder.dateDecodingStrategy = .formatted(dateFormatter)
                    do {
                        let response = try decoder.decode(IndexsInfoResponse.self, from: data)
                        var tmp = [IndexInfo]()
                        response.success.forEach { indexInfo in
                            // 创建新的指数信息对象
                            let newIndexInfo = IndexInfo(code: indexInfo.code, name: indexInfo.name, metricType: indexInfo.metricType, publishDate: indexInfo.publishDate, changeRate: indexInfo.changeRate, closePoint: indexInfo.closePoint, peTtmPercentile5Y: indexInfo.peTtmPercentile5Y, pbPercentile5Y: indexInfo.pbPercentile5Y, psTtmPercentile5Y: indexInfo.psTtmPercentile5Y, peTtmPercentile10Y: indexInfo.peTtmPercentile10Y, pbPercentile10Y: indexInfo.pbPercentile10Y, psTtmPercentile10Y: indexInfo.psTtmPercentile10Y, updateTimestamp: indexInfo.updateTimestamp)
                            
                            // 检查是否已存在相同code的指数
                            if let existingIndex = tmp.firstIndex(where: { $0.code == indexInfo.code }) {
                                // 如果是市值加权估值数据，更新到现有对象
                                if indexInfo.metricType == "mcw" {
                                    tmp[existingIndex].mcw_peTtmPercentile5Y = indexInfo.peTtmPercentile5Y
                                    tmp[existingIndex].mcw_pbPercentile5Y = indexInfo.pbPercentile5Y
                                    tmp[existingIndex].mcw_psTtmPercentile5Y = indexInfo.psTtmPercentile5Y
                                    
                                    tmp[existingIndex].mcw_peTtmPercentile10Y = indexInfo.peTtmPercentile10Y
                                    tmp[existingIndex].mcw_pbPercentile10Y = indexInfo.pbPercentile10Y
                                    tmp[existingIndex].mcw_psTtmPercentile10Y = indexInfo.psTtmPercentile10Y
                                }
                                // 如果是等权估值数据，更新到现有对象
                                else if indexInfo.metricType == "ewpvo" {
                                    tmp[existingIndex].ewpvo_peTtmPercentile5Y = indexInfo.peTtmPercentile5Y
                                    tmp[existingIndex].ewpvo_pbPercentile5Y = indexInfo.pbPercentile5Y
                                    tmp[existingIndex].ewpvo_psTtmPercentile5Y = indexInfo.psTtmPercentile5Y
                                    
                                    tmp[existingIndex].ewpvo_peTtmPercentile10Y = indexInfo.peTtmPercentile10Y
                                    tmp[existingIndex].ewpvo_pbPercentile10Y = indexInfo.pbPercentile10Y
                                    tmp[existingIndex].ewpvo_psTtmPercentile10Y = indexInfo.psTtmPercentile10Y
                                }
                            } else {
                                // 如果是新的指数，直接添加
                                tmp.append(newIndexInfo)
                            }
                        }
                        DispatchQueue.main.async {
                            self.indexInfos = tmp
                        }
                    } catch let error {
                        print("decode error: \(error)")
                    }

                case .failure(let error):
                    print("\(error)")
//                    msg = "导入失败，请稍后重试"
                }
            }
        }
    }
    
    func fetchData(searchString: String, completion: @escaping (Result<Data, Error>) -> Void) {
    //    let url = "http://localhost:8080/api/v1/stocks/index?codes=\(searchString)&token=cmj&isSearch=true&metricType=ewpvo,mcw"
       let url = "https://dongxibuduo.com/sea/api/v1/stocks/index?codes=\(searchString)&token=cmj&isSearch=true&metricType=ewpvo,mcw"
        guard let url = URL(string: url) else {
            completion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])))
            return
        }
        
        let request = URLRequest(url: url)
        
        let task = URLSession.shared.dataTask(with: request) { (data, response, error) in
            if let error = error {
                completion(.failure(error))
            } else if let data = data {
                completion(.success(data))
            }
        }
        
        task.resume()
    }
}
