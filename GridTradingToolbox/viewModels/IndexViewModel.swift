//
//  IndexViewModel.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/11/3.
//

import Foundation

class IndexInfo: Identifiable, Equatable {
    var id = UUID().uuidString
    var code: String
    var name: String
    var metricType: String
    var publishDate: Date
    var changeRate: Double
    var closePoint: Double

    // 等权估值数据 (ewpvo)
    var ewpvo_peTtmPercentile5Y: Double
    var ewpvo_pbPercentile5Y: Double
    var ewpvo_psTtmPercentile5Y: Double
    
    var ewpvo_peTtmPercentile10Y: Double
    var ewpvo_pbPercentile10Y: Double
    var ewpvo_psTtmPercentile10Y: Double
    
    // 市值加权估值数据 (mcw)
    var mcw_peTtmPercentile5Y: Double
    var mcw_pbPercentile5Y: Double
    var mcw_psTtmPercentile5Y: Double
    
    var mcw_peTtmPercentile10Y: Double
    var mcw_pbPercentile10Y: Double
    var mcw_psTtmPercentile10Y: Double

    var updateTimestamp: Int64
    
    
    // 根据当前估值类型和年限获取对应的PE百分位
    func getPeTtmPercentile(yearType: String, metricType: String) -> Double {
        if metricType == "ewpvo" {
            return yearType == "5Y" ? ewpvo_peTtmPercentile5Y : ewpvo_peTtmPercentile10Y
        } else {
            return yearType == "5Y" ? mcw_peTtmPercentile5Y : mcw_peTtmPercentile10Y
        }
    }
    
    // 根据当前估值类型和年限获取对应的PB百分位
    func getPbPercentile(yearType: String, metricType: String) -> Double {
        if metricType == "ewpvo" {
            return yearType == "5Y" ? ewpvo_pbPercentile5Y : ewpvo_pbPercentile10Y
        } else {
            return yearType == "5Y" ? mcw_pbPercentile5Y : mcw_pbPercentile10Y
        }
    }
    
    // 根据当前估值类型和年限获取对应的PS百分位
    func getPsTtmPercentile(yearType: String, metricType: String) -> Double {
        if metricType == "ewpvo" {
            return yearType == "5Y" ? ewpvo_psTtmPercentile5Y : ewpvo_psTtmPercentile10Y
        } else {
            return yearType == "5Y" ? mcw_psTtmPercentile5Y : mcw_psTtmPercentile10Y
        }
    }

    init(code: String, name: String, metricType: String, publishDate: Date, changeRate: Double, closePoint: Double,
         peTtmPercentile5Y: Double, pbPercentile5Y: Double, psTtmPercentile5Y: Double,
         peTtmPercentile10Y: Double, pbPercentile10Y: Double, psTtmPercentile10Y: Double,
         updateTimestamp: Int64) {
        self.code = code
        self.name = name
        self.metricType = metricType
        self.publishDate = publishDate
        self.changeRate = changeRate
        self.closePoint = closePoint
        
        // 初始化所有估值数据为0
        self.ewpvo_peTtmPercentile5Y = 0
        self.ewpvo_pbPercentile5Y = 0
        self.ewpvo_psTtmPercentile5Y = 0
        self.ewpvo_peTtmPercentile10Y = 0
        self.ewpvo_pbPercentile10Y = 0
        self.ewpvo_psTtmPercentile10Y = 0
        
        self.mcw_peTtmPercentile5Y = 0
        self.mcw_pbPercentile5Y = 0
        self.mcw_psTtmPercentile5Y = 0
        self.mcw_peTtmPercentile10Y = 0
        self.mcw_pbPercentile10Y = 0
        self.mcw_psTtmPercentile10Y = 0
        
        // 根据metricType设置对应的估值数据
        if metricType == "ewpvo" {
            self.ewpvo_peTtmPercentile5Y = peTtmPercentile5Y
            self.ewpvo_pbPercentile5Y = pbPercentile5Y
            self.ewpvo_psTtmPercentile5Y = psTtmPercentile5Y
            self.ewpvo_peTtmPercentile10Y = peTtmPercentile10Y
            self.ewpvo_pbPercentile10Y = pbPercentile10Y
            self.ewpvo_psTtmPercentile10Y = psTtmPercentile10Y
        } else if metricType == "mcw" {
            self.mcw_peTtmPercentile5Y = peTtmPercentile5Y
            self.mcw_pbPercentile5Y = pbPercentile5Y
            self.mcw_psTtmPercentile5Y = psTtmPercentile5Y
            self.mcw_peTtmPercentile10Y = peTtmPercentile10Y
            self.mcw_pbPercentile10Y = pbPercentile10Y
            self.mcw_psTtmPercentile10Y = psTtmPercentile10Y
        }
        
        self.updateTimestamp = updateTimestamp
    }

    static func == (lhs: IndexInfo, rhs: IndexInfo) -> Bool {
        return lhs.code == rhs.code
    }
}

class IndexViewModel: ObservableObject {
    @Published var indexInfos: [IndexInfo] = []

    @MainActor
    func removeIndexInfo(code: String) {
        indexInfos.removeAll { $0.code == code }
    }

    @MainActor
    func addIndexInfo(indexInfo: IndexInfo) {
        indexInfos.append(indexInfo)
    }

    @MainActor
    func fetchIndexInfos(codes: [String]) {
        DispatchQueue.global().async {
            self.fetchData(codes: codes) { result in
                switch result {
                case .success(let data):
                    let decoder = JSONDecoder()
                    let dateFormatter = DateFormatter()
                    dateFormatter.locale = Locale(identifier: "zh_CN")
                    dateFormatter.timeZone = TimeZone(identifier: "Asia/Shanghai")
                    dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZZZZZ"
                    decoder.dateDecodingStrategy = .formatted(dateFormatter)
                    do {
                        let response = try decoder.decode(IndexsInfoResponse.self, from: data)
                        var tmp = [IndexInfo]()
                        response.success.forEach { indexInfo in
                            // 根据metricType字段判断估值类型，并将数据存储到对应的字段中
                            let newIndexInfo = IndexInfo(code: indexInfo.code, name: indexInfo.name, metricType: indexInfo.metricType, publishDate: indexInfo.publishDate, changeRate: indexInfo.changeRate, closePoint: indexInfo.closePoint, peTtmPercentile5Y: indexInfo.peTtmPercentile5Y, pbPercentile5Y: indexInfo.pbPercentile5Y, psTtmPercentile5Y: indexInfo.psTtmPercentile5Y, peTtmPercentile10Y: indexInfo.peTtmPercentile10Y, pbPercentile10Y: indexInfo.pbPercentile10Y, psTtmPercentile10Y: indexInfo.psTtmPercentile10Y, updateTimestamp: indexInfo.updateTimestamp)
                            
                            // 检查是否已存在相同code的指数
                            if let existingIndex = tmp.firstIndex(where: { $0.code == indexInfo.code }) {
                                // 如果是市值加权估值数据，更新到现有对象
                                if indexInfo.metricType == "mcw" {
                                    tmp[existingIndex].mcw_peTtmPercentile5Y = indexInfo.peTtmPercentile5Y
                                    tmp[existingIndex].mcw_pbPercentile5Y = indexInfo.pbPercentile5Y
                                    tmp[existingIndex].mcw_psTtmPercentile5Y = indexInfo.psTtmPercentile5Y
                                    
                                    tmp[existingIndex].mcw_peTtmPercentile10Y = indexInfo.peTtmPercentile10Y
                                    tmp[existingIndex].mcw_pbPercentile10Y = indexInfo.pbPercentile10Y
                                    tmp[existingIndex].mcw_psTtmPercentile10Y = indexInfo.psTtmPercentile10Y
                                }
                                // 如果是等权估值数据，更新到现有对象
                                else if indexInfo.metricType == "ewpvo" {
                                    tmp[existingIndex].ewpvo_peTtmPercentile5Y = indexInfo.peTtmPercentile5Y
                                    tmp[existingIndex].ewpvo_pbPercentile5Y = indexInfo.pbPercentile5Y
                                    tmp[existingIndex].ewpvo_psTtmPercentile5Y = indexInfo.psTtmPercentile5Y
                                    
                                    tmp[existingIndex].ewpvo_peTtmPercentile10Y = indexInfo.peTtmPercentile10Y
                                    tmp[existingIndex].ewpvo_pbPercentile10Y = indexInfo.pbPercentile10Y
                                    tmp[existingIndex].ewpvo_psTtmPercentile10Y = indexInfo.psTtmPercentile10Y
                                }
                            } else {
                                // 如果是新的指数，直接添加
                                tmp.append(newIndexInfo)
                            }
                        }
                        tmp.sort { $0.code < $1.code }
                        DispatchQueue.main.async {
                            self.indexInfos = tmp
                        }
                    } catch let error {
                        print("decode error: \(error)")
                    }
                case .failure(let error):
                    print(error)
                }
            }
        }
    }

    func fetchData(codes: [String], completion: @escaping (Result<Data, Error>) -> Void) {
        let codesString = codes.joined(separator: ",")
        // let url = "http://localhost:8080/api/v1/stocks/index?codes=\(codesString)&token=cmj&metricType=ewpvo,mcw"
       let url = "https://dongxibuduo.com/sea/api/v1/stocks/index?codes=\(codesString)&token=cmj&metricType=ewpvo,mcw"
        guard let url = URL(string: url) else {
            completion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])))
            return
        }
        
        let request = URLRequest(url: url)
        
        let task = URLSession.shared.dataTask(with: request) { (data, response, error) in
            if let error = error {
                completion(.failure(error))
            } else if let data = data {
                completion(.success(data))
            }
        }
        
        task.resume()
    }
}
