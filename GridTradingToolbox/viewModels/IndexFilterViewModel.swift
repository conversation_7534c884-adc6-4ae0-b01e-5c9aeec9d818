import Foundation
import SwiftUI
import Combine

@MainActor
class IndexFilterViewModel: ObservableObject {
    @Published var filteredIndexes: [FilteredIndex] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let token = "cmj"
    private let baseURL = "https://dongxibuduo.com/sea"
    
    func fetchFilteredIndexes(
        pePercentile5Y: Double? = nil,
        pePercentile10Y: Double? = nil,
        pbPercentile5Y: Double? = nil,
        pbPercentile10Y: Double? = nil,
        metricType: String
    ) async {
        isLoading = true
        errorMessage = nil
        
        do {
            let request = FilterRequest(
                pePercentile5Y: pePercentile5Y,
                pePercentile10Y: pePercentile10Y,
                pbPercentile5Y: pbPercentile5Y,
                pbPercentile10Y: pbPercentile10Y,
                weightingMethods: [metricType]
            )
            
            let url = URL(string: "\(baseURL)/api/v1/stocks/filter?token=\(token)")!
            var urlRequest = URLRequest(url: url)
            urlRequest.httpMethod = "POST"
            urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
            
            let encoder = JSONEncoder()
            let requestData = try encoder.encode(request)
            urlRequest.httpBody = requestData
            
            print("Request URL: \(url)")
            print("Request Body: \(String(data: requestData, encoding: .utf8) ?? "")")
            
            let (data, response) = try await URLSession.shared.data(for: urlRequest)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw URLError(.badServerResponse)
            }
            
            print("Response Status Code: \(httpResponse.statusCode)")
            print("Response Data: \(String(data: data, encoding: .utf8) ?? "")")
            
            guard httpResponse.statusCode == 200 else {
                throw URLError(.badServerResponse)
            }
            
            let decoder = JSONDecoder()
            let result = try decoder.decode(FilterResult.self, from: data)
            
            print("Parsed Result - Success Count: \(result.success.count), Failed Count: \(result.failed.count)")
            
            // 根据metricType过滤结果
            self.filteredIndexes = result.success
                .filter { $0.metricType == metricType }
                .map { FilteredIndex(from: $0) }
            
            print("Filtered Indexes Count: \(self.filteredIndexes.count)")
        } catch {
            print("Error: \(error)")
            errorMessage = error.localizedDescription
        }
        
        isLoading = false
    }
}
