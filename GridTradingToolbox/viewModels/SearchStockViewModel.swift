//
//  SearchStockViewModel.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/4/6.
//

import Foundation

class SearchStockViewModel: ObservableObject {
    @Published var stocks: [Stock] = []
    
    func loadData(searchString: String) {
        fetchData(searchString: searchString) { result in
            DispatchQueue.global().async {
                switch result {
                case .success(let data):
                    print(data)
                    let decoder = JSONDecoder()
                    let dateFormatter = DateFormatter()
                    dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
                    decoder.dateDecodingStrategy = .formatted(dateFormatter)
                    do {
                        let response = try decoder.decode(SearchCodeResponse.self, from: data)
                        var tmp = [Stock]()
                        response.stocks.forEach { stock in
                            tmp.append(Stock(code: stock.code, name: stock.name))
                        }
                        DispatchQueue.main.async {
                            self.stocks = tmp
                        }
                    } catch let error {
                        print("decode error: \(error)")
                    }

                case .failure(let error):
                    print("\(error)")
//                    msg = "导入失败，请稍后重试"
                }
            }
        }
    }
    
    func fetchData(searchString: String, completion: @escaping (Result<Data, Error>) -> Void) {
//        let url = "http://localhost:8080/api/v1/stocks/?searchCode=\(searchString)&token=cmj"
        let url = "https://dongxibuduo.com/sea/api/v1/stocks/?searchCode=\(searchString)&token=cmj"
        guard let url = URL(string: url) else {
            completion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])))
            return
        }
        
        let request = URLRequest(url: url)
        
        let task = URLSession.shared.dataTask(with: request) { (data, response, error) in
            if let error = error {
                completion(.failure(error))
            } else if let data = data {
                completion(.success(data))
            }
        }
        
        task.resume()
    }
}
