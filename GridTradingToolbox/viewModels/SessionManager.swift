//
//  SeaApiManager.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/8/12.
//

import SwiftUI
import Alamofire
import AuthenticationServices

struct CreateUserRequest: Encodable {
    let identityToken: String
    let appleId: String
    let fullName: String
    let email: String
}

// 新增：登录响应数据结构
struct LoginResponse: Decodable {
    let success: Bool
    let message: String
    let data: LoginData?
}

struct LoginData: Decodable {
    let accessToken: String
    let refreshToken: String
    let tokenType: String
    let expiresIn: Int
    let user: UserInfo
    let isNewUser: Bool
}

struct UserInfo: Codable {
    let id: Int
    let appleUserId: String
    let email: String
    let isPrivateEmail: Bool
    let emailVerified: Bool
    let realUserStatus: Int?
    let fullName: String?
}

class SessionManager: ObservableObject {
    @Published var errorMsg: String = ""
    @Published var accessToken: String = ""
    @Published var refreshToken: String = ""
    @Published var expireAt: Date = .now
    @Published var userInfo: UserInfo?
    @Published var isLoggedIn: Bool = false
    
    // UserDefaults键名
    private let userDefaultsKeys = (
        isLoggedIn: "SessionManager_IsLoggedIn",
        accessToken: "SessionManager_AccessToken",
        refreshToken: "SessionManager_RefreshToken",
        expireAt: "SessionManager_ExpireAt",
        userInfo: "SessionManager_UserInfo",
        appleUserId: "SessionManager_AppleUserId"
    )
    
    init() {
        // 应用启动时自动恢复登录状态
        restoreSessionFromStorage()
        
        // 如果有登录状态，验证Apple ID状态
        if isLoggedIn {
            checkAppleIDStatus()
        }
    }
    
    func login(identityToken: String, appleId: String, fullname: String?, email: String?) {
        print("fuck identityToken: \(identityToken)")
        print("fuck appleId: \(appleId)")
        print("fuck fullname: \(fullname)")
        print("fuck email: \(email)")
        
//        let url = "http://localhost:8080/api/v1/apple/login"
        let url = "https://dongxibuduo.com/sea/api/v1/apple/login"
        let parameters = CreateUserRequest(identityToken: identityToken, appleId: appleId, fullName: fullname ?? "", email: email ?? "")
        
        AF.request(url,
                   method: .post,
                   parameters: parameters,
                   encoder: JSONParameterEncoder.default)
            .validate()
            .responseDecodable(of: LoginResponse.self) { response in
                DispatchQueue.main.async {
                    switch response.result {
                    case .success(let loginResponse):
                        if loginResponse.success, let data = loginResponse.data {
                            // 登录成功，保存token和用户信息
                            self.accessToken = data.accessToken
                            self.refreshToken = data.refreshToken
                            self.userInfo = data.user
                            self.isLoggedIn = true
                            
                            // 计算token过期时间
                            self.expireAt = Date().addingTimeInterval(TimeInterval(data.expiresIn))
                            
                            // 持久化保存登录状态
                            self.saveSessionToStorage()
                            
                            self.errorMsg = ""
                        } else {
                            print("fuck failed")
                            // 服务器返回失败
                            self.errorMsg = loginResponse.message
                            self.clearSession()
                        }
                    case .failure(let error):
                        // 网络请求失败
                        print("fuck 登录请求错误: \(error.localizedDescription)")
                        self.errorMsg = "网络连接失败，请检查网络设置后重试"
                        self.clearSession()
                    }
                }
            }
    }
    
    func getUserName() -> String {
        if isLoggedIn {
            if userInfo != nil && userInfo?.fullName != nil {
                return userInfo!.fullName!
            }
            return "已登录"
        }
        return "未登录"
    }
    
    // 登出方法
    func logout() {
        clearSession()
        clearStoredSession()
    }
    
    // 清除会话信息
    func clearSession() {
        accessToken = ""
        refreshToken = ""
        expireAt = .now
        userInfo = nil
        isLoggedIn = false
    }
    
    // 检查token是否过期
    func isTokenExpired() -> Bool {
        return Date() >= expireAt
    }
    
    // MARK: - 持久化存储方法
    
    // 保存登录状态到UserDefaults
    private func saveSessionToStorage() {
        let defaults = UserDefaults.standard
        
        defaults.set(isLoggedIn, forKey: userDefaultsKeys.isLoggedIn)
        defaults.set(accessToken, forKey: userDefaultsKeys.accessToken)
        defaults.set(refreshToken, forKey: userDefaultsKeys.refreshToken)
        defaults.set(expireAt, forKey: userDefaultsKeys.expireAt)
        
        // 保存Apple User ID
        if let userInfo = userInfo {
            defaults.set(userInfo.appleUserId, forKey: userDefaultsKeys.appleUserId)
            
            // 将UserInfo转换为Data保存
            if let userData = try? JSONEncoder().encode(userInfo) {
                defaults.set(userData, forKey: userDefaultsKeys.userInfo)
            }
        }
        
        defaults.synchronize()
        print("✅ 登录状态已保存到UserDefaults")
    }
    
    // 从UserDefaults恢复登录状态
    private func restoreSessionFromStorage() {
        let defaults = UserDefaults.standard
        
        guard defaults.bool(forKey: userDefaultsKeys.isLoggedIn) else {
            print("❌ 未找到已保存的登录状态")
            return
        }
        
        // 检查token是否过期
        let storedExpireAt = defaults.object(forKey: userDefaultsKeys.expireAt) as? Date ?? Date()
        if Date() >= storedExpireAt {
            print("⏰ Token已过期，清除登录状态")
            clearStoredSession()
            return
        }
        
        // 恢复登录信息
        self.isLoggedIn = true
        self.accessToken = defaults.string(forKey: userDefaultsKeys.accessToken) ?? ""
        self.refreshToken = defaults.string(forKey: userDefaultsKeys.refreshToken) ?? ""
        self.expireAt = storedExpireAt
        
        // 恢复用户信息
        if let userData = defaults.data(forKey: userDefaultsKeys.userInfo),
           let userInfo = try? JSONDecoder().decode(UserInfo.self, from: userData) {
            self.userInfo = userInfo
        }
        
        print("✅ 已从UserDefaults恢复登录状态")
        print("👤 用户: \(getUserName())")
        print("⏱️ Token过期时间: \(expireAt)")
    }
    
    // 清除存储的登录状态
    private func clearStoredSession() {
        let defaults = UserDefaults.standard
        
        defaults.removeObject(forKey: userDefaultsKeys.isLoggedIn)
        defaults.removeObject(forKey: userDefaultsKeys.accessToken)
        defaults.removeObject(forKey: userDefaultsKeys.refreshToken)
        defaults.removeObject(forKey: userDefaultsKeys.expireAt)
        defaults.removeObject(forKey: userDefaultsKeys.userInfo)
        defaults.removeObject(forKey: userDefaultsKeys.appleUserId)
        
        defaults.synchronize()
        print("🗑️ 已清除存储的登录状态")
    }
    
    // MARK: - Apple ID状态检查
    
    // 检查Apple ID登录状态
    private func checkAppleIDStatus() {
        guard let userInfo = userInfo else { return }
        
        let appleIDProvider = ASAuthorizationAppleIDProvider()
        appleIDProvider.getCredentialState(forUserID: userInfo.appleUserId) { [weak self] (credentialState, error) in
            DispatchQueue.main.async {
                switch credentialState {
                case .authorized:
                    print("✅ Apple ID状态正常")
                    // 状态正常，保持登录
                    break
                case .revoked, .notFound:
                    print("❌ Apple ID状态异常，清除登录状态")
                    // Apple ID被撤销或未找到，清除登录状态
                    self?.logout()
                case .transferred:
                    print("🔄 Apple ID已转移")
                    // 可以根据需要处理转移情况
                    break
                @unknown default:
                    print("❓ 未知的Apple ID状态")
                    break
                }
            }
        }
    }
    
    // 刷新token（预留接口，后续可实现）
    func refreshAccessToken(completion: @escaping (Bool) -> Void) {
        // TODO: 实现token刷新逻辑
        completion(false)
    }
    
    // 定期检查登录状态（可以在适当的时候调用）
    func validateSession() {
        if isLoggedIn {
            if isTokenExpired() {
                print("⏰ Token已过期")
                logout()
            } else {
                checkAppleIDStatus()
            }
        }
    }
}
