//
//  StocksPriceViewModel.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/4/8.
//

import Foundation
import Combine

struct StrategyTriggerPrice {
    var strategyId: UUID
    var code: String
    var strategyName: String
    var sellTriggerPrice: Double?
    var buyTriggerPrice: Double?

    init(strategyId: UUID, code: String, strategyName: String, sellTriggerPriceInCents: Int?, buyTriggerPriceInCents: Int?) {
        self.strategyId = strategyId
        self.code = code
        self.strategyName = strategyName
        if sellTriggerPriceInCents != nil {
            self.sellTriggerPrice = Double(sellTriggerPriceInCents!) / 1000.0
        }
        if buyTriggerPriceInCents != nil {
            self.buyTriggerPrice = Double(buyTriggerPriceInCents!) / 1000.0
        }
    }
}

actor StrategyTriggerPricesStore {
    private var prices: [StrategyTriggerPrice] = []
    
    // 基础的获取和设置方法
    func getPrices() -> [StrategyTriggerPrice] {
        prices
    }
    
    func setPrices(_ newPrices: [StrategyTriggerPrice]) {
        prices = newPrices
    }
    
    // 可以添加更多业务相关的方法
    func appendPrice(_ price: StrategyTriggerPrice) {
        prices.append(price)
    }
    
    func removePrice(at index: Int) {
        prices.remove(at: index)
    }

    func removePrice(strategyId: UUID) {
        prices = prices.filter { $0.strategyId != strategyId }
    }
    
    func clear() {
        prices.removeAll()
    }
}

class StockGridPrice: Identifiable {
    var id = UUID().uuidString
    var strategyId: UUID
    var code: String
    var strategyName: String
    var triggerPrice: Double
    var curPrice: Double
    var triggerPercent: Double
    
    init(strategyId: UUID, code: String, strategyName: String, triggerPrice: Double, curPrice: Double) {
        self.strategyId = strategyId
        self.code = code
        self.strategyName = strategyName
        self.triggerPrice = triggerPrice
        self.curPrice = curPrice
        self.triggerPercent = (triggerPrice - curPrice) / curPrice * 100
    }
}

class StocksPriceViewModel: ObservableObject {
    @Published var sellGrids: [StockGridPrice] = []
    @Published var buyGrids: [StockGridPrice] = []
    @Published var nearestSellTrigerGrid: StockGridPrice?
    @Published var nearestBuyTrigerGrid: StockGridPrice?
    @Published var stocksPrice: [String: Double] = [:]
    @Published var isLoading = true

    private var timerCancellable: AnyCancellable?

    private let strategyTriggerPricesStore = StrategyTriggerPricesStore()

    @MainActor
    func setupTimer(strategies: [Strategy]) {
        timerCancellable = Timer.publish(every: 10, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                guard let self = self else { return }
                // 创建异步任务
                Task {
                    await self.queryPriceLoop(strategies: strategies)
                }
            }
        Task {
            await self.queryPriceLoop(strategies: strategies)
        }
    }

    @MainActor
    func cancelTimer() {
        timerCancellable?.cancel()
        timerCancellable = nil
    }

    @MainActor
    func removeStrategy(strategyId: UUID) {
        // 从sellgrids 和 buygrids 中移除 对应策略id的数据
        sellGrids = sellGrids.filter { $0.strategyId != strategyId }
        buyGrids = buyGrids.filter { $0.strategyId != strategyId }
        nearestSellTrigerGrid = sellGrids.count > 0 ? sellGrids[0] : nil
        nearestBuyTrigerGrid = buyGrids.count > 0 ? buyGrids[0] : nil
        // await strategyTriggerPricesStore.removePrice(strategyId: strategyId)
    }
    
    @MainActor
    func queryPriceLoop(strategies: [Strategy]) async {
        let triggerPrices = strategies.filter { strategy in
            return strategy.triggerNotify && strategy.code != nil && strategy.status != Strategy.StrategyStatus.archive.rawValue
        }.map { strategy in
            let sellTriggerPriceInCents = strategy.sortedSmallGrids.first?.getSellPrice() ?? nil
            let buyTriggerPriceInCents = strategy.getNextTradeGrid(gridType: TradeGrid.GridType.small)?.theoreticalBuyPrice ?? nil
            return StrategyTriggerPrice(strategyId: strategy.id, code: strategy.code!, strategyName: strategy.name, sellTriggerPriceInCents: sellTriggerPriceInCents, buyTriggerPriceInCents: buyTriggerPriceInCents)
        }
        await strategyTriggerPricesStore.setPrices(triggerPrices)
        await queryPrice()
    }
    
    func queryPrice() async {
        Task.detached {
            let strategyTriggerPrices = await self.strategyTriggerPricesStore.getPrices()
            if strategyTriggerPrices.isEmpty {
                await MainActor.run {
                    self.isLoading = false
                }
            }

            do {
                let data = try await self.fetchDataAsync(strategyTriggerPrices: strategyTriggerPrices)
                let sellMap = self.sellGrids.reduce(into: [String: [StockGridPrice]]()) { result, stockGridPrice in
                    let keyExists = result[stockGridPrice.code] != nil
                    if keyExists {
                        result[stockGridPrice.code]!.append(stockGridPrice)
                    } else {
                        result[stockGridPrice.code] = [stockGridPrice]
                    }
                }
                let buyMap = self.buyGrids.reduce(into: [String: [StockGridPrice]]()) { result, stockGridPrice in
                    let keyExists = result[stockGridPrice.code] != nil
                    if keyExists {
                        result[stockGridPrice.code]!.append(stockGridPrice)
                    } else {
                        result[stockGridPrice.code] = [stockGridPrice]
                    }
                }
                
                let decoder = JSONDecoder()
                do {
                    let response = try decoder.decode(StocksPriceResponse.self, from: data)
                    var t: [String: Double] = [:]
                    if self.stocksPrice.isEmpty {
                        var tmpSell = [StockGridPrice]()
                        var tmpBuy = [StockGridPrice]()
                        var minSell: StockGridPrice? = nil
                        response.success.forEach { stock in
                            // 查找所有使用该股票代码的策略
                            let relatedTriggerPrices = strategyTriggerPrices.filter { $0.code == stock.code }
                            
                            // 处理每个相关策略的触发价格
                            for strategyTriggerPrice in relatedTriggerPrices {
                                if strategyTriggerPrice.sellTriggerPrice != nil {
                                    let tmpSellPrice = StockGridPrice(
                                        strategyId: strategyTriggerPrice.strategyId,
                                        code: stock.code,
                                        strategyName: strategyTriggerPrice.strategyName,
                                        triggerPrice: strategyTriggerPrice.sellTriggerPrice!,
                                        curPrice: stock.price
                                    )
                                    tmpSell.append(tmpSellPrice)
                                    if minSell == nil {
                                        minSell = tmpSellPrice
                                    } else if tmpSellPrice.triggerPercent < minSell!.triggerPercent {
                                        minSell = tmpSellPrice
                                    }
                                }
                                
                                if strategyTriggerPrice.buyTriggerPrice != nil {
                                    let tmpBuyPrice = StockGridPrice(
                                        strategyId: strategyTriggerPrice.strategyId,
                                        code: stock.code,
                                        strategyName: strategyTriggerPrice.strategyName,
                                        triggerPrice: strategyTriggerPrice.buyTriggerPrice!,
                                        curPrice: stock.price
                                    )
                                    tmpBuy.append(tmpBuyPrice)
                                }
                                
                                t[stock.code] = stock.price
                            }
                        }
                        let sortedSell = tmpSell.sorted { g1, g2 in
                            return g1.triggerPercent < g2.triggerPercent;
                        }
                        let sortedBuy = tmpBuy.sorted { g1, g2 in
                            return g1.triggerPercent > g2.triggerPercent;
                        }
                        await MainActor.run {
                            print("fuck: \(sortedSell.count)")
                            print("fuck: \(sortedBuy.count)")
                            self.sellGrids = sortedSell
                            self.buyGrids = sortedBuy
                            self.isLoading = false
                            if sortedSell.count > 0 {
                                self.nearestSellTrigerGrid = sortedSell[0]
                            }
                            if sortedBuy.count > 0 {
                                self.nearestBuyTrigerGrid = sortedBuy[0]
                            }
                        }
                    } else {
                        response.success.forEach { stock in
                            // 更新该股票所有相关策略的卖出价格
                            if let sellPrices = sellMap[stock.code] {
                                sellPrices.forEach { price in
                                    price.curPrice = stock.price
                                    price.triggerPercent = (price.triggerPrice - stock.price) / stock.price * 100
                                }
                            }
                            
                            // 更新该股票所有相关策略的买入价格
                            if let buyPrices = buyMap[stock.code] {
                                buyPrices.forEach { price in
                                    price.curPrice = stock.price
                                    price.triggerPercent = (price.triggerPrice - stock.price) / stock.price * 100
                                }
                            }
                        }
                        let tmpSell = self.sellGrids.sorted { g1, g2 in
                            return g1.triggerPercent < g2.triggerPercent;
                        }
                        let tmpBuy = self.buyGrids.sorted { g1, g2 in
                            return g1.triggerPercent > g2.triggerPercent;
                        }
                        await MainActor.run {
                            self.sellGrids = tmpSell
                            self.buyGrids = tmpBuy
                            self.isLoading = false
                            if tmpSell.count > 0 {
                                self.nearestSellTrigerGrid = tmpSell[0]
                            }
                            if tmpBuy.count > 0 {
                                self.nearestBuyTrigerGrid = tmpBuy[0]
                            }
                        }
                    }
                } catch let error {
                    print("decode error: \(error)")
                }
            } catch {
                print("fetch data error: \(error)")
            }

        }
        // if strategyTriggerPricesStore.getPrices().isEmpty {
        //     isLoading = false
        //     return
        // }
        // print("fuck query")
        // DispatchQueue.global(qos: .background).async {
        //     self.fetchData(strategyTriggerPrices: strategyTriggerPrices) { result in
        //         switch result {
        //         case .success(let data):
        //             let strategiesMap = strategyTriggerPrices.reduce(into: [String: StrategyTriggerPrice]()) { result, strategyTriggerPrice in
        //                 result[strategyTriggerPrice.code] = strategyTriggerPrice
        //             }
        //             let sellMap = self.sellGrids.reduce(into: [String: [StockGridPrice]]()) { result, stockGridPrice in
        //                 let keyExists = result[stockGridPrice.code] != nil
        //                 if keyExists {
        //                     result[stockGridPrice.code]!.append(stockGridPrice)
        //                 } else {
        //                     result[stockGridPrice.code] = [stockGridPrice]
        //                 }
        //             }
        //             let buyMap = self.buyGrids.reduce(into: [String: [StockGridPrice]]()) { result, stockGridPrice in
        //                 let keyExists = result[stockGridPrice.code] != nil
        //                 if keyExists {
        //                     result[stockGridPrice.code]!.append(stockGridPrice)
        //                 } else {
        //                     result[stockGridPrice.code] = [stockGridPrice]
        //                 }
        //             }
        //             print("get fucking response")
        //             let decoder = JSONDecoder()
        //             do {
        //                 let response = try decoder.decode(StocksPriceResponse.self, from: data)
        //                 var t: [String: Double] = [:]
        //                 if self.stocksPrice.isEmpty {
        //                     var tmpSell = [StockGridPrice]()
        //                     var tmpBuy = [StockGridPrice]()
        //                     var minSell: StockGridPrice? = nil
        //                     response.success.forEach { stock in
        //                         let strategyTriggerPrice = strategiesMap[stock.code]
        //                         if strategyTriggerPrice != nil {
        //                             if strategyTriggerPrice!.sellTriggerPrice != nil {
        //                                 let tmpSellPrice = StockGridPrice(strategyId: strategyTriggerPrice!.strategyId, code: stock.code, triggerPrice: strategyTriggerPrice!.sellTriggerPrice!, curPrice: stock.price)
        //                                 tmpSell.append(tmpSellPrice)
        //                                 if minSell == nil {
        //                                     minSell = tmpSellPrice
        //                                 } else if tmpSellPrice.triggerPercent < minSell!.triggerPercent {
        //                                     minSell = tmpSellPrice
        //                                 }
        //                             }
        //                             if strategyTriggerPrice!.buyTriggerPrice != nil {
        //                                 let tmpBuyPrice = StockGridPrice(strategyId: strategyTriggerPrice!.strategyId, code: stock.code, triggerPrice: strategyTriggerPrice!.buyTriggerPrice!, curPrice: stock.price)
        //                                 tmpBuy.append(tmpBuyPrice)
        //                             }
        //                             t[stock.code] = stock.price
        //                         }
        //                         // if strategy != nil {
        //                         //     let grids = strategy!.sortedSmallGrids
        //                         //     if grids.count > 0 {
        //                         //         let grid = grids.first
        //                         //         var triggerPrice = Double(grid!.theoreticalSellPrice) / 1000.0
        //                         //         if strategy!.calculateInterest {
        //                         //             triggerPrice = Double(grid!.getSellPriceWithInterest(calInterest: strategy!.calculateInterest, period: strategy!.interestPeriod, interest: strategy!.interest)) ?? 0
        //                         //         }
        //                         //         let tmpSellPrice = StockGridPrice(strategy: strategy!, triggerPrice: triggerPrice, curPrice: stock.price)
        //                         //         tmpSell.append(tmpSellPrice)
        //                         //         //                                    tmpAll.append(tmpSellPrice)
        //                         //         if minSell == nil {
        //                         //             minSell = tmpSellPrice
        //                         //         } else if tmpSellPrice.triggerPercent < minSell!.triggerPercent {
        //                         //             minSell = tmpSellPrice
        //                         //         }
        //                         //     }
        //                         //     let nextGrid = strategy!.getNextTradeGrid(gridType: TradeGrid.GridType.small);
        //                         //     if nextGrid != nil {
        //                         //         let tmpBuyPrice = StockGridPrice(strategy: strategy!, triggerPrice: Double(nextGrid!.theoreticalBuyPrice) / 1000.0, curPrice: stock.price)
        //                         //         tmpBuy.append(tmpBuyPrice)
        //                         //     }
        //                         //     t[stock.code] = stock.price
                                    
        //                         // }
        //                     }
        //                     tmpSell.sort { g1, g2 in
        //                         return g1.triggerPercent < g2.triggerPercent;
        //                     }
        //                     tmpBuy.sort { g1, g2 in
        //                         return g1.triggerPercent > g2.triggerPercent;
        //                     }
        //                     DispatchQueue.main.async {
        //                         print("fuck: \(tmpSell.count)")
        //                         print("fuck: \(tmpBuy.count)")
        //                         self.sellGrids = tmpSell
        //                         self.buyGrids = tmpBuy
        //                         self.isLoading = false
        //                         if tmpSell.count > 0 {
        //                             self.nearestSellTrigerGrid = tmpSell[0]
        //                         }
        //                         if tmpBuy.count > 0 {
        //                             self.nearestBuyTrigerGrid = tmpBuy[0]
        //                         }
        //                     }
        //                 } else {
        //                     response.success.forEach { stock in
        //                         let sellPrices = sellMap[stock.code]
        //                         let buyPrices = buyMap[stock.code]
                                
        //                         sellPrices!.forEach { price in
        //                             price.curPrice = stock.price
        //                             price.triggerPercent = (price.triggerPrice - stock.price) / stock.price * 100
        //                         }
                                
        //                         buyPrices!.forEach { price in
        //                             price.curPrice = stock.price
        //                             price.triggerPercent = (price.triggerPrice - stock.price) / stock.price * 100
        //                         }
        //                     }
        //                     let tmpSell = self.sellGrids.sorted { g1, g2 in
        //                         return g1.triggerPercent < g2.triggerPercent;
        //                     }
        //                     let tmpBuy = self.buyGrids.sorted { g1, g2 in
        //                         return g1.triggerPercent > g2.triggerPercent;
        //                     }
        //                     DispatchQueue.main.async {
        //                         self.sellGrids = tmpSell
        //                         self.buyGrids = tmpBuy
        //                         self.isLoading = false
        //                         if tmpSell.count > 0 {
        //                             self.nearestSellTrigerGrid = tmpSell[0]
        //                         }
        //                         if tmpBuy.count > 0 {
        //                             self.nearestBuyTrigerGrid = tmpBuy[0]
        //                         }
        //                     }
        //                 }
        //             } catch let error {
        //                 print("decode error: \(error)")
        //             }
                    
        //         case .failure(let error):
        //             print("fuck \(error)")
        //         }
        //     }
        // }
    }
    
    func fetchDataAsync(strategyTriggerPrices: [StrategyTriggerPrice]) async throws -> Data {
        let codes = strategyTriggerPrices.map { $0.code }.joined(separator: ",")
        let urlString = "https://dongxibuduo.com/sea/api/v1/stocks/batch?codes=\(codes)&token=cmj"
        
        guard let url = URL(string: urlString) else {
            throw URLError(.badURL)
        }
        
        let (data, _) = try await URLSession.shared.data(from: url)
        return data
    }
}
