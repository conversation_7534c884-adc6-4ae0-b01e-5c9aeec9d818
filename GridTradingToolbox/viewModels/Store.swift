//
//  Store.swift
//  GridTradingToolbox
//
//  Created by 陈敏杰 on 2024/2/18.
//

import Foundation
import StoreKit

public enum StoreError: Error {
    case failedVerification
}

@MainActor 
final class Store: ObservableObject {
    @Published private(set) var offlineProducts: [Product] = []
    @Published private(set) var subscriptionProducts: [Product] = []
    @Published private(set) var activeTransactions: Set<StoreKit.Transaction> = []
    @Published private(set) var purchasedProducts: Set<String> = []
    private var updates: Task<Void, Never>?

    init() {
        updates = Task {
            for await update in StoreKit.Transaction.updates {
                if let transaction = try? update.payloadValue {
                    await fetchActiveTransactions()
                    await transaction.finish()
                }
            }
        }
        
        Task {
            await fetchProducts()
            
            await fetchActiveTransactions()
        }
    }
    
    deinit {
        updates?.cancel()
    }
    
    func fetchProducts() async {
        do {
            subscriptionProducts = try await Product.products(
                for: [
                    "gtt.subscription.month", "gtt.subscription.year", "gtt.month", "gtt.year"
                ]
            )
            
//            offlineProducts = try await Product.products(
//                for: [
//                    "gridtradingtoolboxpro"
//                ]
//            )
            
        } catch {
            offlineProducts = []
            subscriptionProducts = []
        }
    }
    
    func purchase(_ product: Product) async throws -> Bool {
        let result = try await product.purchase()
        switch result {
        case .success(let verificationResult):
            let transaction = try checkVerified(verificationResult)
            await fetchActiveTransactions()
            activeTransactions.insert(transaction)
            purchasedProducts.insert(transaction.productID)
            await transaction.finish()
            print("已添加：" + transaction.productID)
//            if let transaction = try? verificationResult.payloadValue {
//                activeTransactions.insert(transaction)
//                purchasedProducts.insert(transaction.productID)
//                // todo
//                await transaction.finish()
//                print("已添加：" + transaction.productID)
//            }
//            await waitForProductPurchase(product.id)
            print("购买成功")
            return true
        case .userCancelled:
            break
        case .pending:
            break
        @unknown default:
            break
        }
        return false
    }
    
    func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
        //Check whether the JWS passes StoreKit verification.
        switch result {
        case .unverified:
            print("purchase unverified")
            //StoreKit parses the JWS, but it fails verification.
            throw StoreError.failedVerification
        case .verified(let safe):
            print("purchase verified")
            //The result is verified. Return the unwrapped value.
            return safe
        }
    }
    
    func fetchActiveTransactions() async {
        var activeTransactions: Set<StoreKit.Transaction> = []
        var purchasedProducts: Set<String> = []
        
        for await entitlement in StoreKit.Transaction.currentEntitlements {
            if let transaction = try? entitlement.payloadValue {
                activeTransactions.insert(transaction)
                purchasedProducts.insert(transaction.productID)
            }
        }
    
        self.activeTransactions = activeTransactions
        self.purchasedProducts = purchasedProducts
        for purchasedProduct in purchasedProducts {
            print("-" + purchasedProduct)
        }
    }
    
    func waitForProductPurchase(_ productID: String) async {
        for _ in 1...100 {
            await fetchActiveTransactions()
            if purchasedProducts.contains(productID) {
                break
            } else {
                try? await Task.sleep(nanoseconds: 300000000)
            }
        }
    }
    
    func isPurchased(_ product: Product) -> Bool {
        for purchasedProduct in purchasedProducts {
            print("-" + purchasedProduct)
        }
        return purchasedProducts.contains(product.id)
    }
}
