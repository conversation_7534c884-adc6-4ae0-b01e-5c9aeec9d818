//
//  Settings.swift
//  GridTradingToolbox
//
//  Created by 陈敏杰 on 2024/1/27.
//

import Foundation
import SwiftUI

class Settings: ObservableObject {
    @Published var showUpdate: Bool = false

    @AppStorage("appFirstLaunch") var appFirstLaunch: Bool = true
    @AppStorage("showFeatureUpdate") var showFeatureUpdate: Bool = true
    @AppStorage("subscriptionMember") var subscriptionMember: Bool = false
    @AppStorage("offlineMember") var offlineMember: Bool = false
    @AppStorage("defaultAccountId") var defaultAccountId: String = "default"
    @AppStorage("lastViewedVersion") var lastViewedVersion: String = ""
    @AppStorage("favoriteIndexCodes") var favoriteIndexCodes: String = "000001,399006"


    func checkForNewFeatures() {
        // 获取当前应用版本
        if let currentVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String {
            // 如果存储的上次查看版本与当前版本不同，显示更新弹窗
            if lastViewedVersion != currentVersion {
                showUpdate = true
                lastViewedVersion = currentVersion
            }
        }
    }
}
