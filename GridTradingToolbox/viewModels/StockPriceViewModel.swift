//
//  StockPriceViewModel.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/4/20.
//

import Foundation

class StockPriceViewModel: ObservableObject {
    @Published var price: Double = 0
    @Published var changePercent: Double = 0
    @Published var isLoading: Bool = true
//    @Published var timer = Timer.publish(every: 5, on: .main, in: .common).autoconnect()
    var strategy: Strategy
    
    init(strategy: Strategy) {
        self.strategy = strategy
    }
    
    func queryPrice() {
        print("start fuck query " + strategy.name)
        DispatchQueue.global(qos: .background).async {
            self.fetchData(strategies: [self.strategy]) { result in
                switch result {
                case .success(let data):
                    print("get fucking response")
                    let decoder = JSONDecoder()
                    do {
                        let response = try decoder.decode(StocksPriceResponse.self, from: data)
                        response.success.forEach { stock in
                            DispatchQueue.main.async {
                                self.price = stock.price
                                self.changePercent = stock.changePercent
                                self.isLoading = false
                            }
                        }
                    } catch let error {
                        print("decode error: \(error)")
                    }
                    
                case .failure(let error):
                    print("fuck \(error)")
                }
            }
        }
    }
    
    
    func fetchData(strategies: [Strategy], completion: @escaping (Result<Data, Error>) -> Void) {
        let codes = strategies.map { $0.code! }.joined(separator: ",")
//        let url = "http://localhost:8080/api/v1/stocks/batch?codes=\(codes)&token=cmj"
        let url = "https://dongxibuduo.com/sea/api/v1/stocks/batch?codes=\(codes)&token=cmj"
        guard let url = URL(string: url) else {
            completion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])))
            return
        }
        
        let request = URLRequest(url: url)
        
        let task = URLSession.shared.dataTask(with: request) { (data, response, error) in
            if let error = error {
                completion(.failure(error))
            } else if let data = data {
                completion(.success(data))
            }
        }
        
        task.resume()
    }
}
