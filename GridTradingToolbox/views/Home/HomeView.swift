//
//  Home.swift
//  GridTradingToolbox
//
//  Created by 陈敏杰 on 2024/1/2.
//

import SwiftUI
import SwiftData
import Combine

struct StrategyCardView: View {
    let strategy: Strategy
    let totalInvestment: Int
    
    var body: some View {
        VStack(alignment: .leading) {
            HStack {
                Image(systemName: "waveform.path.ecg")
                    .resizable()
                    .frame(width: 30, height: 30)
                    .foregroundColor(.blue)
                Text(strategy.name)
                    .font(.headline)
                    .foregroundColor(.black)
                    .lineLimit(1)
                    .truncationMode(.tail)
                Spacer()
                VStack(alignment: .trailing) {
                    Text("占比")
                        .font(.callout)
                        .foregroundColor(.gray)
                    Text(String(format: "%.2f", Utils.div(first: strategy.totalInvestment, last: totalInvestment) * 100.0) + "%")
                        .font(.callout)
                        .foregroundColor(.black)
                }
            }
            .padding([.horizontal, .top])
            
            Divider()
                .padding(.horizontal)
            
            HStack {
                VStack(alignment: .leading) {
                    Text("投入金额")
                        .font(.callout)
                        .foregroundColor(.gray)
                        .padding(.bottom, 0.1)
                    HStack(alignment: .firstTextBaseline) {
                        Text(strategy.displayTotalInvestment)
                            .bold()
                            .foregroundColor(.black)
                        Text("元")
                            .font(.caption)
                            .foregroundColor(.black)
                            .bold()
                    }
                }
                Spacer()
                VStack(alignment: .leading) {
                    Text("提款次数")
                        .font(.callout)
                        .foregroundColor(.gray)
                        .padding(.bottom, 0.1)
                    Text("\(strategy.soldGrids.count)")
                        .foregroundColor(.black)
                        .bold()
                }
                Spacer()
            }
            .padding()
        }
        .background(Color.white)
        .cornerRadius(20)
    }
}

struct HomeView: View {
    @Environment(\.modelContext) var modelContext;
    @EnvironmentObject var settings: Settings
    @EnvironmentObject var store: Store
    
    @Query(sort: \Strategy.createAt, order: .reverse) 
    private var strategies: [Strategy] = []
    
    @StateObject var viewModle = StocksPriceViewModel()
    
    @State private var searchText: String = "";
    @State private var totalInvestment = 0;
    @State var hideSensitiveInfo = false
    
    @State var showingMsg = false
    
    @State private var showMoreView = false
    
    @State private var showDeleteError = false
    @State private var errorMessage = ""
    
    var searchResults: [Strategy] {
        if searchText.isEmpty {
            return strategies.filter{
                $0.status != Strategy.StrategyStatus.archive.rawValue
            }
        } else {
            return strategies.filter {
                $0.name.localizedStandardContains(searchText) && $0.status != Strategy.StrategyStatus.archive.rawValue

            }
        }
    }
    
    var archivedStrategies: [Strategy] {
        strategies.filter{ $0.status == Strategy.StrategyStatus.archive.rawValue }
    }
    
    var body: some View {
        if #available(iOS 18.0, *) {
            NavigationStack {
                homeContent
            }
            .alert(isPresented: $showingMsg, content: {
                Alert(title: Text("存在未卖出的股份，无法归档"), dismissButton: .default(Text("确认")))
            })
            .alert("删除失败, 请联系开发者确认问题", isPresented: $showDeleteError) {
                Button("确定", role: .cancel) { }
            } message: {
                Text(errorMessage)
            }
            .task {
//                 settings.subscriptionMember = true
               await store.fetchActiveTransactions()
               if store.purchasedProducts.isEmpty {
                   settings.subscriptionMember = false
               }
            }
            .sheet(isPresented: $settings.showUpdate) {
                FeatureUpdateView()
            }
        } else {
            NavigationView {
                homeContent
            }
            .alert(isPresented: $showingMsg, content: {
                Alert(title: Text("存在未卖出的股份，无法归档"), dismissButton: .default(Text("确认")))
            })
            .alert("删除失败, 请联系开发者确认问题", isPresented: $showDeleteError) {
                Button("确定", role: .cancel) { }
            } message: {
                Text(errorMessage)
            }
            .task {
//                 settings.subscriptionMember = true
               await store.fetchActiveTransactions()
               if store.purchasedProducts.isEmpty {
                   settings.subscriptionMember = false
               }
            }
            .sheet(isPresented: $settings.showUpdate) {
                FeatureUpdateView()
            }
        }
    }

    @MainActor
    private var homeContent: some View {
        ZStack {
            Color(UIColor.secondarySystemBackground)
                .edgesIgnoringSafeArea(.all)
            ScrollView {
                SummaryCardView(strategies: strategies)
                
                LazyVStack {
                    if settings.subscriptionMember {
                        HStack {
                            Text("待触发网格")
                                .fontWeight(.bold)
                            Spacer()
                            if (!viewModle.sellGrids.isEmpty || !viewModle.buyGrids.isEmpty) {
                                NavigationLink(destination: RealtimeGridPriceView(strategies: strategies)) {
                                    HStack {
                                        Text("更多")
                                            .font(.caption)
                                            .foregroundColor(.gray)
                                        Image(systemName: "chevron.right")
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: 8, height: 8)
                                            .foregroundColor(.gray)
                                    }
                                }
                                // .simultaneousGesture(TapGesture().onEnded{
                                //     viewModle.cancelTimer()
                                // })
                            }
                        }
                        if viewModle.isLoading {
                            HStack {
                                Spacer()
                                ProgressView {
                                    Text("加载中...")
                                        .font(.callout)
                                }
                                .padding()
                                Spacer()
                            }
                            .padding(.vertical)
                            .background(.white)
                            .cornerRadius(20)
                            .padding(.bottom, 4)
                        } else if (!viewModle.sellGrids.isEmpty || !viewModle.buyGrids.isEmpty) {
                            HStack {
                                Spacer()
    //                                    if !viewModle.sellGrids.isEmpty {
                                if viewModle.nearestSellTrigerGrid != nil {
                                    NavigationLink(destination: StrategyView(strategy: strategies.first(where: { $0.id == viewModle.nearestSellTrigerGrid!.strategyId })!)) {
                                        TriggerGridCardView(stockGridPrice: viewModle.nearestSellTrigerGrid!, isSell: true, hideSensitiveInfo: $hideSensitiveInfo)
                                    }
                                    // .simultaneousGesture(TapGesture().onEnded{
                                    //     viewModle.cancelTimer()
                                    // })
                                    .foregroundColor(.black)
                                }
                                if viewModle.sellGrids.count > 0 && viewModle.buyGrids.count > 0 {
                                    Spacer()
                                }
                                if viewModle.nearestBuyTrigerGrid != nil {
                                    NavigationLink(destination: StrategyView(strategy: strategies.first(where: { $0.id == viewModle.nearestBuyTrigerGrid!.strategyId })!)) {
                                        TriggerGridCardView(stockGridPrice: viewModle.nearestBuyTrigerGrid!, isSell: false, hideSensitiveInfo: $hideSensitiveInfo)
                                    }
                                    // .simultaneousGesture(TapGesture().onEnded{
                                    //     viewModle.cancelTimer()
                                    // })
                                    .foregroundColor(.black)
                                }
                                Spacer()
                            }
                            .padding(.vertical)
                            .background(.white)
                            .cornerRadius(20)
                            .padding(.bottom, 4)
                        } else {
                            Text("暂无待触发网格")
                                .font(.caption)
                                .foregroundColor(.gray)
                                .padding()
                        }
                    }
                    
                    HStack {
                        Text("我的策略(\(strategies.filter{$0.status != Strategy.StrategyStatus.archive.rawValue}.count))")
                            .fontWeight(.bold)
                        Spacer()
                        if archivedStrategies.count > 0 {
                            NavigationLink(destination: ArchiveStrategyListView()) {
                                HStack {
                                    Text("归档策略")
                                        .font(.caption)
                                        .foregroundColor(.gray)
                                    Image(systemName: "chevron.right")
                                        .resizable()
                                        .scaledToFit()
                                        .frame(width: 8, height: 8)
                                        .foregroundColor(.gray)
                                }
                            }
                        }
                    }
                    if strategies.count == 0 {
                        VStack(spacing: 4) {
                            Image("no-data")
                                .resizable()
                                .scaledToFit()
                                .frame(height: 180)
                                .padding([.horizontal])
                                .padding(.bottom, -30)
                                .padding(.top, -20)
                            Text("暂无策略")
                                .font(.callout)
                                // .bold()
                            Text("创建你的网格交易策略，享受波动带来的收益~")
                                .font(.caption)
                                .foregroundColor(.gray)
                                .padding(.top, 4)
                            NavigationLink(destination: CreateStrategyView()) {
                            Text("新建策略")
                                .font(.system(size: 15))
                                // .bold()
                                .frame(width: 100, height: 32)
                                .foregroundColor(.white)
                                .background(.blue)
                                .cornerRadius(20)
                            }
                            .padding(.top, 16)
                            NavigationLink(destination: {
                                UserGuideView()
                            }, label: {
                                Text("使用指南")
                                    .font(.footnote)
                            })
                            .padding(.top, 8)
                        }
                        
                    } else {
                        ForEach(searchResults, id: \.id) { strategy in
                            NavigationLink(destination: StrategyView(strategy: strategy)) {
                                StrategyCardView(strategy: strategy, totalInvestment: totalInvestment)
                            }
                            .contextMenu {
                                Button(action: {
                                    if !strategy.archive() {
                                        showingMsg = true
                                    }
                                    try? modelContext.save()
                                }, label: {
                                    Text("归档")
                                })
                                Button(action: {
                                    deleteStrategy(strategy)
                                }, label: {
                                    Text("删除")
                                })
                            }
                        }
                    }
                    
                }
                .padding()
                
            }
        }
        .navigationTitle("策略")
        // .navigationDestination(for: Strategy.self) { item in
        //     StrategyView(strategy: item)
        // }
        .searchable(text: $searchText, prompt: "策略名称")
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                NavigationLink(destination: MoreView()) {
                    Image(systemName: "ellipsis")
                }
            }
            ToolbarItem(placement: .navigationBarTrailing) {
                if settings.subscriptionMember {
                    NavigationLink(destination: IndexView()) {
                        Image(systemName: "chart.xyaxis.line")
                    }
                } else {
                    NavigationLink(destination: ProMemberView()) {
                        Image(systemName: "chart.xyaxis.line")
                    }
                }
            }
            ToolbarItem(placement: .navigationBarTrailing) {
                NavigationLink(destination: GridCalendarView()) {
                    Image(systemName: "calendar")
                }
            }
            ToolbarItem(placement: .navigationBarTrailing) {
                NavigationLink(destination: CreateStrategyView()) {
                    Image(systemName: "plus")
                }
            }
        }
        .onAppear{
            settings.checkForNewFeatures()
            Task {
                if settings.subscriptionMember {
                    viewModle.setupTimer(strategies: strategies)
                }
                
                var tInvestment = 0
                for strategy in strategies {
                    tInvestment += strategy.totalInvestment
                }
                totalInvestment = tInvestment;
            }
        }
        .onDisappear{
            viewModle.cancelTimer()
        }
    }
    
    @MainActor
    func deleteStrategy(_ strategy: Strategy) {
        viewModle.cancelTimer()
        viewModle.removeStrategy(strategyId: strategy.id)

        // 清理灵活交易日志
        if let flexibleLogs = strategy.flexibleTradeLog {
            for log in flexibleLogs {
                modelContext.delete(log)
            }
        }
        
        // 先清理所有关联数据
        if let grids = strategy.grids {
            for grid in grids {
                // 清理通知
                grid.cancelNotification()
                
                // 清理交易日志
                if let logs = grid.tradeLogs {
                    for log in logs {
                        modelContext.delete(log)
                    }
                }
                
                // 清理网格
                modelContext.delete(grid)
            }
        }

        let remainingStrategies = strategies.filter{ $0.id.uuidString != strategy.id.uuidString }
        
        // 最后删除策略
        modelContext.delete(strategy)
        
        do {
            try modelContext.save()
            // strategies = fetchStrategies()
            viewModle.setupTimer(strategies: remainingStrategies)
        } catch {
            errorMessage = "删除策略失败: \(error.localizedDescription)"
            showDeleteError = true
            print("删除策略失败: \(error)")
        }
    }
}

//#Preview {
//    HomeView()
//}
