//
//  SummaryCardView.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2025/1/15.
//

import SwiftUI

struct SummaryCardView: View {
    private var strategies: [Strategy]
    
    private var totalProfit = 0
    private var totalInvestment = 0
    private var maxInvestment = 0
    private var percent = 0

    @State
    var showAlert: Bool = false
    @State
    var alertTitle: String = ""
    @State
    var alertMessage: String = ""
    
    init(strategies: [Strategy]) {
        self.strategies = strategies
        
        let results = strategies.reduce((profit: 0, investment: 0, maxInvestment: 0)) { result, strategy in
            if strategy.status != Strategy.StrategyStatus.archive.rawValue {
                return (
                    result.profit + strategy.totalProfit,
                    result.investment + strategy.totalInvestment,
                    result.maxInvestment + strategy.maxInvestment
                )
            } else {
                return (
                    result.profit + (strategy.profit ?? 0),
                    result.investment,
                    result.maxInvestment
                )
            }
        }
        
        self.totalProfit = results.profit
        self.totalInvestment = results.investment
        self.maxInvestment = results.maxInvestment
        self.percent = maxInvestment != 0 ? 10000 * totalInvestment / maxInvestment : 0
    }
    
    var body: some View {
        VStack {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text("累计收益")
                            .font(.system(size: 14))
                        Image(systemName: "info.circle")
                            .font(.system(size: 14))
                            .foregroundColor(.gray)
                            .onTapGesture {
                                alertTitle = "累计收益"
                                alertMessage = "仅统计了出网部分收益"
                                showAlert = true
                            }
                            .padding(.leading, -8)
                    }
                    
                    Text("\(Utils.displayDecimal(decimal: Double(totalProfit) / 1000.0))")
                        .font(.custom("D-DIN", size: 26))
                        .bold()
                        .foregroundColor(.red)
                }
                
                Spacer()
            }
            .padding(16)
            
            HStack {
                Text("投入比例")
                    .font(.system(size: 14))
                Spacer()
                Text(Utils.trimTrailingZeros(from: String(format: "%.2f", Double(percent) / 100.0)) + "%")
                    .font(.custom("D-DIN", size: 16))
            }.padding(.horizontal)
            GeometryReader { geometry in
                VStack {
                    ZStack {
                        Capsule()
                            .foregroundColor(.gray.opacity(0.25))
                            .frame(width: geometry.size.width, height: 4)
                        HStack {
                            Capsule()
                                .foregroundColor(.blue.opacity(0.75))
                                .frame(width: min(geometry.size.width * Utils.div(first: totalInvestment, last: maxInvestment), geometry.size.width), height: 4)
                            Spacer()
                        }
                    }
                    .padding(.bottom, 4)

                    HStack {
                        Text("已投入")
                            .font(.custom("D-DIN", size: 12))
                            .foregroundColor(.gray)
                        Text(Utils.displayDecimal(decimal: Double(totalInvestment) / 1000.0))
                            .font(.custom("D-DIN", size: 14))
                            .foregroundColor(.gray)
                            .padding(.leading, 1)
                        Spacer()
                        Text("最大投入")
                            .font(.custom("D-DIN", size: 12))
                            .foregroundColor(.gray)
                        Text(Utils.displayDecimal(decimal: Double(maxInvestment) / 1000.0))
                            .font(.custom("D-DIN", size: 14))
                            .foregroundColor(.gray)
                            .padding(.leading, 1)
                        Spacer()
                    }
                    
                }
                
            }.frame(height: 24)
                .padding([.horizontal])
                .padding(.bottom, 16)
                .padding(.top, -4)
            
        }
        .background(Color.white)
        .cornerRadius(20)
        .padding(.horizontal)
        .alert(isPresented: $showAlert) {
            Alert(title: Text(alertTitle).font(.system(size: 20)), message: Text(alertMessage).font(.system(size: 17)), dismissButton: .default(Text("确定")))
        }
    }
}

// #Preview {
//     SummaryCardView()
// }
