//
//  UserGuideView.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/4/3.
//

import SwiftUI

struct UserGuideView: View {
    var body: some View {
        List {
            Section("网格策略") {
                Link("网格交易策略之一", destination: URL(string: "https://mp.weixin.qq.com/s/uxktt5ZpNo03FpQQX-aG7g")!)
                Link("网格交易策略之二", destination: URL(string: "https://mp.weixin.qq.com/s/-czfqGvxkDcay_tSI1jv5g")!)
                Link("网格交易策略之三", destination: URL(string: "https://mp.weixin.qq.com/s/8pRKsjiQSZzrmH-uWCkRLQ")!)
            }
            
            Section("基础") {
                Link("创建&删除网格策略", destination: URL(string: "https://h123qzrdps.feishu.cn/docx/Vg3md2mMVoVOLNxMBvJcdXNAnjg?from=from_copylink")!)
                Link("记录&编辑&删除买卖数据", destination: URL(string: "https://h123qzrdps.feishu.cn/docx/KQVOdDxfiod8WKxxbUpcUsEwndR?from=from_copylink")!)
                Link("卖出预留利润", destination: URL(string: "https://h123qzrdps.feishu.cn/docx/YWVedsO77okeY6xXP45cYmVIndg?from=from_copylink")!)
                Link("查看策略统计信息", destination: URL(string: "https://h123qzrdps.feishu.cn/docx/CA3AdMhKxoXuudxVbiOc11W3nhe?from=from_copylink")!)
            }
            
            Section("子策略") {
                Link("保留利润", destination: URL(string: "https://h123qzrdps.feishu.cn/docx/QAr9dhNdlo22BpxddEDcgNG6nEd?from=from_copylink")!)
                Link("中网大网", destination: URL(string: "https://h123qzrdps.feishu.cn/docx/Oyk9dF8aIo1VRGxcGvocFgeSnbe?from=from_copylink")!)
                Link("底仓计息", destination: URL(string: "https://h123qzrdps.feishu.cn/docx/RQridmptKolUONxMGfMcLb3Fnyh?from=from_copylink")!)
            }
        }
        .navigationTitle("使用指南")
    }
}

#Preview {
    UserGuideView()
}
