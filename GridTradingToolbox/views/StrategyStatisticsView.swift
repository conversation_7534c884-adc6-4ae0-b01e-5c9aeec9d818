//
//  StrategyStatisticsView.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/3/31.
//

import SwiftUI

struct StrategyStatisticsView: View {
    @Environment(\.displayScale) var displayScale

    @Bindable var strategy: Strategy
    @State var hideSensitiveInfo: Bool = false
    @State var showAlert: Bool = false
    private var realTimePrice: Double? = nil
    
    init(strategy: Strategy, realTimePrice: Double? = nil) {
        self.strategy = strategy
        self.realTimePrice = realTimePrice
    }

    var body: some View {
        Form {
            Section {
                Toggle("隐藏敏感信息", isOn: $hideSensitiveInfo)
                HStack {
                    Spacer()
                    VStack {
                        StrategySummaryPostView(strategy: strategy, hideSensitiveInfo: $hideSensitiveInfo, profit: strategy.realTotalProfit, avgHoldDays: strategy.avgHoldDaysOfGrids, realTimePrice: realTimePrice)
                            .frame(width: 250, height: 450)
                            .contextMenu {
                                Button(action: {
                                    render()
                                    showAlert = true
                                }, label: {
                                    Text("保存")
                                })
                            }
                        Text("长按图片保存")
                            .font(.footnote)
                            .foregroundColor(.gray)
                    }
                    Spacer()
                }
            }
        }
        .alert(isPresented: $showAlert, content: {
            Alert(title: Text("图片保存成功~"), dismissButton: .default(Text("确认")))
        })
        .navigationTitle("统计信息")
    }
    
    @MainActor func render() {
        let renderer = ImageRenderer(content: StrategySummaryPostView(strategy: strategy, hideSensitiveInfo: $hideSensitiveInfo, profit: strategy.totalProfit, avgHoldDays: strategy.avgHoldDaysOfGrids, realTimePrice: realTimePrice)
            .frame(width: 250, height: 450))

        renderer.scale = displayScale
        
        UIImageWriteToSavedPhotosAlbum(renderer.uiImage!, nil, nil, nil)
    }
}

//#Preview {
//    StrategyStatisticsView()
//}
