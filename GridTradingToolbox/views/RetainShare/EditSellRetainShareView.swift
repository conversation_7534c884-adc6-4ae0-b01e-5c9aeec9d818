//
//  EditSellRetainShareView.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/10/28.
//

import SwiftUI

struct EditSellRetainShareView: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.modelContext) private var modelContext

    @Bindable var tradeLog: FlexibleTradeLog

    @State var sellPrice: String
    @State var sellShares: Int
    @State var sellDate: Date

    var retainShares: Int
    // var minShares: Int

    init(tradeLog: FlexibleTradeLog) {
        self.tradeLog = tradeLog
        self._sellPrice = State(initialValue: tradeLog.displayTradePrice)
        self._sellShares = State(initialValue: tradeLog.tradeShares)
        self._sellDate = State(initialValue: tradeLog.tradeAt)
        self.retainShares = tradeLog.strategy!.getRetainShares() + tradeLog.tradeShares
    }

    var body: some View {
        NavigationStack {
            VStack {
                Form {
                Section() {
                    HStack {
                        Text("卖出价格")
                        TextField("", text: $sellPrice)
                            .multilineTextAlignment(.trailing)
                            .keyboardType(.numbersAndPunctuation)
                            .onChange(of: sellPrice, initial: false) { oldValue, newValue in
                                if newValue.count == 0 {
                                    return;
                                }
                                let result = Utils.checkNumberFormat(number: newValue)
                                if !result {
                                    sellPrice = oldValue
                                }
                            }
                            .submitLabel(.done)
                    }
                    
                    Stepper(value: $sellShares, in: 100...retainShares, step: 100) {
                        HStack {
                            Text("卖出股数")
                            Spacer()
                            Text(String(sellShares))
                        }
                    }
                    
                    HStack {
                        Text("卖出日期")
                        Spacer()
                        DatePicker("", selection: $sellDate, in: ...Date.now, displayedComponents: .date)
                            .environment(\.locale, Locale.init(identifier: "zh_CN"))
                    }
                }
                
                Section {
                    HStack {
                        Spacer()
                        RetainSharesTradePost(soldPrice: $sellPrice, soldDate: $sellDate, soldShares: $sellShares)
                            .frame(width: 250, height: 350)
                        Spacer()
                    }
                }
                }
            }
            .navigationTitle("编辑预留利润卖出")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("返回") {
                        self.presentationMode.wrappedValue.dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        tradeLog.tradePrice = Utils.times1000Round(price: sellPrice)
                        tradeLog.tradeShares = sellShares
                        tradeLog.strategy!.realTotalProfitAmount = tradeLog.strategy!.calculateRealTotalProfit()
                        tradeLog.strategy!.setRetainShares(tradeLog.strategy!.calculateRetainShares())
                        tradeLog.tradeAt = sellDate
                        try? modelContext.save()
                        self.presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
}

// #Preview {
//     EditSellRetainShareView()
// }
