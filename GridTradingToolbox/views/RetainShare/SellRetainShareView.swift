//
//  SellRetainShareView.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/3/29.
//

import SwiftUI

struct SellRetainShareView: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.modelContext) var modelContext;

    @Bindable var strategy: Strategy
    
    @Binding var confettiFlag: Int

    @State var sellPrice = ""
    @State var sellShares = 0
    @State var sellDate = Date()
    
    @State var buttonDisable = false
    @State var showingAlert = false
    @State var alertMsg = "数据异常，请联系开发者"

    var retainShares: Int
    var minShares: Int
    
    init(strategy: Strategy, confettiFlag: Binding<Int>) {
        self.strategy = strategy;
        self._confettiFlag = confettiFlag;
        self._sellShares = State(initialValue: strategy.getRetainShares())
        let rs = strategy.getRetainShares()
        self.retainShares = rs
        if rs > 0 {
            self.minShares = 100
        } else {
            self.minShares = 0
        }
    }

    
    var body: some View {
        VStack {
            Form {
                Section() {
                    HStack {
                        Text("卖出价格")
                        TextField("", text: $sellPrice)
                            .multilineTextAlignment(.trailing)
                            .keyboardType(.numbersAndPunctuation)
                            .onChange(of: sellPrice, initial: false) { oldValue, newValue in
                                if newValue.count == 0 {
                                    return;
                                }
                                let result = Utils.checkNumberFormat(number: newValue)
                                if !result {
                                    sellPrice = oldValue
                                }
                            }
                            .submitLabel(.done)
                    }
                    
                    Stepper(value: $sellShares, in: minShares...retainShares, step: 100) {
                        HStack {
                            Text("卖出股数")
                            Spacer()
                            Text(String(sellShares))
                        }
                    }
//                    .disabled(true)
                    
                    HStack {
                        Text("卖出日期")
                        Spacer()
                        DatePicker("", selection: $sellDate, in: ...Date.now, displayedComponents: .date)
                            .environment(\.locale, Locale.init(identifier: "zh_CN"))
                    }
                }
                
                Section {
                    HStack {
                        Spacer()
                        RetainSharesTradePost(soldPrice: $sellPrice, soldDate: $sellDate, soldShares: $sellShares)
                            .frame(width: 250, height: 350)
                        Spacer()
                    }
                }
            }
        }
        .navigationTitle("预留利润卖出")
        .navigationBarTitleDisplayMode(.inline)
        .alert(isPresented: $showingAlert, content: {
            Alert(title: Text(alertMsg), dismissButton: .default(Text("确认")))
        })
        .toolbar {
            Button("保存") {
                buttonDisable = true;
                let result = strategy.sellRetainShares(price: sellPrice, shares: sellShares, date: sellDate);
                if result.0 {
                    try? modelContext.save()
                    confettiFlag += 1
                    self.presentationMode.wrappedValue.dismiss()
                } else {
                    alertMsg = result.1
                    showingAlert = true
                    buttonDisable = false
                }
            }
            .disabled(buttonDisable || sellPrice.isEmpty)
        }
        
    }
    
    
}

//#Preview {
//    SellRetainShareView()
//}
