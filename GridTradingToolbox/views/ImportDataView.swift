//
//  ImportDataView.swift
//  GridTradingToolbox
//
//  Created by 陈敏杰 on 2024/1/28.
//

import SwiftUI
import PopupView

struct ImportDataView: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>;
    @Environment(\.modelContext) var modelContext;
    
    @State private var key = ""
    @State private var isLoading = false

    @State private var showingPopup = false
    
    @State var msg = "";
    
    var body: some View {
        Form {
            Section {
                HStack {
                    Text("数据 Key")
                    TextField("小程序数据导入Key", text: $key)
                        .multilineTextAlignment(.trailing)
                }
            }
            
            Section {
                Button(action: {
                    loadData()
                }, label: {
                    HStack {
                        Spacer()
                        if isLoading {
                            ProgressView()
                        }
                        Text("导入")
                        Spacer()
                    }
                })
            }
        }
        .disabled(isLoading)
        .alert(isPresented: $showingPopup, content: {
            Alert(title: Text(msg), dismissButton: .default(Text("确认")))
        })
//        .toolbar(.hidden, for: .tabBar)
        .navigationTitle("数据导入")
        .navigationBarTitleDisplayMode(.inline)
    }
    
    func loadData() {
        isLoading = true
            fetchData() { result in
                DispatchQueue.main.async {
                    isLoading = false
                    showingPopup = true
                    switch result {
                    case .success(let data):
                        let decoder = JSONDecoder()
                        let dateFormatter = DateFormatter()
                        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
                        decoder.dateDecodingStrategy = .formatted(dateFormatter)
                        do {
                            let response = try decoder.decode(Response.self, from: data)
                            for syncStrategy in response.result {
                                let strategy = Strategy.from(syncStrategy: syncStrategy)
                                modelContext.insert(strategy)
                                for grid in syncStrategy.grids {
                                    let nGrid = TradeGrid.from(syncGrid: grid, strategy: strategy)
                                    strategy.grids!.append(nGrid)
                                    for log in grid.tradeLogs {
                                        let tLog = TradeLog.from(syncTradeLog: log, grid: nGrid)
//                                        modelContext.insert(tLog)
                                        nGrid.tradeLogs!.append(tLog)
                                    }
                                }
                                
                                for grid in syncStrategy.soldGrids {
                                    let nGrid = TradeGrid.from(syncGrid: grid, strategy: nil)
                                    strategy.grids!.append(nGrid)
                                    if grid.tradeLogs.count == 2 {
                                        nGrid.strategy = strategy
                                        for log in grid.tradeLogs {
                                            let tLog = TradeLog.from(syncTradeLog: log, grid: nGrid)
                                            nGrid.tradeLogs!.append(tLog)
                                        }
                                    } else {
                                        modelContext.delete(nGrid)
                                    }
                                }
                            }
                            try? modelContext.save()
                            msg = "导入成功"
                            key = ""
                        } catch let error {
                            print("decode error: \(error)")
                            msg = "导入数据解析失败，请联系开发者"
                        }

                    case .failure(let error):
                        print("\(error)")
                        msg = "导入失败，请稍后重试"
                    }
                }
            }
        }
    
    func fetchData(completion: @escaping (Result<Data, Error>) -> Void) {
//        let url = "http://localhost:8080/api/v1/strategies/app"
        let url = "https://dongxibuduo.com/sea/api/v1/strategies/app"
        guard let url = URL(string: url) else {
            completion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])))
            return
        }
        
        var request = URLRequest(url: url)
        request.setValue(key, forHTTPHeaderField: "GSA-Session")
        
        let task = URLSession.shared.dataTask(with: request) { (data, response, error) in
            if let error = error {
                completion(.failure(error))
            } else if let data = data {
                completion(.success(data))
            }
        }
        
        task.resume()
    }
}

//#Preview {
//    ImportDataView()
//}
