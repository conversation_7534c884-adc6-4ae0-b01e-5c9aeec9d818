//
//  SellGridView.swift
//  GridTradingToolbox
//
//  Created by 陈敏杰 on 2024/1/21.
//
import SwiftUI
import SwiftData
import PopupView

import SwiftUI

extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

struct SellGridView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.displayScale) var displayScale
    
    @Query(sort: \StockAccount.createAt, order: .reverse) private var accounts: [StockAccount]
    
    @Bindable var strategy: Strategy
    @Bindable var grid: TradeGrid
    
    @Binding var confettiFlag: Int
    
    @State var showingPopup = false
    
    @State var showErrorMsg = false
    @State var errorMsg = ""
    
    @State var sellPrice = ""
    @State var sellShares = ""
    @State var sellDate = Date()
    var isEditMode = false
    @State var buttonDisable = false
    @State var hideSensitiveInfo = false
    @State var moodInex = "default"
    @State var note = ""
    
    @FocusState private var isSharesFocused: Bool
    
    var curStockAcc: StockAccount? {
        if grid.accountId == nil || grid.accountId == "" || grid.accountId == "default" {
            return nil
        } else {
            return accounts.first(where: { acc in
                acc.id == grid.accountId!
            })
        }
    }
            
    var body: some View {
        NavigationStack {
            VStack {
                Form {
                    Section(footer: showErrorMsg ? Text(errorMsg).foregroundColor(.red) : nil) {
                        HStack {
                            Text("卖出价格")
                            TextField("", text: $sellPrice)
                                .multilineTextAlignment(.trailing)
                                .keyboardType(.numbersAndPunctuation)
                                .onChange(of: sellPrice, initial: false) { oldValue, newValue in
                                    if newValue.count == 0 {
                                        return;
                                    }
                                    let result = Utils.checkNumberFormat(number: newValue)
                                    if !result {
                                        sellPrice = oldValue
                                    }
                                }
                                .submitLabel(.done)
                        }
                        
                        HStack {
                            Text("卖出股数")
                            TextField("", text: $sellShares)
                                .multilineTextAlignment(.trailing)
                                .keyboardType(.numbersAndPunctuation)
                                .focused($isSharesFocused)
                                .onChange(of: isSharesFocused) { focused in
                                    if !focused {
                                        sellShares = Utils.formatShares(sellShares)
                                    }
                                }
                                .submitLabel(.done)
                        }
                        
//                        Stepper(value: $sellShares, in: 0...grid.buyTradeLog!.tradeShares, step: 100) {
//                            HStack {
//                                Text("卖出股数")
//                                Spacer()
//                                Text(String(sellShares))
//                            }
//                        }
                        
                        HStack {
                            Text("卖出日期")
                            Spacer()
                            DatePicker("", selection: $sellDate, in: ...Date.now, displayedComponents: .date)
                                .environment(\.locale, Locale.init(identifier: "zh_CN"))
                        }
                    }
                    
                    Section {
                        Toggle("隐藏敏感信息", isOn: $hideSensitiveInfo)
                        if curStockAcc != nil {
                            HStack {
                                Text("交易佣金")
                                Spacer()
                                Text(Utils.displayPrice(price: curStockAcc!.calTrxFee(amount: (sellPrice as NSString).doubleValue * (sellShares as NSString).doubleValue), num: 2))
                            }
                        }
                        HStack {
                            Spacer()
                            VStack {
                                Post(soldPrice: $sellPrice, soldDate: $sellDate, soldShares: Int(Utils.formatShares(sellShares))!, hideSensitiveInfo: $hideSensitiveInfo, grid: grid)
                                    .frame(width: 250, height: 350)
                                    .contextMenu {
                                        Button(action: {
                                            render()
                                            showingPopup = true
                                        }, label: {
                                            Text("保存")
                                        })
                                    }
                                Text("长按图片保存")
                                    .font(.footnote)
                                    .foregroundColor(.gray)
                            }
                            Spacer()
                        }
                    }
                    
                    Section(header: Text("投资记录")) {
                        Picker("心态", selection: $moodInex) {
                            Text("🤑").tag("happy")
                            Text("😊").tag("smile")
                            Text("😶").tag("default")
                            Text("😱").tag("sad")
                            Text("😭").tag("cry")
                        }
                        TextField("记录当前的心理情绪与投资逻辑", text: $note, axis: .vertical)
                            .lineLimit(3)
                    }
                }
            }
            .navigationTitle(isEditMode ? "编辑卖出记录" : "记录卖出")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("返回") {
                        self.presentationMode.wrappedValue.dismiss()
                    }
                    .disabled(buttonDisable)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        let checkRes = checkSellParams()
                        if !checkRes {
                            showErrorMsg = true
                        } else {
                            recordSellOut()
                            buttonDisable = true
                            confettiFlag += 1
                            self.presentationMode.wrappedValue.dismiss()
                        }
                    }
                    .disabled(buttonDisable)
                }
            }
            .alert(isPresented: $showingPopup, content: {
                Alert(title: Text("图片保存成功~"), dismissButton: .default(Text("确认")))
            })
        }
    }
    
    init(strategy: Strategy, grid: TradeGrid, confettiFlag: Binding<Int>, isEditMode: Bool = false) {
        self.strategy = strategy
        self.grid = grid
        _confettiFlag = confettiFlag
        self.isEditMode = isEditMode
        self.buttonDisable = false
        if isEditMode {
            _sellPrice = State(initialValue: grid.sellTradeLog!.displayTradePrice)
            _sellShares = State(initialValue: String(grid.sellTradeLog!.tradeShares))
            _sellDate = State(initialValue: grid.sellTradeLog!.tradeAt)
            _moodInex = State(initialValue: grid.sellTradeLog!.mood ?? "default")
            _note = State(initialValue: grid.sellTradeLog!.note ?? "")
        } else {
            _sellPrice = State(initialValue:
                                Utils.trimTrailingZeros(from: grid.getSellPriceWithInterest(calInterest: strategy.calculateInterest, period: strategy.interestPeriod, interest: strategy.interest)))
            _sellShares = State(initialValue: String(grid.theoreticalSellShares))
        }
    }
    
    // pass -> true, fail -> false
    private func checkSellParams() -> Bool {
        sellShares = Utils.formatShares(sellShares)
        let sellSharesValue = Int(sellShares)!
        if sellSharesValue > grid.buyTradeLog!.tradeShares {
            errorMsg = "卖出股数应小于等于当前买入股数"
            return false
        }
        if Calendar.current.compare(sellDate, to: grid.buyTradeLog!.tradeAt, toGranularity: .day) == .orderedAscending {
            errorMsg = "卖出日期应大于买入日期"
            return false
        }
        return true
    }

    @MainActor
    private func recordSellOut() {
        let sellSharesValue = Int(sellShares)!
        let tradePrice = Utils.times1000Round(price: sellPrice)
        if isEditMode {
            let tradeLog = grid.sellTradeLog!
            
            tradeLog.tradePrice = tradePrice
            tradeLog.tradeShares = sellSharesValue
            tradeLog.tradeAt = sellDate
            tradeLog.mood = moodInex
            tradeLog.note = note
            
            grid.holdShares = grid.buyTradeLog!.tradeShares - tradeLog.tradeShares
            grid.strategy!.totalProfitAmount = grid.strategy!.calculateTotalProfit()
            grid.strategy!.realTotalProfitAmount = grid.strategy!.calculateRealTotalProfit()
            grid.strategy!.setRetainShares(grid.strategy!.calculateRetainShares())
            
            // 强制保存并处理错误
            do {
                try modelContext.save()
                print("更新成功 - 日期: \(grid.sellTradeLog!.tradeAt)")
            } catch {
                print("保存失败: \(error)")
            }
        } else {
            let tradeLog = TradeLog(grid: grid, tradeType: TradeLog.TradeType.sell.rawValue, tradeShares: sellSharesValue, tradePrice: tradePrice, tradeAt: sellDate, mood: moodInex, note: note)
            let index = strategy.grids!.firstIndex(of: grid)!
            grid.tradeLogs!.append(tradeLog)
            grid.holdShares -= sellSharesValue
            grid.cancelNotification()
            grid.strategy!.totalInvestmentAmount = grid.strategy!.calculateTotalInvestment()
            grid.strategy!.totalProfitAmount = grid.strategy!.calculateTotalProfit()
            grid.strategy!.setRetainShares(grid.strategy!.calculateRetainShares())
            grid.strategy!.realTotalProfitAmount = grid.strategy!.calculateRealTotalProfit()
            strategy.grids!.remove(at: index)
            strategy.grids!.insert(grid, at: index)
            try? modelContext.save()
            
            // Task {
            //     if let dh = await dataHandler() {
            //         try await dh.sellGrid(tradeShares: sellSharesValue, tradePrice: tradePrice, tradeAt: sellDate, mood: moodInex, note: note, grid: grid, strategy: strategy)
            //         let index = strategy.grids!.firstIndex(of: grid)
            //         print("index: \(index)")
            //         if index != nil {
            //             strategy.grids!.remove(at: index!)
            //             strategy.grids!.insert(grid, at: index!)
            //         }
            //     }
            // }
        }
        // try? modelContext.save()
    }
    
    @MainActor func render() {
        let renderer = ImageRenderer(content: Post(soldPrice: $sellPrice, soldDate: $sellDate, soldShares: Int(Utils.formatShares(sellShares))!, hideSensitiveInfo: $hideSensitiveInfo, grid: grid)
            .frame(width: 250, height: 350))

        renderer.scale = displayScale
        
        UIImageWriteToSavedPhotosAlbum(renderer.uiImage!, nil, nil, nil)
    }
}

//#Preview {
//    SellGridView()
//}
