//
//  ProMemberView.swift
//  GridTradingToolbox
//
//  Created by 陈敏杰 on 2024/2/17.
//

import SwiftUI
import StoreKit
import PopupView
import ConfettiSwiftUI

struct ProMemberView: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var store: Store
    @EnvironmentObject var settings: Settings
    
    @State var isLoading = false
    
    @State private var showingPopup = false
    @State private var isShowingError = false
    @State private var confettiFlag = 0
    
    @State var msg = "";
    
    @State var selectedItem = "year"
    
    var monthProduct: Product? {
        store.subscriptionProducts.first(where: {$0.id == "gtt.subscription.month"}) ?? nil
    }
    
    var yearProduct: Product? {
        store.subscriptionProducts.first(where: {$0.id == "gtt.subscription.year"}) ?? nil
    }
    
    var body: some View {
        NavigationStack {
            ZStack {
                ScrollView {
                    VStack(alignment: .leading) {
                        HStack {
                            Image(systemName: "clock")
                                .frame(width: 24, height: 24)
                                .foregroundColor(.yellow)
                                .cornerRadius(5)
                                .padding(.trailing, 5)
                            Link(destination: URL(string: "https://h123qzrdps.feishu.cn/docx/MV9ydCCTJo6oIVxHoGecIdamnVd?from=from_copylink")!, label: {
                                Text("实时行情")
                                    .underline()
                                    .foregroundStyle(.black)
                            })
                        }
                        .padding(.horizontal)

                        HStack {
                            Image(systemName: "chart.xyaxis.line")
                                .frame(width: 24, height: 24)
                                .foregroundColor(.yellow)
                                .cornerRadius(5)
                                .padding(.trailing, 5)

                            Link(destination: URL(string: "https://h123qzrdps.feishu.cn/docx/UWebdQVjHoRjDPxKQIJcSayqnLf")!) {
                                Text("指数估值信息")
                                    .underline()
                                    .foregroundStyle(.black)
                            }
                        }
                        .padding(.horizontal)
                        
                        HStack {
                            Image(systemName: "chart.dots.scatter")
                                .frame(width: 24, height: 24)
                                .foregroundColor(.yellow)
                                .cornerRadius(5)
                                .padding(.trailing, 5)

                            Link(destination: URL(string: "https://h123qzrdps.feishu.cn/docx/QNDad0McFoUyudxboGVclWZ6nR7?from=from_copylink")!) {
                                Text("交易点位图表")
                                    .underline()
                                    .foregroundStyle(.black)
                            }
                        }
                        .padding(.horizontal)

                        HStack {
                            Image(systemName: "arrow.triangle.branch")
                                .frame(width: 24, height: 24)
                                .foregroundColor(.yellow)
                                .cornerRadius(5)
                                .padding(.trailing, 5)

                            Link(destination: URL(string: "https://h123qzrdps.feishu.cn/docx/RQridmptKolUONxMGfMcLb3Fnyh?from=from_copylink")!) {
                                Text("子策略 - 底仓计息")
                                    .underline()
                                    .foregroundStyle(.black)
                            }
                        }
                        .padding(.horizontal)

//                        HStack {
//                            Image(systemName: "bell.slash")
//                                .frame(width: 24, height: 24)
//                                .foregroundColor(.yellow)
//                                .cornerRadius(5)
//                                .padding(.trailing, 5)
//                            Text("没有付费提示")
//                        }
//                        .padding(.horizontal)
//                        .padding(.bottom)
                        
                        if monthProduct != nil {
                            HStack {
                                Text(monthProduct!.displayName)
                                    .fontWeight(.semibold)
                                    .padding()
                                
                                Spacer()
                                
                                if store.purchasedProducts.contains(monthProduct!.id) {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(.yellow)
                                        .padding()
                                } else {
                                    Text(monthProduct!.displayPrice)
                                        .font(.footnote)
                                        .padding()
                                }
                            }
                            .background(Color(UIColor.secondarySystemBackground))
                            .clipShape(RoundedRectangle(cornerRadius: 16, style: .continuous))
                            .overlay{
                                if selectedItem == "month" {
                                    RoundedRectangle(cornerRadius: 16)
                                                .stroke(.yellow, lineWidth: 2)
                                }
                            }
                            .padding()
                            .onTapGesture {
                                selectedItem = "month"
                            }
                        }
                        
                        if yearProduct != nil {
                            HStack {
                                Text(yearProduct!.displayName)
                                    .fontWeight(.semibold)
                                    .padding()
                                Text("限时特惠")
                                    .font(.footnote)
                                    .foregroundStyle(.gray)
                                
                                Spacer()
                                
                                if store.purchasedProducts.contains(yearProduct!.id) {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(.yellow)
                                        .padding()
                                } else {
                                    Text(yearProduct!.displayPrice)
                                        .font(.footnote)
                                        .padding()
                                }
                            }
                            .background(Color(UIColor.secondarySystemBackground))
                            .clipShape(RoundedRectangle(cornerRadius: 16, style: .continuous))
                            .overlay{
                                if selectedItem == "year" {
                                    RoundedRectangle(cornerRadius: 16)
                                                .stroke(.yellow, lineWidth: 2)
                                }
                            }
                            .padding(.horizontal)
                            .padding(.bottom)
                            .onTapGesture {
                                selectedItem = "year"
                            }
                        }
                        
                        Text("订阅须知")
                            .font(.caption)
                            .foregroundStyle(.gray)
                            .padding(.top)
                            .padding(.horizontal)
                        Text("订阅即视为已同意《使用条款》与《隐私协议》，订阅后除非是在当前订购期限结束前提前至少24小时取消续订，你的月缴或年缴套餐将以相同的期限自动续订。订购可随时在 App Store 取消，无需任何额外费用。取消后，你的套餐将在当前订购期限结束时终止。")
                            .font(.caption2)
                            .foregroundStyle(.gray)
                            .padding(.horizontal)
                            .padding(.vertical, 4)
                        HStack {
                            Link(destination: URL(string: "https://h123qzrdps.feishu.cn/docx/MNPadKeSaoqg6LxgifVcFHLzn2c?from=from_copylink")!, label: {
                                Text("使用条款")
                                    .font(.caption2)
                                    .foregroundColor(.gray)
                                    .underline()
                            })
                            Spacer()
                            Link(destination: URL(string: "https://h123qzrdps.feishu.cn/docx/GGl2d2elKoGFumxYE4Ocjhcbn7g?from=from_copylink")!, label: {
                                Text("隐私条款")
                                    .font(.caption2)
                                    .foregroundColor(.gray)
                                    .underline()
                            })
                        }
                        .padding(.horizontal)
                        
                        
                    }
                }
                
                VStack {
                    Spacer()
                    HStack {
                        Button(action: {
                            if !isLoading {
                                Task {
                                    isLoading = true
        //                            try? await AppStore.sync()
                                    await store.fetchActiveTransactions()
                                    var subscriptionMember = false
                                    var offlineMember = false
                                    for product in store.subscriptionProducts {
                                        if store.purchasedProducts.contains(product.id) {
                                            subscriptionMember = true
                                            confettiFlag += 1
                                        }
                                    }
                                    for product in store.offlineProducts {
                                        if store.purchasedProducts.contains(product.id) {
                                            offlineMember = true
                                        }
                                    }
                                    settings.subscriptionMember = subscriptionMember
                                    settings.offlineMember = offlineMember
                                    isLoading = false
                                    self.presentationMode.wrappedValue.dismiss()
                                }
                            }
                        }) {
                            if isLoading {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .black))
                                    .frame(minWidth: 0, maxWidth: .infinity)
                                    .padding()
//                                    .background(Color.yellow)
                                    .foregroundColor(.black)
                                    .cornerRadius(12)
                            } else {
                                Text("恢复购买")
                                    .fontWeight(.semibold)
                                    .frame(minWidth: 0, maxWidth: .infinity)
                                    .padding()
                                    .foregroundColor(.black)
                                    .cornerRadius(12)
                            }
                        }
                        
                        Button(action: {
                            if !isLoading {
                                isLoading = true;
                                Task {
                                    do {
                                        var product = monthProduct
                                        if selectedItem == "year" {
                                            product = yearProduct
                                        }
                                        if product != nil {
                                            if try await store.purchase(product!) {
                                                settings.subscriptionMember = true
                                                confettiFlag += 1
                                            }
                                        }
                                    } catch StoreError.failedVerification {
                                        msg = "订阅验证失败，请稍后重试。"
                                        isShowingError = true
                                    } catch {
                                        print("购买失败: \(error)")
                                    }
                                    self.isLoading = false
                                }
                            }
                        }) {
                            if isLoading {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .black))
                                    .frame(minWidth: 0, maxWidth: .infinity)
                                    .padding()
                                    .background(Color.yellow)
                                    .foregroundColor(.black)
                                    .cornerRadius(12)
                            } else {
                                Text("订阅")
                                    .fontWeight(.semibold)
                                    .frame(minWidth: 0, maxWidth: .infinity)
                                    .padding()
                                    .background(Color.yellow)
                                    .foregroundColor(.black)
                                    .cornerRadius(12)
                            }
                        }
                    }
                    .padding(.horizontal)
                    .padding(.bottom, 20)
                }
            }
//            List {
//                Section("订阅会员") {
//                    ForEach(store.subscriptionProducts, id: \.id) { product in
//                        HStack {
//                            if store.purchasedProducts.contains(product.id) {
//                                Image(systemName: "crown")
//                                .foregroundColor(.yellow)
//                                .padding()
//                            } else {
//                                Image(systemName: "lock")
//                                .foregroundColor(.yellow)
//                                .padding()
//                            }
//                            
//                            VStack(alignment: .leading) {
//                                Text(product.displayName)
//                                Text(product.description)
//                                    .font(.footnote)
//                                    .foregroundColor(.gray)
//                            }
//                            
//                            Spacer()
//                            
//                            if store.purchasedProducts.contains(product.id) {
//                                Image(systemName: "checkmark")
//                                .foregroundColor(.yellow)
//                                .padding()
//                            } else {
//                                Button(action: {
//                                    isLoading = true;
//                                    Task {
//                                        do {
//                                            if try await store.purchase(product) {
//                                                settings.subscriptionMember = true;
////                                                self.msg = "订阅成功，感谢支持~"
////                                                self.showingPopup = true
//                                            }
//                                        } catch StoreError.failedVerification {
//                                            msg = "订阅验证失败，请稍后重试。"
//                                            isShowingError = true
//                                        } catch {
//                                            print("购买失败: \(error)")
//                                        }
////                                        let result = try await store.purchase(product);
//////                                        DispatchQueue.main.sync {
////                                        print("p-result: \(result)")
////                                        if result {
////                                            self.settings.subscriptionMember = true;
////                                            self.msg = "订阅成功，感谢支持~"
////                                            self.showingPopup = true
////                                        }
//                                        self.isLoading = false
////                                        }
//                                    }
//                                }, label: {
//                                    Text(product.displayPrice)
//                                        .padding(.vertical, 5)
//                                        .padding(.horizontal, 10)
//                                        .background(.gray.opacity(0.1))
//                                        .cornerRadius(25)
//                                })
//                            }
//                        }
//                        
//                    }
//                }
//                
//                Section("会员功能") {
//                    HStack {
//                        Image(systemName: "clock")
//                            .frame(width: 25, height: 25)
//                            .foregroundColor(.yellow)
//                            .cornerRadius(5)
//                            .padding(.trailing, 5)
//                        Link("实时行情", destination: URL(string: "https://h123qzrdps.feishu.cn/docx/MV9ydCCTJo6oIVxHoGecIdamnVd?from=from_copylink")!)
////                            .foregroundColor(.black)
//                    }
//                    HStack {
//                        Image(systemName: "chart.xyaxis.line")
//                            .frame(width: 25, height: 25)
//                            .foregroundColor(.yellow)
//                            .cornerRadius(5)
//                            .padding(.trailing, 5)
////                        Text("交易点位图表")
//                        Link("交易点位图表", destination: URL(string: "https://h123qzrdps.feishu.cn/docx/QNDad0McFoUyudxboGVclWZ6nR7?from=from_copylink")!)
////                            .foregroundColor(.black)
//                    }
//                    HStack {
//                        Image(systemName: "arrow.triangle.branch")
//                            .frame(width: 25, height: 25)
//                            .foregroundColor(.yellow)
//                            .cornerRadius(5)
//                            .padding(.trailing, 5)
//                        Link("子策略-底仓计息", destination: URL(string: "https://h123qzrdps.feishu.cn/docx/RQridmptKolUONxMGfMcLb3Fnyh?from=from_copylink")!)
////                            .foregroundColor(.black)
//                    }
//                }
//                
//                Section("关于") {
//                    Text("「网格交易工具箱」是一个用于管理网格交易策略的工具。你可以购买「会员」来支持开发者，后续会有更多的功能面向「会员」开放。")
//                        .font(.callout)
//                    Link("使用条款", destination: URL(string: "https://h123qzrdps.feishu.cn/docx/MNPadKeSaoqg6LxgifVcFHLzn2c?from=from_copylink")!)
//                    Link("隐私条款", destination: URL(string: "https://h123qzrdps.feishu.cn/docx/GGl2d2elKoGFumxYE4Ocjhcbn7g?from=from_copylink")!)
//                    Link("联系我们 & 加入用户群", destination: URL(string: "https://h123qzrdps.feishu.cn/docx/NqFtda3vWoXRUVxwX0Tc0kRpnJd?from=from_copylink")!)
//                    Text("订购后，除非是在当前订购期限结束前提前至少24小时取消续订，你的月缴或年缴套餐将以相同的期限自动续订。订购可随时在 App Store 取消，无需任何额外费用。取消后，你的套餐将在当前订购期限结束时终止。")
//                        .foregroundColor(.gray)
//                        .font(.footnote)
//                    HStack {
//                        Spacer()
//                        Text("沪ICP备2023020181号-4A")
//                            .font(.footnote)
//                            .foregroundColor(.gray)
//                        Spacer()
//                    }
//                }
//            }
            .alert(isPresented: $isShowingError, content: {
                Alert(title: Text(msg), message: nil, dismissButton: .default(Text("好的")))
            })
            .confettiCannon(counter: $confettiFlag, num: 50)
            .navigationTitle("会员")
//            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        self.presentationMode.wrappedValue.dismiss()
                    }, label: {
                        Image(systemName: "multiply")
                            .foregroundColor(.blue)
                    })
                }
//                ToolbarItem(placement: .navigationBarLeading) {
//                    Button("返回") {
//                        self.presentationMode.wrappedValue.dismiss()
//                    }
//                }
                
//                ToolbarItem(placement: .navigationBarTrailing) {
//                    Button("恢复购买") {
//                        Task {
//                            isLoading = true
////                            try? await AppStore.sync()
//                            await store.fetchActiveTransactions()
//                            var subscriptionMember = false
//                            var offlineMember = false
//                            for product in store.subscriptionProducts {
//                                if store.purchasedProducts.contains(product.id) {
//                                    subscriptionMember = true
//                                }
//                            }
//                            for product in store.offlineProducts {
//                                if store.purchasedProducts.contains(product.id) {
//                                    offlineMember = true
//                                }
//                            }
//                            settings.subscriptionMember = subscriptionMember
//                            settings.offlineMember = offlineMember
//                            isLoading = false
//                        }
//                    }
//                }
            }
//            .overlay {
//                if isLoading {
//                    ZStack {
//                        Color.black.opacity(0.5)
//                            .edgesIgnoringSafeArea(.all)
//                        ProgressView {
//                            Text("验证中...")
//                                .foregroundColor(.white)
//                        }
//                              .progressViewStyle(CircularProgressViewStyle(tint: .white))
//                              .scaleEffect(1.0, anchor: .center)
//                    }
//                }
//            }
            .onAppear {
                if store.subscriptionProducts.isEmpty {
                    Task {
                        await store.fetchProducts()
                        await store.fetchActiveTransactions()
                    }
                }
            }
            .disabled(isLoading)
        }
    }
}

struct CourseItem: View {
    @ObservedObject var store: Store
    @State var isPurchased: Bool = false
    var product: Product

    var body: some View {
        VStack {
            if store.isPurchased(product) {
                Text(Image(systemName: "checkmark"))
                    .bold()
                    .padding()
            } else {
                Text(product.displayPrice)
                    .padding()
            }
        }
//        .onChange(of: store.purchasedProducts) {
//            Task {
//                isPurchased = (try? await store.isPurchased(product)) ?? false
//            }
//        }
    }
}

//#Preview {
//    ProMemberView()
//}
