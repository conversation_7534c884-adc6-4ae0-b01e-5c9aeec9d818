//
//  ICloudSyncStatusView.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2025/1/3.
//

import SwiftUI
import CloudKit

struct ICloudSyncStatusView: View {
    @State private var iCloudStatus = "检查中..."
    @State private var lastSyncTime: Date?
    @State private var isChecking = false
    @State private var syncProgress: Double = 0
    
    var body: some View {
        List {
            Section {
                HStack {
                    Text("iCloud 状态")
                    Spacer()
                    Text(iCloudStatus)
                        .foregroundColor(.secondary)
                }
                
                HStack {
                    Text("上次同步")
                    Spacer()
                    if let lastSync = lastSyncTime {
                        Text(lastSync, style: .date)
                            .foregroundColor(.secondary)
                    } else {
                        Text("未同步")
                            .foregroundColor(.secondary)
                    }
                }
                
                if isChecking {
                    ProgressView("同步进度", value: syncProgress, total: 1.0)
                }
            }
            
            Section {
                Button(action: checkiCloudStatus) {
                    HStack {
                        Text("检查同步状态")
                        Spacer()
                        Image(systemName: "arrow.clockwise")
                    }
                }
                .disabled(isChecking)
                
                Button(action: forceSyncNow) {
                    HStack {
                        Text("立即同步")
                        Spacer()
                        Image(systemName: "arrow.triangle.2.circlepath")
                    }
                }
                .disabled(isChecking)
            }
        }
        .navigationTitle("iCloud同步状态")
        .onAppear {
            checkiCloudStatus()
        }
    }
    
    private func checkiCloudStatus() {
        isChecking = true
        syncProgress = 0.2
        
        CKContainer.default().accountStatus { (accountStatus, error) in
            DispatchQueue.main.async {
                switch accountStatus {
                case .available:
                    self.iCloudStatus = "已连接"
                    self.syncProgress = 0.6
                    self.checkLastSync()
                case .noAccount:
                    self.iCloudStatus = "未登录 iCloud"
                case .restricted:
                    self.iCloudStatus = "受限"
                case .couldNotDetermine:
                    self.iCloudStatus = "无法确定状态"
                @unknown default:
                    self.iCloudStatus = "未知状态"
                }
                
                self.syncProgress = 1.0
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    self.isChecking = false
                }
            }
        }
    }
    
    private func checkLastSync() {
        // 从 UserDefaults 获取上次同步时间
        if let lastSync = UserDefaults.standard.object(forKey: "LastiCloudSync") as? Date {
            self.lastSyncTime = lastSync
        }
    }
    
    private func forceSyncNow() {
        isChecking = true
        syncProgress = 0
        
        // 这里添加强制同步的逻辑
        // 模拟同步过程
        Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { timer in
            DispatchQueue.main.async {
                if self.syncProgress < 1.0 {
                    self.syncProgress += 0.2
                } else {
                    timer.invalidate()
                    self.isChecking = false
                    // 更新同步时间
                    let now = Date()
                    self.lastSyncTime = now
                    UserDefaults.standard.set(now, forKey: "LastiCloudSync")
                }
            }
        }
    }
}

#Preview {
    ICloudSyncStatusView()
}
