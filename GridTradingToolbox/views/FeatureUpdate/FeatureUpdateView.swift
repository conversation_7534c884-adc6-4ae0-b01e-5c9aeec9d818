//
//  FeatureUpdateView.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/8/2.
//

import SwiftUI

struct Feature: Identifiable {
    let id: UUID = UUID()
    let imageName: String
    let title: String
    let desc: String
}

struct FeatureUpdateView: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @EnvironmentObject var settings: Settings

    let features = [
        Feature(imageName: "IndexFilterFeature", title: "指数筛选", desc: "新增会员功能，指数筛选，根据PE和PB的百分位数据来筛选查看指数信息")
    ]
    
    @State var selectedIndex = 0
    @State var showPro = false
    
    var body: some View {
        NavigationStack {
            if showPro {
                ProMemberView()
            } else {
                ZStack {
                    ScrollView {
                        VStack(alignment: .leading) {
                            TabView {
                                Image(features[selectedIndex].imageName)
                                    .resizable()
                                    .scaledToFit()
                                    .frame(height: 250)
                                    .padding()
                                    .tag(0)
                            }
                            .frame(height: 350)
                            .padding()
                            .tabViewStyle(PageTabViewStyle())
                            .indexViewStyle(PageIndexViewStyle(backgroundDisplayMode: .always))
                            
                            
                            VStack(alignment: .leading) {
                                Text(features[selectedIndex].title)
                                    .fontWeight(.bold)
                                    .font(.title2)
                                    .padding(.bottom, 8)
                                Text(features[selectedIndex].desc)
                                    .font(.callout)
                                    .foregroundColor(.gray)
                                    .padding(.bottom, 16)
                                    .multilineTextAlignment(.leading)
                            }
                            .padding(.vertical)
                            .padding(.horizontal)
                            .padding(.horizontal)
                            
                            Spacer()
                        }
                    }
                    
                    VStack {
                        Spacer()
                        Button(action: {
                            if settings.subscriptionMember {
                                settings.showUpdate = false
                                self.presentationMode.wrappedValue.dismiss()
                            } else {
                                withAnimation {
                                    showPro.toggle()
                                }
                            }
                        }) {
                            Text("继续")
                                .frame(minWidth: 0, maxWidth: .infinity)
                                .padding()
                                .background(Color.yellow)
                                .foregroundColor(.white)
                                .cornerRadius(12)
                        }
                        .padding(.horizontal)
                        .padding(.bottom, 20) // 调整底部间距
                    }
                }
                .navigationTitle("最近更新")
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button(action: {
                            self.presentationMode.wrappedValue.dismiss()
                        }, label: {
                            Image(systemName: "multiply")
                                .foregroundColor(.blue)
                        })
                    }
                }
            }
        }
    }
}

//struct FeatureView: View {
//    var featureTitle: String
//    var featureDesc: String
//    
//    var body: some View {
//        VStack(spacing:20){
//            Image(systemName: "user")
//                .resizable()
//                .scaledToFit()
//                .shadow(color: Color(red: 0, green: 0, blue: 0), radius: 3,x:2,y:2)
//            Text(featureTitle)
//                .fontWeight(.bold)
//                .font(.title2)
//            Text(featureDesc)
//                .foregroundColor(.gray)
//                .padding(.bottom,15)
//                .multilineTextAlignment(.leading)
//        }
//        .padding(.horizontal,15)
//    }
//}

#Preview {
    FeatureUpdateView()
}
