//
//  StrategySummaryCardView.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2025/1/12.
//

import SwiftUI
import PopupView

struct StrategySummaryCardView: View {
    @EnvironmentObject var settings: Settings

    var strategy: Strategy
    @StateObject var viewModel: StockPriceViewModel
    private var maxInvestment = 0
    private var percent = 0

    @State
    var showAlert: Bool = false
    @State
    var alertTitle: String = ""
    @State
    var alertMessage: String = ""
    
    init(strategy: Strategy, viewModel: StockPriceViewModel) {
        self.strategy = strategy;
        let mInvestment = strategy.maxInvestment;
        
        if mInvestment != 0 {
            self.percent = 10000 * strategy.totalInvestment / mInvestment
        } else {
            self.percent = 0
        }
        self.maxInvestment = mInvestment
        self._viewModel = StateObject(wrappedValue: viewModel)
    }
    
    var displayProfit: String {
        var profit = Double(strategy.totalProfit)
        if settings.subscriptionMember && strategy.triggerNotify && strategy.code != nil && !viewModel.isLoading {
            profit = Double(strategy.realTotalProfit) + Double(strategy.getRetainShares()) * viewModel.price * 1000.0
        }
        
        return Utils.displayDecimal(decimal: profit / 1000.0)
    }

    // 持仓盈亏(盈亏金额, 收益率)
    var positionYield: (Double, Double) {
        let currentCost = viewModel.price * Double(strategy.holdShares)
        let holdingCost = Double(strategy.holdingCost) / 1000.0
        if holdingCost == 0 {
            return (0, 0)
        }
        print("currentCost: \(currentCost), holdingCost: \(holdingCost)")
        return (currentCost - holdingCost, currentCost / holdingCost - 1)
    }
    
    var body: some View {
        VStack {
            if settings.subscriptionMember && strategy.triggerNotify && strategy.code != nil && strategy.holdShares > 0 {
                HStack(alignment: .firstTextBaseline) {
                    if viewModel.isLoading {
                        ProgressView()
                            .font(.system(size: 24))
                            .padding([.bottom, .leading], 4)
                    } else {
                        VStack(alignment: .leading, spacing: 4) { 
                            HStack {
                                Text("持仓盈亏")
                                    .font(.system(size: 14))
                                    // .popover(isPresented: $showPositionYieldTips) {
                                    //     VStack(alignment: .leading, spacing: 8) {
                                    //         Text("目前暂未统计交易佣金等，可能与券商统计有细微差距。")
                                    //             .font(.system(size: 12))
                                    //             .foregroundColor(.gray)
                                    //     }
                                    //     .padding()
                                    //     .presentationCompactAdaptation(.popover)
                                    // }
                                Image(systemName: "info.circle")
                                    .font(.system(size: 14))
                                    .foregroundColor(.gray)
                                    .onTapGesture {
                                        alertTitle = "持仓盈亏"
                                        alertMessage = "目前暂未统计交易佣金等，可能与券商统计有细微差距"
                                        showAlert = true
                                    }
                                    .padding(.leading, -8)
                            }
                            HStack(alignment: .firstTextBaseline) {
                                if positionYield.0 > 0 {
                                    Text("+\(Utils.displayDecimal(decimal: positionYield.0))")
                                        .font(.custom("D-DIN", size: 26))
                                        .bold()
                                        .foregroundColor(.red)
                                        .padding(.trailing)
                                    if positionYield.1 > 0 {
                                        Text("+" + Utils.displayPrice(price: positionYield.1 * 100, num: 2) + "%")
                                        .font(.custom("D-DIN", size: 17))
                                        .bold()
                                        .foregroundColor(.red)
                                    }
                                } else if positionYield.0 < 0 {
                                    Text(Utils.displayPrice(price: positionYield.0, num: 2))
                                        .font(.custom("D-DIN", size: 26))
                                        .bold()
                                        .foregroundColor(.green)
                                        .padding(.trailing)
                                    Text(Utils.displayPrice(price: positionYield.1 * 100, num: 2) + "%")
                                        .font(.custom("D-DIN", size: 17))
                                        .bold()
                                        .foregroundColor(.green)
                                } else {
                                    Text("0")
                                        .font(.custom("D-DIN", size: 26))
                                        .bold()
                                        .foregroundColor(.black)
                                        .padding(.trailing)
                                    Text("0%")
                                        .font(.custom("D-DIN", size: 17))
                                        .bold()
                                        .foregroundColor(.black)
                                }
                            }
                        }
                    }
                    Spacer()
                    if ((settings.subscriptionMember && strategy.code != nil && strategy.triggerNotify) || strategy.getRetainShares() == 0) && strategy.grids!.count > 0 && strategy.holdGrids.count == 0 {
                        NavigationLink(destination: StrategyStatisticsView(strategy: strategy, realTimePrice: viewModel.price)) {
                            HStack {
                                Text("统计")
                                    .font(.system(size: 12))
                                    .foregroundColor(.gray)
                                Image(systemName: "chevron.right")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: 8, height: 8)
                                    .foregroundColor(.gray)
                            }
                        }
                    }
                }
                .padding([.horizontal, .top, .bottom], 16)

                HStack(alignment: .firstTextBaseline) {
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text("出网收益")
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                                // .popover(isPresented: $showProfitTips) {
                                //     VStack(alignment: .leading, spacing: 8) {
                                //         Text("仅统计已卖出的网格的收益，若有预留利润股数，使用实时价格计算该部分收益。")
                                //             .font(.caption)
                                //             .foregroundColor(.gray)
                                //     }
                                //     .padding()
                                //     .presentationCompactAdaptation(.popover)
                                // }
                            Image(systemName: "info.circle")
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                                .onTapGesture {
                                    alertTitle = "出网收益"
                                    alertMessage = "仅统计已卖出的网格的收益，若有预留利润股数，使用实时价格计算该部分收益"
                                    showAlert = true
                                }
                                .padding(.leading, -8)
                        }
                        
                        if settings.subscriptionMember && strategy.triggerNotify && strategy.code != nil && viewModel.isLoading {
                                ProgressView()
                                    .font(.system(size: 17))
                                    .padding([.bottom, .leading], 4)
                            } else {
                                Text(displayProfit)
                                    .font(.custom("D-DIN", size: 17))
                                    .foregroundColor(.red)
                                    .bold()
                            }
                    }

                    Spacer()
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text("持有股数")
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                                // .popover(isPresented: $showHoldSharesTips) {
                                //     VStack(alignment: .leading, spacing: 8) {
                                //         Text("持有股数包含预留利润股数 \(strategy.getRetainShares()) 股。")
                                //             .font(.caption)
                                //             .foregroundColor(.gray)
                                //     }
                                //     .padding()
                                //     .presentationCompactAdaptation(.popover)
                                // }
                            Image(systemName: "info.circle")
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                                .onTapGesture {
                                    alertTitle = "持有股数"
                                    alertMessage = "持有股数包含预留利润股数 \(strategy.getRetainShares()) 股"
                                    showAlert = true
                                }
                                .padding(.leading, -8)
                        }
                        
                        Text("\(strategy.holdShares)")
                            .font(.custom("D-DIN", size: 17))
                            .bold()
                    }
                    
                    Spacer()

                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text("平均持有时长")
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                                // .popover(isPresented: $showAvgHoldDaysTips) {
                                //     VStack(alignment: .leading, spacing: 8) {
                                //         Text("所有网格的平均持有天数。")
                                //             .font(.caption)
                                //             .foregroundColor(.gray)
                                //     }
                                //     .padding()
                                //     .presentationCompactAdaptation(.popover)
                                // }
                            Image(systemName: "info.circle")
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                                .onTapGesture {
                                    alertTitle = "平均持有时长"
                                    alertMessage = "所有网格(已卖出和持有的网格)的平均持有天数"
                                    showAlert = true
                                }
                                .padding(.leading, -8)
                        }
                        Text(Utils.trimTrailingZeros(from: String(format: "%.2f", strategy.avgHoldDaysOfGrids)))
                            .font(.custom("D-DIN", size: 17))
                            .bold()
                    }

                    // Spacer()
                }
                .padding([.horizontal])
                .padding(.bottom, 16)
            } else {
                HStack(alignment: .top) {
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text("出网收益")
                                .font(.system(size: 14))
                                // .popover(isPresented: $showProfitTips) {
                                //     VStack(alignment: .leading, spacing: 8) {
                                //         Text("仅统计已卖出的网格的收益，若有预留利润股数，使用实时价格计算该部分收益。")
                                //             .font(.caption)
                                //             .foregroundColor(.gray)
                                //     }
                                //     .padding()
                                //     .presentationCompactAdaptation(.popover)
                                // }
                            Image(systemName: "info.circle")
                                .font(.system(size: 14))
                                .foregroundColor(.gray)
                                .onTapGesture {
                                    alertTitle = "出网收益"
                                    alertMessage = "仅统计已卖出的网格的收益，若有预留利润股数，使用实时价格计算该部分收益"
                                    showAlert = true
                                }
                                .padding(.leading, -8)
                        }
                        
                        HStack(alignment: .firstTextBaseline) {
                            if settings.subscriptionMember && strategy.triggerNotify && strategy.code != nil && viewModel.isLoading {
                                ProgressView()
                                    .font(.system(size: 28))
                                    .padding([.bottom, .leading], 4)
                            } else {
                                Text(displayProfit)
                                    .font(.custom("D-DIN", size: 28))
                                    .foregroundColor(.red)
                                    .bold()
                            }
                        }
                    }
                    
                    Spacer()
    //                if strategySummary.retainShares == 0 && strategy.holdGrids.count == 0 && strategy.grids!.count > 0 {
                    if ((settings.subscriptionMember && strategy.code != nil && strategy.triggerNotify) || strategy.getRetainShares() == 0) && strategy.grids!.count > 0 && strategy.holdGrids.count == 0 {
                        NavigationLink(destination: StrategyStatisticsView(strategy: strategy, realTimePrice: viewModel.price)) {
                            HStack {
                                Text("统计")
                                    .font(.system(size: 12))
                                    .foregroundColor(.gray)
                                Image(systemName: "chevron.right")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: 8, height: 8)
                                    .foregroundColor(.gray)
                            }
                        }
                    }
                }
                .padding([.horizontal, .top, .bottom], 16)

                HStack(alignment: .firstTextBaseline) {
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text("持有股数")
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                                // .popover(isPresented: $showHoldSharesTips) {
                                //     VStack(alignment: .leading, spacing: 8) {
                                //         Text("持有股数包含预留利润股数 \(strategy.getRetainShares()) 股。")
                                //             .font(.system(size: 12))
                                //             .foregroundColor(.gray)
                                //     }
                                //     .padding()
                                //     .presentationCompactAdaptation(.popover)
                                // }
                            Image(systemName: "info.circle")
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                                .onTapGesture {
                                    alertTitle = "持有股数"
                                    alertMessage = "持有股数包含预留利润股数 \(strategy.getRetainShares()) 股"
                                    showAlert = true
                                }
                                .padding(.leading, -8)
                        }
                        
                        Text("\(strategy.holdShares)")
                            .font(.custom("D-DIN", size: 17))
                            .bold()
                    }
                    
                    Spacer()

                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text("平均持有时长")
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                                // .popover(isPresented: $showAvgHoldDaysTips) {
                                //     VStack(alignment: .leading, spacing: 8) {
                                //         Text("所有网格的平均持有天数。")
                                //             .font(.system(size: 12))
                                //             .foregroundColor(.gray)
                                //     }
                                //     .padding()
                                //     .presentationCompactAdaptation(.popover)
                                // }
                            Image(systemName: "info.circle")
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                                .onTapGesture {
                                    alertTitle = "平均持有时长"
                                    alertMessage = "所有网格(已卖出和持有的网格)的平均持有天数"
                                    showAlert = true
                                }
                                .padding(.leading, -8)
                        }
                        Text(Utils.trimTrailingZeros(from: String(format: "%.2f", strategy.avgHoldDaysOfGrids)))
                            .font(.custom("D-DIN", size: 17))
                            .bold()
                    }

                    Spacer()
                }
                .padding([.horizontal])
                .padding(.bottom, 16)
            }
            
            // HStack {
            //     VStack(alignment: .leading) {
            //         Text("持有股数")
            //             .font(.caption)
            //             .foregroundColor(.gray)
                    
            //         HStack(alignment: .firstTextBaseline) {
            //             Text(String(strategy.holdShares))
            //             Text("股")
            //                 .font(.caption)
            //         }
            //         Text("提款次数")
            //             .font(.caption)
            //             .foregroundColor(.gray)
            //             .padding(.top, 0.1)
                    
            //         Text("\(strategy.soldGrids.count)")
            //     }
                
            //     Spacer()
            //     VStack(alignment: .leading) {
            //         Text("预留利润")
            //             .font(.caption)
            //             .foregroundColor(.gray)
                    
            //         HStack(alignment: .firstTextBaseline) {
            //             Text(String(strategy.getRetainShares()))
            //             Text("股")
            //                 .font(.caption)
            //         }
            //         Text("网格平均持有时长")
            //             .font(.caption)
            //             .foregroundColor(.gray)
            //             .padding(.top, 0.1)
                    
            //         HStack(alignment: .firstTextBaseline) {
            //             Text(Utils.trimTrailingZeros(from: String(format: "%.2f", strategy.avgHoldDaysOfGrids)))
            //             Text("天").font(.caption)
            //         }
            //     }
            //     Spacer()
            // }
            // .padding([.horizontal])
            
            HStack {
                Text("投入比例")
                    .font(.system(size: 14))
                Spacer()
                Text(Utils.trimTrailingZeros(from: String(format: "%.2f", Double(percent) / 100.0)) + "%")
                    .font(.custom("D-DIN", size: 16))
            }
            .padding([.horizontal])
            GeometryReader { geometry in
                VStack {
                    ZStack {
                        Capsule()
                            .foregroundColor(.gray.opacity(0.25))
                            .frame(width: geometry.size.width, height: 4)
                        HStack {
                            Capsule()
                                .foregroundColor(.blue.opacity(0.75))
                                .frame(width: geometry.size.width * (percent >= 10000 ? 1 : Utils.div(first: strategy.totalInvestment, last: maxInvestment)), height: 4)
                            Spacer()
                        }
                    }
                    .padding(.bottom, 4)
                    
                    HStack {
                        Text("已投入")
                            .font(.custom("D-DIN", size: 12))
                            .foregroundColor(.gray)
                        Text(Utils.displayDecimal(decimal: Double(strategy.totalInvestment) / 1000.0))
                            .font(.custom("D-DIN", size: 14))
                            .foregroundColor(.gray)
                            .padding(.leading, 1)
                        Spacer()
                        Text("最大投入")
                            .font(.custom("D-DIN", size: 12))
                            .foregroundColor(.gray)
                        Text(Utils.displayDecimal(decimal: Double(maxInvestment) / 1000.0))
                            .font(.custom("D-DIN", size: 14))
                            .foregroundColor(.gray)
                            .padding(.leading, 1)
                        Spacer()
                    }
                }
                
            }.frame(height: 24)
                .padding([.horizontal])
                .padding(.bottom, 16)
                .padding(.top, -4)
        }
        .background(Color.white)
        .cornerRadius(20)
        .alert(isPresented: $showAlert) {
            Alert(title: Text(alertTitle).font(.system(size: 20)), message: Text(alertMessage).font(.system(size: 17)), dismissButton: .default(Text("确定")))
        }
    }
}
