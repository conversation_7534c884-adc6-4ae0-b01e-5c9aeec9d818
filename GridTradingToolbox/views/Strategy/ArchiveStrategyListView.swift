//
//  ArchiveStrategyListView.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/5/17.
//

import SwiftUI
import SwiftData

struct ArchiveStrategyListView: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.modelContext) var modelContext;

    @Query(filter: #Predicate<Strategy> { strategy in
        strategy.status ?? 0 > 1
    }, animation: .smooth) private var archivedStrategies: [Strategy]

    // var strategies: [Strategy]
    
    // var archivedStrategies: [Strategy] {
    //     strategies.filter{ $0.status == Strategy.StrategyStatus.archive.rawValue }
    // }
    
    var body: some View {
        VStack {
            if archivedStrategies.count == 0 {
                Image("no-data")
                    .resizable()
                    .scaledToFit()
                    .frame(height: 200)
                    .padding([.horizontal, .top])   
                Text("暂无归档策略")
                    .font(.footnote)
                    .bold()
                    .padding(.top)
            } else {
                List {
                    ForEach(archivedStrategies) { strategy in
                    NavigationLink(destination: {
                        StrategyStatisticsView(strategy: strategy)
                    }, label: {
                        Text(strategy.name)
                    })
                    .contextMenu {
                        Button(action: {
                            activateStrategy(strategy: strategy)
                        }) {
                            Text("取消归档")
                        }
                        }
                    }
                }
            }
        }
        .navigationTitle("归档策略")
        .navigationBarTitleDisplayMode(.inline)
    }

    @MainActor
    func activateStrategy(strategy: Strategy) {
        strategy.activate()
        try? modelContext.save()
    }
}

//#Preview {
//    ArchiveStrategyListView()
//}
