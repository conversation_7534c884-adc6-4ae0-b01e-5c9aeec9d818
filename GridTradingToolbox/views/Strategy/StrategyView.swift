//
//  ContentView.swift
//  Example
//
//  Created by <PERSON> （陈敏杰） on 2024/1/4.
//

import SwiftUI
import SwiftData
import Charts
import ConfettiSwiftUI

struct BuyCardView: View {
    @EnvironmentObject var settings: Settings

    var grid: TradeGrid
    @Bindable var strategy: Strategy
    @State var showBuyGrid = false
        
    var body: some View {
        HStack {
            VStack(alignment: .leading) {
                Text("档位")
                    .foregroundColor(.gray)
                    .font(.caption2)
                Text(grid.displayGrade)
                    .font(.callout)
                    .fontWeight(.bold)
                    .foregroundColor(.green)
                    .padding(.bottom)
                Text("买入触发价")
                    .foregroundColor(.gray)
                    .font(.caption2)
                Text(grid.displayTheoreticalBuyPrice)
                    .font(.callout)
                    .fontWeight(.bold)
                    .foregroundColor(.green)
            }
            .padding()
            
            VStack(alignment: .leading) {
                Text("买入股数")
                    .foregroundColor(.gray)
                    .font(.caption2)
                Text(grid.displayTheoreticalBuyShares)
                    .font(.callout)
                    .fontWeight(.bold)
                    .foregroundColor(.green)
                    .padding(.bottom)
                Text("买入触发金额")
                    .foregroundColor(.gray)
                    .font(.caption2)
                Text(grid.displayTriggerAmount)
                    .font(.callout)
                    .fontWeight(.bold)
                    .foregroundColor(.green)
            }
            .padding()
            
            Spacer()
            
            Circle()
                .overlay {
                    Text("买")
                        .foregroundColor(.white)
                        .bold()
                }
                .frame(width: 30, height: 30)
                .foregroundColor(.green)
                .clipShape(Circle())
                .padding(.trailing)
                .onTapGesture {
                    showBuyGrid = true;
                }
                .sheet(isPresented: $showBuyGrid) {
                    BuyGridView(strategy: strategy, grid: grid, stockAccountId: settings.defaultAccountId)
                }
        }
        .background(Color.green.opacity(0.1))
        .cornerRadius(20)
    }
}

struct SellCardView: View {
    @EnvironmentObject var settings: Settings
    @Environment(\.modelContext) private var modelContext

    @Bindable var grid: TradeGrid
    var showSellButton: Bool
    @Bindable var strategy: Strategy
    @Binding var confettiFlag: Int
    @State var showSellGrid = false
    @State var showEditGrid = false
    
    var body: some View {
        HStack {
            VStack(alignment: .leading) {
                Text("档位")
                    .foregroundColor(.gray)
                    .font(.caption2)
                Text(grid.displayGrade)
                    .fontWeight(.bold)
                    .foregroundColor(.red)
                    .padding(.bottom)
                Text("卖出触发价")
                    .foregroundColor(.gray)
                    .font(.caption2)
                HStack {
                    Text(grid.getSellPriceWithInterest(calInterest: strategy.calculateInterest, period: strategy.interestPeriod, interest: strategy.interest))
                        .font(.callout)
                        .fontWeight(.bold)
                        .foregroundColor(.red)
                    if grid.isOverInterestPeriod() {
                        Image(systemName: "crown")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .foregroundColor(.yellow)
                            .frame(width: 15, height: 15)
                    }
                }
            }.padding()
            
            
            VStack(alignment: .leading) {
                Text("卖出股数")
                    .foregroundColor(.gray)
                    .font(.caption2)
                Text(grid.displayTheoreticalSellShares)
                    .font(.callout)
                    .fontWeight(.bold)
                    .foregroundColor(.red)
                    .padding(.bottom)
                Text("卖出触发金额")
                    .foregroundColor(.gray)
                    .font(.caption2)
                Text(grid.displayTriggerAmount)
                    .font(.callout)
                    .fontWeight(.bold)
                    .foregroundColor(.red)
            }.padding()
            
            Spacer()
            
            if showSellButton {
                Circle()
                    .overlay {
                        Text("卖")
                            .foregroundColor(.white)
                            .bold()
                    }
                    .frame(width: 30, height: 30)
                    .foregroundColor(.red)
                    .clipShape(Circle())
                    .padding(.trailing)
                    .onTapGesture {
                        showSellGrid = true;
                    }
                    .sheet(isPresented: $showSellGrid) {
                        SellGridView(strategy: strategy, grid: grid, confettiFlag: $confettiFlag)
                    }
            }
        }
        .background(Color.red.opacity(0.1))
        .cornerRadius(20)
        .onTapGesture {
            showEditGrid = true;
        }
        .sheet(isPresented: $showEditGrid) {
            BuyGridView(strategy: strategy, grid: grid, isEditMode: true, stockAccountId: settings.defaultAccountId)
        }
        .contextMenu {
            if showSellButton {
                Button(action: {
                    deleteGrid()
                }, label: {
                    Text("删除")
                })
            }
        }
    }

    @MainActor
    private func deleteGrid() {
        if grid.isSold {
            // todo 检测预留利润的股数是否已经被卖出
            let index = strategy.grids!.firstIndex(of: grid)
            if index != nil {
            strategy.grids!.remove(at: index!)
            }
        } else {
            if grid.gridType == TradeGrid.GridType.small.rawValue && strategy.sortedSmallGrids[0] == grid {
            let index = strategy.grids!.firstIndex(of: grid)
            if index != nil {
                strategy.grids!.remove(at: index!)
            }
            } else if grid.gridType == TradeGrid.GridType.medium.rawValue && strategy.sortedMediumGrids[0] == grid {
            let index = strategy.grids!.firstIndex(of: grid)
            if index != nil {
                strategy.grids!.remove(at: index!)
            }
            } else if grid.gridType == TradeGrid.GridType.large.rawValue && strategy.sortedLargeGrids[0] == grid {
            let index = strategy.grids!.firstIndex(of: grid)
            if index != nil {
                strategy.grids!.remove(at: index!)
            }
            }
            strategy.totalInvestmentAmount = strategy.calculateTotalInvestment()
        }
        modelContext.delete(grid)
        try? modelContext.save()
    }
}

struct StrategyView: View {
    @EnvironmentObject var settings: Settings
    
    @Bindable var strategy: Strategy
    // @Query var strategy: Strategy
    @StateObject var viewModel: StockPriceViewModel
    var smallGrid: TradeGrid? = nil;
    var mediumGrid: TradeGrid? = nil;
    var largeGrid: TradeGrid? = nil;
        
    @State private var displayGridType = "0";
    @State private var showSellGrid = false;
    @State private var showEditGrid = false;
    @State private var showBuySmallGrid = false;
    @State private var confettiFlag = 0;
    @State var grids: [StressGrid] = [];
    @State var showStressTest = false;
    @State var showMoreSmallGrids = false;
    @State var showMoreMediumGrids = false;
    @State var showMoreLargeGrids = false;
    @State private var showEditStrategy = false
    @State private var showSellRetainShare = false
        
    var body: some View {
        
        ScrollView {
            ZStack {
                Color(UIColor.secondarySystemBackground)
                    .edgesIgnoringSafeArea(.all)
                
                
                
                VStack {
                    StrategySummaryCardView(strategy: strategy, viewModel: viewModel)
                    
                    if settings.subscriptionMember && !strategy.grids!.isEmpty  {
                        HStack {
                            Text("交易点位")
                                .fontWeight(.bold)
                            Spacer()
                        }.padding([.top])
                        
                        VStack {
                            PriceChartView(strategy: strategy)
                        }
                        .frame(height: 150)
                            .background(Color.white)
                            .cornerRadius(20)
                    }
                    
                    HStack(alignment: .firstTextBaseline) {
                        Text("网格详情(\(strategy.holdGrids.count))")
                            .fontWeight(.bold)
                        Spacer()
                        if !strategy.soldGrids.isEmpty {
                            NavigationLink(destination: TradeHistoryView(strategy: strategy)) {
                                HStack {
                                    Text("交易历史(\(strategy.soldGrids.count))")
                                        .font(.caption2)
                                        .foregroundColor(.black)
                                    Image(systemName: "chevron.right")
                                        .resizable()
                                        .scaledToFit()
                                        .frame(width: 8, height: 8)
                                        .foregroundColor(.black)
                                }
                            }
                        }
                    }.padding([.top])
                    
                    
                    
                    VStack {
                        if strategy.mediumLargeSwitch {
                            Picker("", selection: $displayGridType.animation()) {
                                Text("小网(\(strategy.smallGrids.count))").tag("0")
                                Text("中网(\(strategy.mediumGrids.count))").tag("1")
                                Text("大网(\(strategy.largeGrids.count))").tag("2")
                            }
                            .pickerStyle(.segmented)
                            .padding([.top, .leading, .trailing])
                        }
                        
                        if displayGridType == "0" {
                            GridsDisplayView(showMoreGrids: $showMoreSmallGrids, confettiFlag: $confettiFlag, grids: strategy.sortedSmallGrids, strategy: strategy, grid: smallGrid)
                        } else if displayGridType == "1" {
                            GridsDisplayView(showMoreGrids: $showMoreMediumGrids, confettiFlag: $confettiFlag, grids: strategy.sortedMediumGrids, strategy: strategy, grid: mediumGrid)
                        } else {
                            GridsDisplayView(showMoreGrids: $showMoreLargeGrids, confettiFlag: $confettiFlag, grids: strategy.sortedLargeGrids, strategy: strategy, grid: largeGrid)
                        }
                        
                        Divider().padding()
                    }
                    .background(Color.white)
                    .cornerRadius(20)
                    
                    Spacer()

                    if #unavailable(iOS 18.0) {
                        NavigationView {
                            Color(UIColor.secondarySystemBackground)
                                .edgesIgnoringSafeArea(.all)
                        }
                        .background(
                            VStack {
                                NavigationLink(destination: CreateStrategyView(strategy: strategy, isEditMode: true), isActive: $showEditStrategy) {
                                    EmptyView()
                                }
                                NavigationLink(destination: SellRetainShareView(strategy: strategy, confettiFlag: $confettiFlag), isActive: $showSellRetainShare) {
                                    EmptyView()
                                }
                            }
                        )
                    }
                }.padding()
            }
            
        }
        .confettiCannon(counter: $confettiFlag, num: 50)
        .background(Color(UIColor.secondarySystemBackground))
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .principal) {
                VStack {
                    Text(strategy.name)
                        .font(.headline)
                        .frame(maxWidth: 200)
                    if settings.subscriptionMember && strategy.triggerNotify && strategy.code != nil && !viewModel.isLoading {
                        HStack {
                            Text(Utils.displayPrice(price: viewModel.price, num: 3))
                                .font(.footnote)
                                .foregroundStyle(viewModel.changePercent > 0 ? .red : .green)
                            if viewModel.changePercent > 0 {
                                Text("+\(Utils.displayPrice(price: viewModel.changePercent, num: 2))%")
                                    .font(.footnote)
                                    .foregroundStyle(.red)
                            } else {
                                Text("\(Utils.displayPrice(price: viewModel.changePercent, num: 2))%")
                                    .font(.footnote)
                                    .foregroundStyle(.green)
                            }
                        }
                    }
                }
            }
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    grids = strategy.stressTest()
                    showStressTest = true
                }, label: {
                    Image(systemName: "gauge.with.needle")
                })
            }
            ToolbarItem(placement: .navigationBarTrailing) {
                if strategy.holdGrids.count > 0 {
                    NavigationLink(destination: CreateStrategyView(strategy: strategy, isEditMode: true)) {
                        Image(systemName: "pencil")
                    }
                } else {
                    if #available(iOS 18.0, *) {
                        Menu {
                            NavigationLink(destination: CreateStrategyView(strategy: strategy, isEditMode: true)) {
                                Text("编辑策略")
                            }
                            NavigationLink(destination: SellRetainShareView(strategy: strategy, confettiFlag: $confettiFlag)) {
                                Text("卖出预留利润")
                            }
                        } label: {
                            Label("more", systemImage: "ellipsis.circle")
                        }
                    } else {
                        Menu {
                            Button("编辑策略") {
                                showEditStrategy = true
                            }
                            Button("卖出预留利润") {
                                showSellRetainShare = true
                            }
                        } label: {
                            Label("more", systemImage: "ellipsis.circle")
                        }
                    }
                        // Menu {
                        //     NavigationLink(destination: CreateStrategyView(strategy: strategy, isEditMode: true)) {
                        //         Text("编辑策略")
                        //     }
                        //     NavigationLink(destination: SellRetainShareView(strategy: strategy, confettiFlag: $confettiFlag)) {
                        //         Text("卖出预留利润")
                        //     }
                        // } label: {
                        //     Label("more", systemImage: "ellipsis.circle")
                        // }
                    // }
                }
            }
        }
        .sheet(isPresented: $showStressTest) {
            let minPrice = (100000 - strategy.maxFall) * strategy.targetPrice / 100000
            StressTestView(grids: $grids, minPrice: minPrice, mediumLargeSwitch: strategy.mediumLargeSwitch)
        }
        .onAppear {
            if settings.subscriptionMember && strategy.triggerNotify && strategy.code != nil {
                viewModel.queryPrice()
            }
        }
        
        
    }
    
    init(strategy: Strategy) {
        self.strategy = strategy
        self.smallGrid = strategy.getNextTradeGrid(gridType: TradeGrid.GridType.small);
        if (strategy.mediumLargeSwitch) {
            self.mediumGrid = strategy.getNextTradeGrid(gridType: TradeGrid.GridType.medium);
            self.largeGrid = strategy.getNextTradeGrid(gridType: TradeGrid.GridType.large);
        }
        let vm = StockPriceViewModel(strategy: strategy)
        self._viewModel = StateObject(wrappedValue: vm)
    }
}

struct GridsDisplayView: View {
    @Binding var showMoreGrids: Bool
    @Binding var confettiFlag: Int
    
    var grids: [TradeGrid]
    var strategy: Strategy
    var grid: TradeGrid?
    
    var body: some View {
        if grid != nil {
            BuyCardView(grid: grid!, strategy: strategy, showBuyGrid: false)
                .padding([.horizontal, .top])
        }
        
        if !grids.isEmpty {
            ForEach(Array(grids.enumerated()), id: \.element.id) { i, item in
               if showMoreGrids || i == 0 {
                   SellCardView(grid: item, showSellButton: i == 0, strategy: strategy, confettiFlag: $confettiFlag, showSellGrid: false, showEditGrid: false)
                       .padding([.horizontal, .top])
                       .transition(.opacity)
               }
           }
            
            if grids.count > 1 {
                Button(action: {
                    withAnimation(.easeInOut(duration: 1)) {
                        showMoreGrids.toggle()
                    }
                }, label: {
                    if showMoreGrids {
                        Image(systemName: "chevron.up")
                            .foregroundColor(.gray)
                    } else {
                        Image(systemName: "chevron.down")
                            .foregroundColor(.gray)
                    }
                })
                .padding(.top)
            }
        }
    }
}

//struct ContentView_Previews: PreviewProvider {
//    static var previews: some View {
//        StrategyView(strategy: Strategy(name: "传媒ETF", interval: "5", targetPrice: "1", amount: "2000", maxFall: "60", buyStrategy: "2", incrementalBuyRatio: "5", mediumLargeSwitch: true, mediumInterval: "15", largeInterval: "30", triggerNotify: true, calculateInterest: true, retainProfitSwitch: true))
//            .previewDevice(PreviewDevice(rawValue: "iPhone 14"))
//            .previewDisplayName("iPhone 14")
//    }
//}
