//
//  StrategySummaryPostView.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/3/29.
//

import SwiftUI

struct StrategySummaryPostView: View {
    let lg = LinearGradient(gradient: Gradient(colors: [
        Color.white.opacity(0.5),
        Color.white.opacity(0.95)]), startPoint: .bottomTrailing, endPoint: .topLeading)
    
    @Bindable var strategy: Strategy
    @Binding var hideSensitiveInfo: Bool
    var profit: Int
    var avgHoldDays: Double
    var xirr: Double
    var invalidXirr: Bool = false
    var soldGridsCount: Int
    var retainProfitExtraProfit: Int = 0
    var mediumLargeGridExtraProfit: Int = 0
    var interestExtraProfit: Int = 0
    var realTimePrice: Double? = nil
    
    init(strategy: Strategy, hideSensitiveInfo: Binding<Bool>, profit: Int, avgHoldDays: Double, realTimePrice: Double? = nil) {
        self.strategy = strategy;
        self.profit = profit;
        self.avgHoldDays = avgHoldDays;
        self.soldGridsCount = strategy.grids!.filter({ g in
            g.buyTradeLog != nil && g.buyTradeLog!.tradeShares > 0
        }).count;
        _hideSensitiveInfo = hideSensitiveInfo;
        
        var values = [Double]()
        var dates = [Date]()
        var sellPrices = [Int]()
        var leftShares = [Int]()
        var basicProfit = 0
        var mediumLargeProfit = 0
        var interestProfit = 0
        var holdShares = 0
        var realProfit = 0
        let calendar = Calendar.current
        for grid in strategy.grids! {
            var shares = 0;
            var sellShares = 0;
            var sellPrice = 0;
            var buyPrice = 0;
            var sellDate = Date.now;
            var buyDate = Date.now;
            for log in grid.tradeLogs! {
                var cost = log.tradePrice * log.tradeShares;
                if log.tradeType == TradeLog.TradeType.buy.rawValue {
                    cost = -cost;
                    shares = log.tradeShares;
                    buyPrice = log.tradePrice;
                    buyDate = log.tradeAt;
                } else if log.tradeType == TradeLog.TradeType.sell.rawValue {
                    sellPrice = log.tradePrice;
                    sellDate = log.tradeAt;
                    sellShares = log.tradeShares;
                }
                values.append(Double(cost));
                dates.append(log.tradeAt);
            }
            sellPrices.append(sellPrice)
            let ls = shares - sellShares
            leftShares.append(ls)
            holdShares += ls
            realProfit += sellPrice * sellShares - buyPrice * shares
            let curGridBasicProfit = (sellPrice - buyPrice) * shares;
            if grid.gridType == TradeGrid.GridType.small.rawValue {
                if strategy.calculateInterest {
                    let times = calendar.dateComponents([.month], from: buyDate, to: sellDate).month ?? 0;
                    let hasYearInterest = strategy.interestPeriod! == Strategy.InterestPeriod.year.rawValue && times >= 12;
                    let hasMonthInterest = strategy.interestPeriod! == Strategy.InterestPeriod.month.rawValue && times >= 1;
                    if (hasYearInterest || hasMonthInterest) {
                        let curInterestProfit = shares * (sellPrice - grid.theoreticalSellPrice);
                        interestProfit += curInterestProfit;
                        basicProfit += curInterestProfit - curInterestProfit;
                    } else {
                        basicProfit += curGridBasicProfit;
                    }
                } else {
                    basicProfit += curGridBasicProfit;
                }
            } else if grid.gridType == TradeGrid.GridType.medium.rawValue || grid.gridType == TradeGrid.GridType.large.rawValue {
                if strategy.calculateInterest {
                    let times = calendar.dateComponents([.month], from: buyDate, to: sellDate).month ?? 0;
                    let hasYearInterest = strategy.interestPeriod! == Strategy.InterestPeriod.year.rawValue && times >= 12;
                    let hasMonthInterest = strategy.interestPeriod! == Strategy.InterestPeriod.month.rawValue && times >= 1;
                    if (hasYearInterest || hasMonthInterest) {
                        let curInterestProfit = shares * (sellPrice - grid.theoreticalSellPrice);
                        interestProfit += curInterestProfit;
                        mediumLargeProfit += curGridBasicProfit - curInterestProfit;
                    } else {
                        mediumLargeProfit += curGridBasicProfit;
                    }
                } else {
                    mediumLargeProfit += curGridBasicProfit;
                }
            }
        }
        for log in strategy.flexibleTradeLog! {
            var cost = log.tradePrice * log.tradeShares
            if log.tradeType == FlexibleTradeLog.TradeType.buy.rawValue {
                cost = -cost
            }
            values.append(Double(cost))
            dates.append(log.tradeAt)
            leftShares.append(log.tradeShares)
            holdShares -= log.tradeShares
            sellPrices.append(-log.tradePrice)
            if realTimePrice != nil {
                realProfit += log.tradeShares * Int(realTimePrice! * 1000.0)
            }
        }
        
        if holdShares > 0 && realTimePrice != nil {
            sellPrices.append(Int(realTimePrice! * 1000))
            leftShares.append(-holdShares)
            values.append(Double(holdShares) * realTimePrice! * 1000.0)
            dates.append(Date.now)
            realProfit += holdShares * Int(realTimePrice! * 1000.0)
            print("fuck realProfit: \(realProfit)")
            self.profit = realProfit
        }
        let res = Utils.xirr(values: values, dates: dates)
        if res != nil {
            self.xirr = res!
        } else {
            self.xirr = 0.0;
            self.invalidXirr = true;
        }
        self.mediumLargeGridExtraProfit = mediumLargeProfit;
        self.interestExtraProfit = interestProfit;
        
        var retainProfitExtraProfit = 0;
        zip(sellPrices, leftShares).forEach { price, shares in
            retainProfitExtraProfit += -price * shares;
        }
        self.retainProfitExtraProfit = retainProfitExtraProfit;
        
    }
    
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 10, style: .continuous)
                .fill(
                    LinearGradient(
                        gradient: Gradient(
                            colors: [
                                Color(red: 214/255, green: 233/255, blue: 255/255),
                                Color(red: 214/255, green: 229/255, blue: 255/255),
                                Color(red: 209/255, green: 214/255, blue: 255/255),
                                Color(red: 221/255, green: 209/255, blue: 255/255),
                                Color(red: 243/255, green: 209/255, blue: 255/255),
                                Color(red: 255/255, green: 204/255, blue: 245/255),
                                Color(red: 255/255, green: 204/255, blue: 223/255),
                                Color(red: 255/255, green: 200/255, blue: 199/255),
                                Color(red: 255/255, green: 216/255, blue: 199/255),
                                Color(red: 255/255, green: 221/255, blue: 199/255)
                            ]
                        ),
                        startPoint: .bottomTrailing,
                        endPoint: .topLeading
                    )
                )
            
            RoundedRectangle(cornerRadius: 10, style: .continuous)
                .fill(lg)
                .padding()
            VStack {
                Image("post")
                    .resizable()
                    .frame(width: 150, height: 150)
                    .padding(.top)
                
                Text(strategy.name)
                    .bold()
                    .font(.callout)
                    .foregroundColor(.black)
                    .padding(.bottom, 16)
                
                VStack(alignment: .leading) {
                    
                    HStack {
                        
                        VStack {
                            Text("盈利金额")
                                .font(.caption)
                                .foregroundColor(.gray)
                            if hideSensitiveInfo {
                                Text("*")
                                    .font(.caption)
                                    .foregroundColor(.red)
                            } else {
                                Text("\(Utils.trimTrailingZeros(from: String(format: "%.2f", Double(profit)/1000.0)))")
                                    .font(.caption)
                                    .foregroundColor(.red)
                            }
                        }
                        Spacer()
                        VStack {
                            Text("年化收益率")
                                .font(.caption)
                                .foregroundColor(.gray)
                            if invalidXirr {
                                Text("无法计算")
                                    .font(.caption)
                                    .foregroundColor(.red)
                            } else {
                                Text("\(Utils.trimTrailingZeros(from: String(format: "%.2f", xirr * 100))) %")
                                    .font(.caption)
                                    .foregroundColor(.red)
                            }
                        }
                        Spacer()
                    }
                    .padding(.bottom, 8)
                    
                    HStack {
                        VStack {
                            Text("提款次数")
                                .font(.caption)
                                .foregroundColor(.gray)
                                .padding(.bottom, 0.1)
                            Text("\(soldGridsCount)")
                                .font(.caption)
                        }
                        Spacer()
                        VStack {
                            Text("平均持有时长")
                                .font(.caption)
                                .foregroundColor(.gray)
                                .padding(.bottom, 0.1)
                            Text("\(Utils.trimTrailingZeros(from: String(format: "%.2f", avgHoldDays))) 天")
                                .font(.caption)
                        }
                        Spacer()
                    }
                    .padding(.bottom, 8)
                    
                    if hideSensitiveInfo {
                        if retainProfitExtraProfit != 0 || mediumLargeGridExtraProfit != 0 || interestExtraProfit != 0 {
                            Text("子策略贡献利润占比")
                                .font(.caption)
                                .padding(.bottom, 4)
                        }
                        if retainProfitExtraProfit != 0 {
                            HStack {
                                Text("- 保留利润: ")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                Text("\(Utils.trimTrailingZeros(from: String(format: "%.2f", Double(retainProfitExtraProfit) / Double(profit) * 100))) %")
//                                    .bold()
                                    .font(.caption)
                                    .foregroundColor(.red)
                            }
                        }
                        if mediumLargeGridExtraProfit != 0 {
                            HStack {
                                Text("- 中网大网: ")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                Text("\(Utils.trimTrailingZeros(from: String(format: "%.2f", Double(mediumLargeGridExtraProfit) / Double(profit) * 100))) %")
//                                    .bold()
                                    .font(.caption)
                                    .foregroundColor(.red)
                            }
                        }
                        if interestExtraProfit != 0 {
                            HStack {
                                Text("- 底仓计息: ")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                Text("\(Utils.trimTrailingZeros(from: String(format: "%.2f", Double(interestExtraProfit) / Double(profit) * 100))) %")
//                                    .bold()
                                    .font(.caption)
                                    .foregroundColor(.red)
                            }
                        }
                    } else {
                        if retainProfitExtraProfit != 0 || mediumLargeGridExtraProfit != 0 || interestExtraProfit != 0 {
                            Text("子策略贡献利润")
                                .font(.caption)
                                .padding(.bottom, 4)
                        }
                        if retainProfitExtraProfit != 0 {
                            HStack {
                                Text("- 保留利润: ")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                Text("\(Utils.trimTrailingZeros(from: String(format: "%.2f", Double(retainProfitExtraProfit) / 1000.0)))")
//                                    .bold()
                                    .font(.caption)
                                    .foregroundColor(.red)
                            }
                        }
                        if mediumLargeGridExtraProfit != 0 {
                            HStack {
                                Text("- 中网大网: ")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                Text("\(Utils.trimTrailingZeros(from: String(format: "%.2f", Double(mediumLargeGridExtraProfit) / 1000.0)))")
//                                    .bold()
                                    .font(.caption)
                                    .foregroundColor(.red)
                            }
                        }
                        if interestExtraProfit != 0 {
                            HStack {
                                Text("- 底仓计息: ")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                Text("\(Utils.trimTrailingZeros(from: String(format: "%.2f", Double(interestExtraProfit) / 1000.0)))")
//                                    .bold()
                                    .font(.caption)
                                    .foregroundColor(.red)
                            }
                        }
                    }
                    Spacer()
                    HStack {
                        Spacer()
                        Text("「网格交易工具箱」App")
                            .font(.footnote)
                            .fontWeight(.semibold)
                            .padding([.bottom])
                        Spacer()
                    }
                    .padding(.bottom)
                }
                .padding(.horizontal, 32)
            
            }
            
        }
    }
}

//#Preview {
//    StrategySummaryPostView()
//}