//
//  SellRetainShareView.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/3/29.
//

import SwiftUI

struct SellRetainShareView: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>

    @Bindable var strategy: Strategy
    
    @Binding var confettiFlag: Int

    @State var sellPrice = ""
    @State var sellShares = 0
    @State var sellDate = Date()
    
    @State var buttonDisable = false
    @State var showingAlert = false
    
    init(strategy: Strategy, confettiFlag: Binding<Int>) {
        self.strategy = strategy;
        self._confettiFlag = confettiFlag;
        self._sellShares = State(initialValue: strategy.retainShares)
    }

    
    var body: some View {
        VStack {
            Form {
                Section() {
                    HStack {
                        Text("卖出价格")
                        TextField("", text: $sellPrice)
                            .multilineTextAlignment(.trailing)
                            .keyboardType(.numbersAndPunctuation)
                            .onChange(of: sellPrice, initial: false) { oldValue, newValue in
                                if newValue.count == 0 {
                                    return;
                                }
                                let result = Utils.checkNumberFormat(number: newValue)
                                if !result {
                                    sellPrice = oldValue
                                }
                            }
                            .submitLabel(.done)
                    }
                    
                    Stepper(value: $sellShares, in: 0...strategy.retainShares, step: 100) {
                        HStack {
                            Text("卖出股数")
                            Spacer()
                            Text(String(sellShares))
                        }
                    }
                    .disabled(true)
                    
                    HStack {
                        Text("卖出日期")
                        Spacer()
                        DatePicker("", selection: $sellDate, in: ...Date.now, displayedComponents: .date)
                            .environment(\.locale, Locale.init(identifier: "zh_CN"))
                    }
                }
                
                Section {
                    HStack {
                        Spacer()
                        RetainSharesTradePost(soldPrice: $sellPrice, soldDate: $sellDate, soldShares: $sellShares)
                            .frame(width: 250, height: 350)
                        Spacer()
                    }
                }
            }
        }
        .navigationTitle("记录卖出")
        .navigationBarTitleDisplayMode(.inline)
        .alert(isPresented: $showingAlert, content: {
            Alert(title: Text("数据异常，请联系开发者"), dismissButton: .default(Text("确认")))
        })
        .toolbar {
            Button("保存") {
                buttonDisable = true;
                let result = strategy.sellRetainShares(price: sellPrice, shares: sellShares, date: sellDate);
                if result {
                    confettiFlag += 1
                    self.presentationMode.wrappedValue.dismiss()
                } else {
                    buttonDisable = false;
                    showingAlert = true;
                }
            }
            .disabled(buttonDisable || sellPrice.isEmpty)
        }
        
    }
    
    
}

//#Preview {
//    SellRetainShareView()
//}
