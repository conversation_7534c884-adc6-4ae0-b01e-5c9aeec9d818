//
//  SummaryCardView.swift
//  Example
//
//  Created by <PERSON> （陈敏杰） on 2024/1/19.
//

import SwiftUI

struct Post: View {
    let lg = LinearGradient(gradient: Gradient(colors: [
        Color.white.opacity(0.5),
        Color.white.opacity(0.95)]), startPoint: .bottomTrailing, endPoint: .topLeading)
    
    @Binding var soldPrice: String
    @Binding var soldDate: Date
    @Binding var hideSensitiveInfo: Bool
    var profit: String
    var holdDays: String
    var profitRatio: String
    var leftShares: Int
    
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 10, style: .continuous)
                .fill(
                    LinearGradient(
                        gradient: Gradient(
                            colors: [
                                Color(red: 214/255, green: 233/255, blue: 255/255),
                                Color(red: 214/255, green: 229/255, blue: 255/255),
                                Color(red: 209/255, green: 214/255, blue: 255/255),
                                Color(red: 221/255, green: 209/255, blue: 255/255),
                                Color(red: 243/255, green: 209/255, blue: 255/255),
                                Color(red: 255/255, green: 204/255, blue: 245/255),
                                Color(red: 255/255, green: 204/255, blue: 223/255),
                                Color(red: 255/255, green: 200/255, blue: 199/255),
                                Color(red: 255/255, green: 216/255, blue: 199/255),
                                Color(red: 255/255, green: 221/255, blue: 199/255)
                            ]
                        ),
                        startPoint: .bottomTrailing,
                        endPoint: .topLeading
                    )
                )
            
            RoundedRectangle(cornerRadius: 10, style: .continuous)
                .fill(lg)
//                .frame(width: 300)
                .padding()
            VStack {
                Image("post")
                    .resizable()
                    .frame(width: 150, height: 150)
                    .padding(.top)
                Text("盈利")
                    .font(.callout)
                if hideSensitiveInfo {
                    Text("\(profitRatio) %")
                        .bold()
                        .font(.title)
                        .foregroundColor(.red)
                        .padding(.bottom)
                } else {
                    Text(profit)
                        .bold()
                        .font(.title)
                        .foregroundColor(.red)
                        .padding(.bottom)
                }
                
                HStack {
                    Spacer()
                    if !hideSensitiveInfo {
                        VStack {
                            Text("留利润")
                                .font(.caption)
                                .foregroundColor(.gray)
                                .padding(.bottom, 0.1)
                            Text("\(leftShares) 股")
                                .font(.caption)
                        }
                        Spacer()
                    }
                    VStack {
                        Text("卖出价")
                            .font(.caption)
                            .foregroundColor(.gray)
                            .padding(.bottom, 0.1)
                        Text(soldPrice)
                            .font(.caption)
                    }
                    Spacer()
                    VStack {
                        Text("持有天数")
                            .font(.caption)
                            .foregroundColor(.gray)
                            .padding(.bottom, 0.1)
                        Text(holdDays)
                            .font(.caption)
                    }
                    Spacer()
                }
                Spacer()
//                VStack(alignment: .leading) {
//                    Text("赚钱的风,")
//                        .font(.footnote)
//                        .fontWeight(.semibold)
//                    Text("终于吹到了我的钱包里~")
//                        .font(.footnote)
//                        .fontWeight(.semibold)
//                }
////                .padding(.horizontal)
                HStack {
                    Spacer()
                    Text("「网格交易工具箱」App")
                        .font(.footnote)
                        .fontWeight(.semibold)
                        .padding([.bottom])
                    Spacer()
                }
                .padding([.bottom])
            }
            
        }
//        .frame(width: 300, height: 500)
    }
    
    init(soldPrice: Binding<String>, soldDate: Binding<Date>, soldShares: Int, hideSensitiveInfo: Binding<Bool>, grid: TradeGrid) {
        _soldPrice = soldPrice
        _soldDate = soldDate
        _hideSensitiveInfo = hideSensitiveInfo
        var days = Utils.calcDaysBetweenDates(startDate: grid.buyTradeLog!.tradeAt, endDate: soldDate.wrappedValue)
        if days == 0 {
            days = 1
        }
        self.holdDays = String(days)
        
        let leftShares = grid.buyTradeLog!.tradeShares - soldShares
        self.leftShares = leftShares
        
        let price = Int((Double(soldPrice.wrappedValue) ?? 0) * 1000)
        let profitRatio = Double(price - grid.buyTradeLog!.tradePrice) / Double(grid.buyTradeLog!.tradePrice)
        self.profitRatio = String(format: "%.2f%", profitRatio * 100)
        self.profit = Utils.trimTrailingZeros(from: String(format: "%.3f", ((soldPrice.wrappedValue as NSString).doubleValue - Double(grid.buyTradeLog!.tradePrice) / 1000.0) * Double(grid.buyTradeLog!.tradeShares)))
    }
    
}

struct RetainSharesTradePost: View {
    let lg = LinearGradient(gradient: Gradient(colors: [
        Color.white.opacity(0.5),
        Color.white.opacity(0.95)]), startPoint: .bottomTrailing, endPoint: .topLeading)
    
    @Binding var soldPrice: String
    @Binding var soldShares: Int
    @Binding var soldDate: Date
    var profit: String
    
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 10, style: .continuous)
                .fill(
                    LinearGradient(
                        gradient: Gradient(
                            colors: [
                                Color(red: 214/255, green: 233/255, blue: 255/255),
                                Color(red: 214/255, green: 229/255, blue: 255/255),
                                Color(red: 209/255, green: 214/255, blue: 255/255),
                                Color(red: 221/255, green: 209/255, blue: 255/255),
                                Color(red: 243/255, green: 209/255, blue: 255/255),
                                Color(red: 255/255, green: 204/255, blue: 245/255),
                                Color(red: 255/255, green: 204/255, blue: 223/255),
                                Color(red: 255/255, green: 200/255, blue: 199/255),
                                Color(red: 255/255, green: 216/255, blue: 199/255),
                                Color(red: 255/255, green: 221/255, blue: 199/255)
                            ]
                        ),
                        startPoint: .bottomTrailing,
                        endPoint: .topLeading
                    )
                )
            
            RoundedRectangle(cornerRadius: 10, style: .continuous)
                .fill(lg)
//                .frame(width: 300)
                .padding()
            VStack {
                Image("post")
                    .resizable()
                    .frame(width: 150, height: 150)
                    .padding(.top)
                Text("盈利")
                    .font(.callout)
                Text(profit)
                    .bold()
                    .font(.title)
                    .foregroundColor(.red)
                    .padding(.bottom)
                
                Text("卖出价")
                    .font(.caption)
                    .foregroundColor(.gray)
                    .padding(.bottom, 0.1)
                Text(soldPrice)
                    .font(.caption)
                
                Spacer()
                
                HStack {
                    Spacer()
                    Text("「网格交易工具箱」App")
                        .font(.footnote)
                        .fontWeight(.semibold)
                        .padding([.bottom])
                    Spacer()
                }
                .padding([.bottom])
            }
            
        }
//        .frame(width: 300, height: 500)
    }
    
    init(soldPrice: Binding<String>, soldDate: Binding<Date>, soldShares: Binding<Int>) {
        _soldPrice = soldPrice
        _soldShares = soldShares
        _soldDate = soldDate
        self.profit = Utils.trimTrailingZeros(from: String(format: "%.3f", (soldPrice.wrappedValue as NSString).doubleValue * Double(soldShares.wrappedValue)))
    }
    
}

//struct SummaryCardView: View {
//    let lg = LinearGradient(gradient: Gradient(colors: [
//        Color.white.opacity(0.5),
//        Color.white.opacity(0.95)]), startPoint: .bottomTrailing, endPoint: .topLeading)
//    
//    @State private var renderedImage = Image(systemName: "photo")
//    @Environment(\.displayScale) var displayScale
//    
//    var body: some View {
//        VStack {
//            renderedImage
//            
//        }
//        .background(Color.white)
//        .onAppear {
//            render()
//        }
//        
//    }
//    
//    @MainActor func render() {
//        let renderer = ImageRenderer(content: Post(profit: "+8.88%", name: "传媒ETF", soldPrice: "1.234", holdDays: "16").frame(width: 300, height: 500))
//
//            // make sure and use the correct display scale for this device
//            renderer.scale = displayScale
//        
////        UIImageWriteToSavedPhotosAlbum(renderer.uiImage!, nil, nil, nil)
//
//            if let uiImage = renderer.uiImage {
//                renderedImage = Image(uiImage: uiImage)
//            }
//        }
//}

//struct SummaryCardView_Previews: PreviewProvider {
//    static var previews: some View {
//        SummaryCardView()
//    }
//}
