//
//  PriceChartView.swift
//  GridTradingToolbox
//
//  Created by 陈敏杰 on 2024/1/20.
//

import SwiftUI
import Charts

struct YellowGroupBoxStyle: GroupBoxStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.content
            .padding()
            .background(.white)
            .cornerRadius(20)
            .overlay(
                configuration.label.padding(10),
                alignment: .topLeading
            )
    }
}

struct PriceChartView: View {
    var strategy: Strategy;
    var tradeLogs: [TradeLog]
    var min: Int
    var max: Int
    var showAvgPrice: Bool
    var avgPrice: Int
    var yScale = "day"
    
    init(strategy: Strategy) {
        self.strategy = strategy
        var logs = [TradeLog]()
        var min = Int.max
        var max = Int.min
        var totalCost = 0
        var holdShares = 0
        var minDate: Date = .now
        var maxDate: Date = .now
        for grid in strategy.grids! {
            for log in grid.tradeLogs! {
                logs.append(log)
                min = min < log.tradePrice ? min : log.tradePrice
                max = max > log.tradePrice ? max : log.tradePrice
                minDate = minDate < log.tradeAt ? minDate : log.tradeAt
                maxDate = maxDate > log.tradeAt ? maxDate : log.tradeAt
                if log.tradeType == TradeLog.TradeType.buy.rawValue {
                    totalCost += log.tradePrice * log.tradeShares
                    holdShares += log.tradeShares
                } else {
                    totalCost -= log.tradePrice * log.tradeShares
                    holdShares -= log.tradeShares
                }
            }
        }
        for log in strategy.flexibleTradeLog! {
            if log.tradeType == FlexibleTradeLog.TradeType.buy.rawValue {
                holdShares += log.tradeShares
            } else {
                holdShares -= log.tradeShares
            }
        }
        logs.sort{ log1, log2 in
            if log1.tradeAt == log2.tradeAt {
                return log1.tradeType < log2.tradeType
            }
            return log1.tradeAt < log2.tradeAt
        }
        self.tradeLogs = logs
        self.min = min
        self.max = max
        if holdShares > 0 {
            self.showAvgPrice = true
            self.avgPrice = totalCost / holdShares
            self.min = min < avgPrice ? min : avgPrice
            self.max = max > avgPrice ? max : avgPrice
            print(self.avgPrice)
        } else {
            self.showAvgPrice = false
            self.avgPrice = 0
        }
        
        let diff = Utils.calcDaysBetweenDates(startDate: minDate, endDate: maxDate)
        if diff > 548 {
            self.yScale = "year"
        } else if diff > 30 {
            self.yScale = "month"
        }
    }
    
    var body: some View {
        GroupBox() {
            Chart {
                ForEach(tradeLogs) { log in
                    LineMark(
                        x: .value("交易日期", log.tradeAt, unit: .day),
                        y: .value("price", Double(log.tradePrice) / 1000.0)
                    )
                    .foregroundStyle(.gray)
//                    .interpolationMethod(.catmullRom)
                    .symbol {
                        if log.tradeType == TradeLog.TradeType.buy.rawValue {
                            Circle()
                                .fill(.green)
                                .frame(width: 8, height: 8)
                        } else {
                            Circle()
                                .fill(.red)
                                .frame(width: 8, height: 8)
                        }
                    }
                }
                RuleMark(y: .value("成本价", Double(avgPrice) / 1000.0))
                    .foregroundStyle(Color.secondary)
                    .lineStyle(StrokeStyle(lineWidth: 0.8, dash: [10]))
                    .annotation(alignment: .topLeading) {
                        Text("成本价: " + Utils.displayPrice(price: avgPrice))
                            .font(.footnote).bold()
                            .foregroundStyle(Color.secondary)
                    }
            }
            .chartXAxis {
                if yScale == "day" {
                    AxisMarks(values: .stride(by: .day)) { _ in
                        AxisTick()
                        AxisGridLine()
                        AxisValueLabel(format: .dateTime.month(.abbreviated).day(.defaultDigits), centered: true)
                    }
                } else if yScale == "month" {
                    AxisMarks(values: .stride(by: .month)) { _ in
                        AxisTick()
                        AxisGridLine()
                        AxisValueLabel(format: .dateTime.month(.abbreviated), centered: true)
                    }
                } else {
                    AxisMarks(values: .stride(by: .year)) { _ in
                        AxisTick()
                        AxisGridLine()
                        AxisValueLabel(format: .dateTime.year(.defaultDigits), centered: true)
                    }
                }
            }
            .chartYScale(domain: [0.9 * Double(min) / 1000.0, 1.1 * Double(max) / 1000.0])
            
        }
        .groupBoxStyle(YellowGroupBoxStyle())
    }
}

//#Preview {
//    PriceChartView()
//}
