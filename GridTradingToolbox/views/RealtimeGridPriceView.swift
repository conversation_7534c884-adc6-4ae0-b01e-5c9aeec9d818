//
//  RealtimeGridPriceView.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/4/10.
//

import SwiftUI

struct RealtimeGridPriceView: View {
    @StateObject var viewModle = StocksPriceViewModel()
    var strategies: [Strategy]
    @State var hideSensitiveInfo = false
    @State var selectedItem = "0"
    let items = ["0", "1"]
    
    init(strategies: [Strategy]) {
        self.strategies = strategies
    }
    
    var body: some View {
        ZStack {
            // 更轻盈的背景色
            Color(UIColor.systemGray6)
                .edgesIgnoringSafeArea(.all)
            
            VStack(spacing: 16) {
                // 改进的分段控制器
                HStack(spacing: 0) {
                    ForEach(items, id: \.self) { item in
                        Button(action: {
                            withAnimation(.spring()) {
                                selectedItem = item
                            }
                        }) {
                            Text(item == "0" ? "待卖出" : "待买入")
                                .fontWeight(.medium)
                                .padding(.vertical, 12)
                                .frame(maxWidth: .infinity)
                                .foregroundColor(selectedItem == item ? .primary : .secondary)
                        }
                    }
                }
                .overlay(
                    // 底部指示器
                    VStack {
                        Spacer()
                        Rectangle()
                            .fill(Color.black)
                            .frame(width: UIScreen.main.bounds.width / 2 - 40, height: 3)
                            .offset(x: selectedItem == "0" ? -(UIScreen.main.bounds.width / 4 - 20) : (UIScreen.main.bounds.width / 4 - 20))
                            .animation(.spring(), value: selectedItem)
                    }
                )
                .padding(.horizontal)
                
                // 状态卡片 - 类似截图中的"Keep it up!"
                if !viewModle.isLoading && ((selectedItem == "0" && !viewModle.sellGrids.isEmpty) || 
                                           (selectedItem == "1" && !viewModle.buyGrids.isEmpty)) {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(selectedItem == "0" ? "待卖出网格" : "待买入网格")
                                .font(.headline)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                            
                            let triggeredCount = selectedItem == "0" ?
                                viewModle.sellGrids.filter { $0.triggerPercent <= 0 }.count :
                                viewModle.buyGrids.filter { $0.triggerPercent >= 0 }.count
                            
                            let waitingCount = (selectedItem == "0" ? viewModle.sellGrids.count : viewModle.buyGrids.count) - triggeredCount
                            
                            Text(triggeredCount > 0 ?
                                "\(waitingCount) 个网格待触发，\(triggeredCount) 个网格已触发" :
                                "\(waitingCount) 个网格待触发"
                            )
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.9))
                        }
                        
                        Spacer()
                        
                        Image(systemName: selectedItem == "0" ? "arrow.up.right.circle.fill" : "arrow.down.right.circle.fill")
                            .font(.system(size: 40))
                            .foregroundColor(.white.opacity(0.8))
                    }
                    .padding()
                    .background(
                        LinearGradient(
                            gradient: Gradient(
                                colors: selectedItem == "0" ? 
                                    [Color.red.opacity(0.7), Color.orange.opacity(0.7)] : 
                                    [Color.green.opacity(0.7), Color.blue.opacity(0.7)]
                            ),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 16))
                    .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
                    .padding(.horizontal)
                }
                
                if viewModle.isLoading {
                    Spacer()
                    VStack {
                        ProgressView()
                            .scaleEffect(1.5)
                            .padding()
                        Text("加载中...")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(20)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(Material.ultraThinMaterial)
                            .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
                    )
                    .padding()
                    Spacer()
                } else {
                    ScrollView {
                        LazyVStack(spacing: 16) {
                            ForEach(selectedItem == "0" ? viewModle.sellGrids : viewModle.buyGrids, id: \.id) { grid in
                                NavigationLink(destination: StrategyView(strategy: strategies.first(where: { $0.id == grid.strategyId })!)) {
                                    EnhancedGridItemCard(
                                        grid: grid, 
                                        hideSensitiveInfo: hideSensitiveInfo, 
                                        isSellGrid: selectedItem == "0"
                                    )
                                }
                                .transition(.asymmetric(
                                    insertion: .move(edge: .trailing).combined(with: .opacity),
                                    removal: .move(edge: .leading).combined(with: .opacity)
                                ))
                            }
                        }
                        .animation(.spring(response: 0.5, dampingFraction: 0.8), value: selectedItem)
                        .padding(.horizontal)
                        .padding(.bottom)
                    }
                    .refreshable {
                        await viewModle.queryPriceLoop(strategies: strategies)
                    }
                }
            }
        }
        .navigationTitle("待触发网格")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            Button(action: {
                hideSensitiveInfo.toggle()
            }, label: {
                if hideSensitiveInfo {
                    Image(systemName: "eye.slash")
                        .foregroundColor(.secondary)
                } else {
                    Image(systemName: "eye")
                        .foregroundColor(.secondary)
                }
            })
        }
        .onAppear {
            Task {
                await viewModle.queryPriceLoop(strategies: strategies)
            }
        }
    }
}

// 增强版网格项卡片组件
struct EnhancedGridItemCard: View {
    var grid: StockGridPrice
    var hideSensitiveInfo: Bool
    var isSellGrid: Bool
    
    var body: some View {
        HStack(alignment: .center) {
            // 左侧图标
            ZStack {
                Circle()
                    .fill(isSellGrid ? Color.red.opacity(0.1) : Color.green.opacity(0.1))
                    .frame(width: 40, height: 40)
                
                Image(systemName: isSellGrid ? "arrow.up.right" : "arrow.down.right")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(isSellGrid ? .red : .green)
            }
            .padding(.trailing, 8)
            
            // 中间内容
            VStack(alignment: .leading, spacing: 6) {
                Text(grid.strategyName)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.primary)
                
                HStack(spacing: 12) {
                    // 触发价
                    HStack(spacing: 4) {
                        // Image(systemName: "chart.line.uptrend.xyaxis")
                        //     .font(.system(size: 10))
                        //     .foregroundColor(.gray)

                        Text("触发价")
                            .font(.system(size: 13))
                            .foregroundColor(.secondary)
                        
                        Text(hideSensitiveInfo ? "*" : Utils.displayPrice(price: grid.triggerPrice))
                            .font(.system(size: 13))
                            .foregroundColor(.secondary)
                    }
                    
                    // 当前价
                    HStack(spacing: 4) {
                        // Image(systemName: "dollarsign.circle")
                        //     .font(.system(size: 10))
                        //     .foregroundColor(.gray)

                        Text("当前价")
                            .font(.system(size: 13))
                            .foregroundColor(.secondary)
                        
                        Text(hideSensitiveInfo ? "*" : Utils.displayPrice(price: grid.curPrice))
                            .font(.system(size: 13))
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Spacer()
            
            // 右侧状态
            VStack(alignment: .trailing) {
                if isSellGrid {
                    if grid.triggerPercent <= 0 {
                        StatusBadge(text: "已触发", color: .red)
                    } else {
                        Text("\(Utils.displayPrice(price: grid.triggerPercent, num: 2)) %")
                            .font(.system(size: 15, weight: .bold))
                            .foregroundColor(.red)
                    }
                } else {
                    if grid.triggerPercent >= 0 {
                        StatusBadge(text: "已触发", color: .green)
                    } else {
                        Text("\(Utils.displayPrice(price: grid.triggerPercent, num: 2)) %")
                            .font(.system(size: 15, weight: .bold))
                            .foregroundColor(.green)
                    }
                }
            }
        }
        .padding(.vertical, 16)
        .padding(.horizontal, 20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Material.regularMaterial)
                .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 4)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(
                    LinearGradient(
                        colors: [
                            .white.opacity(0.6),
                            .white.opacity(0.3),
                            .clear
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1
                )
        )
    }
}

// 状态标签组件
struct StatusBadge: View {
    var text: String
    var color: Color
    
    var body: some View {
        Text(text)
            .font(.system(size: 13, weight: .bold))
            .foregroundColor(.white)
            .padding(.horizontal, 10)
            .padding(.vertical, 5)
            .background(
                Capsule()
                    .fill(color)
            )
    }
}

// 移除不再使用的PriceInfoColumn结构
