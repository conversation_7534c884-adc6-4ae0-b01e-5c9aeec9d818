//
//  RetainTradeHistoryView.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/4/1.
//

import SwiftUI

struct RetainSelledCardView: View {
    var tradeLog: FlexibleTradeLog
    
    var body: some View {
        HStack {
            Text("卖出")
                .font(.caption)
                .padding() // 增加文本周围的空间
                .overlay(
                    Capsule()
                        .stroke(lineWidth: 1)
                        .frame(width: 40, height: 28)
                )
                .foregroundColor(.red)
            VStack(alignment: .leading) {
                Text("价格")
                    .foregroundColor(.gray)
                    .font(.caption2)
                Text(tradeLog.displayTradePrice)
                    .font(.caption2)
            }.padding()
            
            VStack(alignment: .leading) {
                Text("股数")
                    .foregroundColor(.gray)
                    .font(.caption2)
                Text(String(tradeLog.tradeShares))
                    .font(.caption2)
            }.padding()
            
            VStack(alignment: .leading) {
                Text("时间")
                    .foregroundColor(.gray)
                    .font(.caption2)
                Text(Utils.displayDate(date: tradeLog.tradeAt))
                    .font(.caption2)
            }.padding()
            
            Spacer()
        }
//        .background(Color.red.opacity(0.1))
        .cornerRadius(20)
//        .padding([.horizontal])
    }
    
    init(tradeLog: FlexibleTradeLog) {
        self.tradeLog = tradeLog
    }
}

struct RetainTradeHistoryView: View {
    @Bindable var strategy: Strategy
    
    var body: some View {
        List(strategy.flexibleTradeLog!) {
            RetainSelledCardView(tradeLog: $0)
        }
//        ScrollView {
//            ZStack {
//                Color(UIColor.secondarySystemBackground)
//                    .edgesIgnoringSafeArea(.all)
//                
//                VStack {
//                    ForEach(strategy.flexibleTradeLog!) { log in
//                        VStack {
//                            HStack {
//                                VStack(alignment: .leading) {
//                                    Text("档位")
//                                        .font(.caption2)
//                                        .foregroundColor(.gray)
//                                    Text("-")
//                                        .font(.callout)
//                                        .padding(.bottom)
//                                    Text("盈利金额")
//                                        .font(.caption2)
//                                        .foregroundColor(.gray)
//                                    Text(log.displayProfit)
//                                        .font(.callout)
//                                        .padding(.bottom)
//                                }
//                                Spacer()
//                                VStack(alignment: .leading) {
//                                    Text("网格类型")
//                                        .font(.caption2)
//                                        .foregroundColor(.gray)
//                                    Text("预留利润")
//                                        .font(.callout)
//                                        .padding(.bottom)
//                                    Text("卖出日期")
//                                        .font(.caption2)
//                                        .foregroundColor(.gray)
//                                    Text(Utils.displayDate(date:log.tradeAt))
//                                        .font(.callout)
//                                }
//                                Spacer()
//                            }
//                            .padding([.horizontal])
//
//                            Divider()
//                                .padding()
//                            RetainSelledCardView(tradeLog: log)
//                        }
//                        .padding(.vertical)
//                        .background(Color.white)
//                        .cornerRadius(20)
//                        .contextMenu {
//                            Button(action: {
//                                strategy.deleteFlexibleTradeLog(tradeLog: log)
//                            }, label: {
//                                Text("删除")
//                            })
//                        }
//                    }
//                }
//            }
//        }
//        .padding()
        .background(Color(UIColor.secondarySystemBackground))
            .navigationTitle("交易历史")
            .navigationBarTitleDisplayMode(.inline)
    }
}

//#Preview {
//    RetainTradeHistoryView()
//}
