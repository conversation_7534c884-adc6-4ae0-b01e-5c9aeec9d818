//
//  LandingView.swift
//  GridTradingToolbox
//
//  Created by 陈敏杰 on 2024/1/27.
//

import SwiftUI
import SwiftData

struct LandingView: View {
    @EnvironmentObject var settings: Settings
    @Environment(\.modelContext) var modelContext;
        
    let images = ["landing-1", "landing-2", "landing-3"]
    var body: some View {
        NavigationStack {
            VStack {
                TabView {
                    VStack {
                        Image("\(images[0])")
                            .resizable()
                            .scaledToFit()
                            .frame(height: 400)
                            .padding()
                        Text("快速计算网格")
                            .font(.title2)
                            .bold()
                        Text("快速计算各个网格档位的价格，投入与收益信息，并进行压力测试，让策略运行无忧。")
                            .padding()
                        Spacer()
                    }
                    
                    VStack {
                        Image("\(images[1])")
                            .resizable()
                            .scaledToFit()
                            .frame(height: 400)
                            .padding()
                        Text("统计收益与投入")
                            .font(.title2)
                            .bold()
                        Text("及时统计各个网格交易策略的收益与投入数据，对策略运行状况了然于胸。")
                            .padding()
                        Spacer()
                    }
                    
                    VStack {
                        Image("\(images[2])")
                            .resizable()
                            .scaledToFit()
                            .frame(height: 400)
                            .padding()
                        Text("子策略增加收益")
                            .font(.title2)
                            .bold()
                        Text("支持不同的增加收益率的子策略，让收益狂奔。")
                            .padding()
                        
                        Spacer()
                        Button(action: {
                            settings.appFirstLaunch = false
                        }, label: {
                            Text("开始体验")
                                .font(.system(size: 17))
                                .bold()
                                .frame(width: 120, height: 44)
                                .foregroundColor(.white)
                                .background(.red)
                                .cornerRadius(20)
                        })
                        Spacer()
                    }
                }
                .tabViewStyle(PageTabViewStyle())
                .indexViewStyle(PageIndexViewStyle(backgroundDisplayMode: .always))
            }
        }
    }
}

//#Preview {
//    LandingView()
//}
