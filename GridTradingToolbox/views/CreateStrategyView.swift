//
//  CreateStrategyView.swift
//  GridTradingToolbox
//
//  Created by 陈敏杰 on 2024/1/2.
//

import SwiftUI
import SwiftData
import UserNotifications

struct CreateStrategyView: View {
    enum Field {
        case name
        case targetPrice
        case amount
        case incrementalBuyRatio
        case interval
        case maxFall
        case retainProfit
        case mediumInterval
        case largeInterval
        case interest
    }
    
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.modelContext) private var modelContext
    @EnvironmentObject var settings: Settings
    
    @Bindable private var strategy = Strategy()
    @State private var isEditMode = false
    
    @State private var name = ""
    @State private var targetPrice = ""
    @State private var amount = ""
    @State private var buyStrategy = "2"
    @State private var incrementalBuyRatio = "5"
    @State private var fixedShares = ""
    @State private var interval = "5"
    @State private var retainProfitSwitch = false
    @State private var retainProfit = "1"
    @State private var mediumLargeSwitch = false
    @State private var triggerNotify = false
    @State private var calculateInterest = false
    @State private var mediumInterval = "15"
    @State private var largeInterval = "30"
    @State private var interestPeriod = "2"
    @State private var interest = "3"
    @State private var code = ""
    @State private var maxFall = "60"
    
    @State private var searchText = ""
    @State private var showStocksSearch = false
    
    @State private var errorMsg = ""
    @State private var showErrorMsg = false
    @State private var showAlert = false
    
    @State private var showStressTest = false
    @State private var grids: [StressGrid] = []
    
    @State private var showPro = false
    
    @FocusState private var focusedField: Field?
    
    private var hasGrid = false;
    
    var disableSave: Bool {
        if buyStrategy == "3" {
            return name.isEmpty || targetPrice.isEmpty || fixedShares.isEmpty || interval.isEmpty || maxFall.isEmpty
        } else {
            return name.isEmpty || targetPrice.isEmpty || amount.isEmpty || interval.isEmpty || maxFall.isEmpty
        }
    }
    var disableStressTest: Bool {
        if buyStrategy == "3" {
            return targetPrice.isEmpty || fixedShares.isEmpty || interval.isEmpty || maxFall.isEmpty
        } else {
            return targetPrice.isEmpty || amount.isEmpty || interval.isEmpty || maxFall.isEmpty
        }
    }
    
    init() {}
    
    init(strategy: Strategy, isEditMode: Bool = false) {
        if isEditMode {
            self.strategy = strategy
            if (strategy.holdGrids.count > 0) {
                self.hasGrid = true
            }
            _isEditMode = State(initialValue: isEditMode)
            _name = State(initialValue: strategy.name)
            _targetPrice = State(initialValue: Utils.trimTrailingZeros(from: strategy.displayTargetPrice))
            _amount  = State(initialValue: Utils.trimTrailingZeros(from: strategy.displayAmount))
            _buyStrategy = State(initialValue: String(strategy.buyStrategy))
            if strategy.buyStrategy == Strategy.BuyStrategy.incremental.rawValue {
                _incrementalBuyRatio  = State(initialValue: Utils.trimTrailingZeros(from: strategy.displayIncrementalBuyRatio))
            }
            if strategy.buyStrategy == Strategy.BuyStrategy.fixedShares.rawValue {
                _fixedShares = State(initialValue: String(strategy.fixedShares!))
            }
            _interval  = State(initialValue: Utils.trimTrailingZeros(from: strategy.displayInterval))
            _retainProfitSwitch = State(initialValue: strategy.retainProfitSwitch)
            if strategy.retainProfitSwitch {
                _retainProfit =  State(initialValue: Utils.trimTrailingZeros(from: strategy.displayRetainProfit))
            }
            _mediumLargeSwitch  = State(initialValue: strategy.mediumLargeSwitch)
            if strategy.mediumLargeSwitch {
                _mediumInterval = State(initialValue: Utils.trimTrailingZeros(from: strategy.displayMediumInterval))
                _largeInterval = State(initialValue: Utils.trimTrailingZeros(from: strategy.displayLargeInterval))
            }
            _triggerNotify = State(initialValue: strategy.triggerNotify)
            if strategy.triggerNotify {
                _code = State(initialValue: strategy.code!)
            }
            _calculateInterest = State(initialValue: strategy.calculateInterest)
            if strategy.calculateInterest {
                _interest = State(initialValue: Utils.trimTrailingZeros(from: strategy.displayInterest))
                if strategy.interestPeriod == nil {
                    _interestPeriod = State(initialValue: "2")
                } else {
                    _interestPeriod = State(initialValue: String(strategy.interestPeriod!))
                }
            }
            _maxFall = State(initialValue: Utils.trimTrailingZeros(from: strategy.displayMaxFall))
        }
    }
    
    var body: some View {
        Form {
            Section(footer: showErrorMsg ? Text(errorMsg).foregroundColor(.red) : nil) {
                HStack {
                    Text("策略名称")
                    TextField("如: 传媒ETF", text: $name)
                        .multilineTextAlignment(.trailing)
                        .focused($focusedField, equals: .name)
                        .submitLabel(.next)
                }
                
                HStack {
                    Text("触发价格")
                    TextField("第一网买入触发价", text: $targetPrice)
                        .multilineTextAlignment(.trailing)
                        .keyboardType(.numbersAndPunctuation)
                        .disabled(hasGrid)
                        .foregroundStyle(hasGrid ? .gray : .black)
                        .onChange(of: targetPrice, initial: false) { oldValue, newValue in
                            if newValue.count == 0 {
                                return;
                            }
                            let result = Utils.checkNumberFormat(number: newValue)
                            if !result {
                                targetPrice = oldValue
                            }
                        }
                        .focused($focusedField, equals: .targetPrice)
                        .submitLabel(.done)
                }
                
                Picker("买入策略", selection: $buyStrategy.animation()) {
                    Text("固定股数买入").tag("3")
                    Text("等比递增买入").tag("2")
                    Text("等额买入").tag("1")
                }
                .disabled(hasGrid)

                if buyStrategy != "3" {
                    HStack {
                        Text("- 网格金额")
                        TextField("每网买入金额", text: $amount)
                            .multilineTextAlignment(.trailing)
                            .keyboardType(.numbersAndPunctuation)
                            .disabled(hasGrid)
                            .foregroundStyle(hasGrid ? .gray : .black)
                            .onChange(of: amount, initial: false) { oldValue, newValue in
                                if newValue.count == 0 {
                                    return;
                                }
                                let result = Utils.checkNumberFormat(number: newValue)
                                if !result {
                                    amount = oldValue
                                }
                            }
                            .focused($focusedField, equals: .amount)
                            .submitLabel(.done)
                    }
                    .transition(.asymmetric(
                        insertion: .scale.combined(with: .opacity),
                        removal: .scale.combined(with: .opacity)
                    ))
                }
                
                if buyStrategy == "2" {
                    HStack {
                        Text("- 递增比例")
                        TextField("每网买入金额", text: $incrementalBuyRatio)
                            .multilineTextAlignment(.trailing)
                            .keyboardType(.numbersAndPunctuation)
                            .disabled(hasGrid)
                            .foregroundStyle(hasGrid ? .gray : .black)
                            .onChange(of: incrementalBuyRatio, initial: false) { oldValue, newValue in
                                if newValue.count == 0 {
                                    return;
                                }
                                let result = Utils.checkNumberFormat(number: newValue)
                                if !result {
                                    incrementalBuyRatio = oldValue
                                }
                            }
                            .submitLabel(.done)
                        Text("%").font(.callout)
                    }
                    .transition(.asymmetric(
                        insertion: .scale.combined(with: .opacity),
                        removal: .scale.combined(with: .opacity)
                    ))
                } else if buyStrategy == "3" {
                    HStack {
                        Text("- 固定股数")
                        TextField("每网买入股数", text: $fixedShares)
                            .multilineTextAlignment(.trailing)
                            .keyboardType(.numbersAndPunctuation)
                            .disabled(hasGrid)
                            .foregroundStyle(hasGrid ? .gray : .black)
                            .onChange(of: fixedShares, initial: false) { oldValue, newValue in
                                if newValue.count == 0 {
                                    return;
                                }
                                let result = Utils.checkNumberFormat(number: newValue)
                                if !result {
                                    fixedShares = oldValue
                                }
                            }
                    }
                    .transition(.asymmetric(
                        insertion: .scale.combined(with: .opacity),
                        removal: .scale.combined(with: .opacity)
                    ))
                }
                
                HStack {
                    Text("网格幅度")
                    TextField("", text: $interval)
                        .multilineTextAlignment(.trailing)
                        .keyboardType(.numbersAndPunctuation)
                        .disabled(hasGrid)
                        .foregroundStyle(hasGrid ? .gray : .black)
                        .onChange(of: interval, initial: false) { oldValue, newValue in
                            if newValue.count == 0 {
                                return;
                            }
                            let result = Utils.checkNumberFormat(number: newValue)
                            if !result {
                                interval = oldValue
                            }
                        }
                        .submitLabel(.done)
                    Text("%").font(.callout)
                }
                
                HStack {
                    Text("最大跌幅")
                    TextField("", text: $maxFall)
                        .multilineTextAlignment(.trailing)
                        .keyboardType(.numbersAndPunctuation)
                        .disabled(hasGrid)
                        .foregroundStyle(hasGrid ? .gray : .black)
                        .onChange(of: maxFall, initial: false) { oldValue, newValue in
                            if newValue.count == 0 {
                                return;
                            }
                            let result = Utils.checkNumberFormat(number: newValue)
                            if !result {
                                maxFall = oldValue
                            }
                        }
                        .submitLabel(.done)
                    Text("%").font(.callout)
                }
                Toggle(isOn: $triggerNotify) {
                    HStack{
                        Text("实时行情")
                        Image(systemName: "crown")
                            .foregroundColor(.yellow)
                    }
                }.onChange(of: triggerNotify) { oldValue, newValue in
                    if newValue {
                        if !settings.subscriptionMember {
                            showPro = true
                            triggerNotify = false
                        } else {
                            showStocksSearch = true
                            triggerNotify = true
                        }
                    }
                }
                

                if triggerNotify {
                    HStack {
                        Text("- 股票代码")
                        Spacer()
                        if code.isEmpty {
                            Text("点击搜索股票代码")
                                .foregroundColor(.gray).opacity(0.5)
                        } else {
                            Text(code)
                        }
                    }.onTapGesture {
                        showStocksSearch = true
                    }.sheet(isPresented: $showStocksSearch) {
                        StocksSearchView(code: $code)
                    }
                }
            }
            .onSubmit {
                switch focusedField {
                case .name:
                    focusedField = .targetPrice
                case .targetPrice:
                    focusedField = .amount
                default:
                    print("done")
                }
            }
            .animation(.spring(duration: 0.3), value: buyStrategy)
            
            
            Section(header: Text("子策略")) {
                Toggle("保留利润", isOn: $retainProfitSwitch)
                    .disabled(hasGrid)
                if retainProfitSwitch {
                    HStack {
                        Text("- 保留倍数")
                        TextField("", text: $retainProfit)
                            .multilineTextAlignment(.trailing)
                            .keyboardType(.numbersAndPunctuation)
                            .disabled(hasGrid)
                            .foregroundStyle(hasGrid ? .gray : .black)
                            .onChange(of: retainProfit, initial: false) { oldValue, newValue in
                                if newValue.count == 0 {
                                    return;
                                }
                                let result = Utils.checkNumberFormat(number: newValue)
                                if !result {
                                    retainProfit = oldValue
                                }
                            }
                            .submitLabel(.done)
                    }
                }
                
                Toggle("中网大网", isOn: $mediumLargeSwitch)
                    .disabled(hasGrid)
                
                if mediumLargeSwitch {
                    HStack {
                        Text("- 中网幅度")
                        TextField("", text: $mediumInterval)
                            .multilineTextAlignment(.trailing)
                            .keyboardType(.numbersAndPunctuation)
                            .disabled(hasGrid)
                            .foregroundStyle(hasGrid ? .gray : .black)
                            .onChange(of: mediumInterval, initial: false) { oldValue, newValue in
                                if newValue.count == 0 {
                                    return;
                                }
                                let result = Utils.checkNumberFormat(number: newValue)
                                if !result {
                                    mediumInterval = oldValue
                                }
                            }
                            .submitLabel(.done)
                        Text("%").font(.callout)
                    }
                    
                    HStack {
                        Text("- 大网幅度")
                        TextField("", text: $largeInterval)
                            .multilineTextAlignment(.trailing)
                            .keyboardType(.numbersAndPunctuation)
                            .disabled(hasGrid)
                            .foregroundStyle(hasGrid ? .gray : .black)
                            .onChange(of: largeInterval, initial: false) { oldValue, newValue in
                                if newValue.count == 0 {
                                    return;
                                }
                                let result = Utils.checkNumberFormat(number: newValue)
                                if !result {
                                    largeInterval = oldValue
                                }
                            }
                            .submitLabel(.done)
                        Text("%").font(.callout)
                    }
                }
                
                Toggle(isOn: $calculateInterest) {
                    HStack{
                        Text("底仓计息")
                        Image(systemName: "crown")
                            .foregroundColor(.yellow)
                    }
                }
                .onChange(of: calculateInterest) { oldValue, newValue in
                    if newValue {
                        if !settings.subscriptionMember {
                            showPro = true
                            calculateInterest = false
                        } else {
                            UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { success, error in
                                if success {
                                    print("Notification set!")
                                } else if let error {
                                    print(error.localizedDescription)
                                }
                            }
                        }
                    }
                }
                .sheet(isPresented: $showPro) {
                    ProMemberView()
                }
                
                if calculateInterest {
                    
                    HStack{
                        Text("- 计息周期")
                        Spacer()
                        Picker("计息周期", selection: $interestPeriod) {
                            Text("月").tag("1")
                            Text("年").tag("2")
                        }
//                        .disabled(hasGrid)
                        .pickerStyle(.segmented)
                        .frame(width: 120)
                    }
                    
                    HStack {
                        Text("- 每期利息")
                        TextField("每个周期的利息", text: $interest)
                            .multilineTextAlignment(.trailing)
                            .keyboardType(.numbersAndPunctuation)
                            .onChange(of: interest, initial: false) { oldValue, newValue in
                                if newValue.count == 0 {
                                    return;
                                }
                                let result = Utils.checkNumberFormat(number: newValue)
                                if !result {
                                    interest = oldValue
                                }
                            }
                            .submitLabel(.done)
                        Text("%").font(.callout)
                    }
                    
                }
            }
        }
        .navigationTitle(isEditMode ? "编辑策略" : "新建策略")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            Button(action: {
                let triggerPrice = Utils.times1000Round(price: targetPrice)
                let tAmount = amount.isEmpty ? 0 : Utils.times1000Round(price: amount)
                let tBuyStrategy = (buyStrategy as NSString).integerValue
                let tIncrementalBuyRatio = tBuyStrategy == Strategy.BuyStrategy.equal.rawValue ? nil : Utils.times1000Round(price: incrementalBuyRatio)
                let tFixedShares = tBuyStrategy == Strategy.BuyStrategy.fixedShares.rawValue ? (fixedShares as NSString).integerValue : nil
                let tInterval = Utils.times1000Round(price: interval)
                let tMaxFall = Utils.times1000Round(price: maxFall)
                let mInterval = mediumInterval.isEmpty ? nil : Utils.times1000Round(price: mediumInterval)
                let lInterval = largeInterval.isEmpty ? nil : Utils.times1000Round(price: largeInterval)
                
                let result = Strategy.isValidForStressTest(triggerPrice: triggerPrice, amount: tAmount, buyStrategy: tBuyStrategy, incrementalBuyRatio: tIncrementalBuyRatio, interval: tInterval, maxFall: tMaxFall, mediumLargeSwitch: mediumLargeSwitch, mediumInterval: mInterval, largeInterval: lInterval, fixedShares: tFixedShares)
                if (result.0) {
                    grids = Strategy.stressTest(triggerPrice: triggerPrice, amount: tAmount, buyStrategy: tBuyStrategy, incrementalBuyRatio: tIncrementalBuyRatio, interval: tInterval, maxFall: tMaxFall, mediumLargeSwitch: mediumLargeSwitch, mediumInterval: mInterval, largeInterval: lInterval, fixedShares: tFixedShares)
                    showStressTest = true
                } else {
                    showAlert = true
                    showErrorMsg = true
                    errorMsg = result.1
                }
            }, label: {
                Image(systemName: "gauge.with.needle")
            })
            .disabled(disableStressTest)
            .alert(isPresented: $showAlert, content: {
                Alert(title: Text("压力测试失败"), message: Text(errorMsg), dismissButton: .default(Text("OK")))
            })
            .sheet(isPresented: $showStressTest) {
                let triggerPrice = Utils.times1000Round(price: targetPrice)
                let tMaxFall = Utils.times1000Round(price: maxFall)
                let minPrice = (100000 - tMaxFall) * triggerPrice  / 100000
                StressTestView(grids: $grids, minPrice: minPrice, mediumLargeSwitch: mediumLargeSwitch)
            }
            
            Button("保存") {
                saveStrategy()
            }.disabled(disableSave)
                .alert(isPresented: $showAlert, content: {
                    Alert(title: Text("保存失败"), message: Text(errorMsg), dismissButton: .default(Text("OK")))
                })
            
        }
        .onAppear {
            print("CreateStrategyView onAppear")
        }
    }

    private func saveStrategy() {
        let tmpStrategy = Strategy(name: name, code: code, interval: interval, targetPrice: targetPrice, amount: amount, maxFall: maxFall, buyStrategy: buyStrategy, incrementalBuyRatio: incrementalBuyRatio, mediumLargeSwitch: mediumLargeSwitch, mediumInterval: mediumInterval, largeInterval: largeInterval, triggerNotify: triggerNotify, calculateInterest: calculateInterest, interestPeriod: interestPeriod, interest: interest, retainProfitSwitch: retainProfitSwitch, retainProfit: retainProfit, fixedShares: fixedShares)
        let result = tmpStrategy.isValid()
        if (result.0) {
            if isEditMode {
                strategy.editFrom(source: tmpStrategy)
            } else {
                modelContext.insert(tmpStrategy)
            }
            try? modelContext.save()
            self.presentationMode.wrappedValue.dismiss()
        } else {
            showAlert = true
            showErrorMsg = true
            errorMsg = result.1
        }
    }
}

//#Preview {
//    CreateStrategyView()
//}
