//
//  BuyGridView.swift
//  GridTradingToolbox
//
//  Created by 陈敏杰 on 2024/1/16.
//

import SwiftUI
import SwiftData
import UserNotifications

struct BuyGridView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    
    @Query(sort: \StockAccount.createAt, order: .reverse) private var accounts: [StockAccount]
        
    @Bindable var strategy: Strategy
    @Bindable var grid: TradeGrid
    
    @State var showErrorMsg = false
    @State var errorMsg = ""
    @State var isSaving = false
    
    @State var buyPrice = ""
    @State var buyShares = ""
    @State var buyDate = Date()
    @State var accountIndex = "default"
    @State var moodInex = "default"
    @State var note = ""
    
    @FocusState private var isSharesFocused: Bool
    
    var isEditMode = false
    var preInvestment = 0
    var preProfit = 0
    
    var curStockAcc: StockAccount? {
        if isEditMode {
            if accountIndex == "default" {
                return nil
            } else {
                return accounts.first(where: { acc in
                    acc.id == accountIndex
                })
            }
        } else {
            return accounts.first(where: { acc in
                acc.id == accountIndex
            })
        }
    }

    var body: some View {
        NavigationStack {
            Form {
                HStack {
                    VStack(alignment: .leading) {
                        Text("买入金额").font(.caption)
                            .foregroundColor(.gray)
                        Text(Utils.trimTrailingZeros(from: String(format: "%.3f", (buyPrice as NSString).doubleValue * (buyShares as NSString).doubleValue)))
                            .font(.title)
                            .foregroundColor(.green)
                    }
                    Spacer()
                    if curStockAcc != nil {
                        VStack(alignment: .leading) {
                            Text("交易佣金").font(.caption)
                                .foregroundColor(.gray)
                            Text(Utils.displayPrice(price: curStockAcc!.calTrxFee(amount: (buyPrice as NSString).doubleValue * (buyShares as NSString).doubleValue), num: 2))
                                .font(.title)
                                .foregroundColor(.green)
                        }
                    }
                    Spacer()
                }
                Section(footer: showErrorMsg ? Text(errorMsg).foregroundColor(.red) : nil) {
                    HStack {
                        Text("买入价格")
                        TextField("", text: $buyPrice)
                            .multilineTextAlignment(.trailing)
                            .keyboardType(.numbersAndPunctuation)
                            .onChange(of: buyPrice, initial: false) { oldValue, newValue in
                                if newValue.count == 0 {
                                    return;
                                }
                                let result = Utils.checkNumberFormat(number: newValue)
                                if !result {
                                    buyPrice = oldValue
                                }
                            }
                            .submitLabel(.done)
                    }
                    
                    HStack {
                        Text("买入股数")
                        TextField("", text: $buyShares)
                            .multilineTextAlignment(.trailing)
                            .keyboardType(.numbersAndPunctuation)
                            .focused($isSharesFocused)
                            .onChange(of: isSharesFocused) { focused in
                                if !focused {
                                    buyShares = Utils.formatShares(buyShares)
                                }
                            }
                            .submitLabel(.done)
                    }
                    
                    HStack {
                        Text("买入日期")
                        Spacer()
                        DatePicker("", selection: $buyDate, in: ...Date(), displayedComponents: .date)
                            .environment(\.locale, Locale.init(identifier: "zh_CN"))
                    }
                }
                
                Section {
                    Picker("证券账户", selection: $accountIndex) {
                        Text("无").tag("default")
                        ForEach(accounts, id: \.id) { account in
                            Text("\(account.name)")
                                .tag(account.id)
                        }
                    }
                }
                
                Section(header: Text("投资记录")) {
                    Picker("心态", selection: $moodInex) {
                        Text("🤑").tag("happy")
                        Text("😊").tag("smile")
                        Text("😶").tag("default")
                        Text("😱").tag("sad")
                        Text("😭").tag("cry")
                    }
                    TextField("记录当前的心理情绪与投资逻辑", text: $note, axis: .vertical)
                        .lineLimit(3)
                }
            }
            .navigationTitle(isEditMode ? "编辑买入记录" : "记录买入")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("返回") {
                        self.presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        if !isSaving {
                            isSaving = true
                            if checkBuyParams() {
                                recordBuyIn()
                                self.presentationMode.wrappedValue.dismiss()
                            } else {
                                isSaving = false
                            }
                        }
                    }
                    .disabled(isSaving)
                }
            }
        }
    }
    
    init(strategy: Strategy, grid: TradeGrid, isEditMode: Bool = false, stockAccountId: String = "default") {
        self.strategy = strategy
        self.grid = grid
        self.isEditMode = isEditMode
        
        if isEditMode {
            _buyPrice = State(initialValue: grid.buyTradeLog!.displayTradePrice)
            _buyShares = State(initialValue: String(grid.buyTradeLog!.tradeShares))
            _buyDate = State(initialValue: grid.buyTradeLog!.tradeAt)
            self.preInvestment = grid.buyTradeLog!.tradeShares * grid.buyTradeLog!.tradePrice
            _accountIndex = State(initialValue: grid.accountId ?? "default")
            _moodInex = State(initialValue: grid.buyTradeLog!.mood ?? "default")
            _note = State(initialValue: grid.buyTradeLog!.note ?? "")
        } else {
            _buyPrice = State(initialValue: grid.displayTheoreticalBuyPrice)
            _buyShares = State(initialValue: String(grid.theoreticalBuyShares))
            _accountIndex = State(initialValue: stockAccountId)
        }
    }

    @MainActor
    private func checkBuyParams() -> Bool {
        if isEditMode {
            if grid.isSold {
                if Calendar.current.compare(buyDate, to: grid.sellTradeLog!.tradeAt, toGranularity: .day) == .orderedDescending {
                    errorMsg = "买入日期应小于卖出日期"
                    showErrorMsg = true
                    return false
                }
            }
        }
        return true
    }
    
    @MainActor
    private func recordBuyIn() {
        buyShares = Utils.formatShares(buyShares)
        let buySharesValue = Int(buyShares)!
        let tradePrice = Utils.times1000Round(price: buyPrice)
        if isEditMode {
            let tradeLog = grid.buyTradeLog!
            grid.accountId = accountIndex
            
            if grid.isSold {
                grid.holdShares = buySharesValue - grid.sellTradeLog!.tradeShares
            } else {
                grid.holdShares = buySharesValue
                if strategy.calculateInterest && !Utils.isSameDay(tradeLog.tradeAt, buyDate) {
                    grid.cancelNotification()
                    grid.addNotification()
                }
                
            }
            grid.theoreticalSellShares = grid.recalculateTheoreticalSellShares(buyPrice: tradePrice, buyShares: buySharesValue) 
            tradeLog.tradePrice = tradePrice
            tradeLog.tradeShares = buySharesValue
            tradeLog.tradeAt = buyDate
            tradeLog.mood = moodInex
            tradeLog.note = note
            if !grid.isSold {
                strategy.totalInvestmentAmount = strategy.calculateTotalInvestment()
                strategy.totalProfitAmount = strategy.calculateTotalProfit()
                strategy.realTotalProfitAmount = strategy.calculateRealTotalProfit()
                strategy.setRetainShares(strategy.calculateRetainShares())
            }
            try? modelContext.save()

            // Task {
            //     if let dh = await dataHandler() {
            //         try await dh.updateBuyTradeLog(grid: grid, tradePrice: tradePrice, tradeShares: buySharesValue, tradeAt: buyDate, mood: moodInex, note: note)
            //     }
            // }

        } else {
            grid.strategy = strategy
            grid.accountId = accountIndex
            grid.holdShares += buySharesValue
            grid.theoreticalSellShares = grid.recalculateTheoreticalSellShares(buyPrice: tradePrice, buyShares: buySharesValue)
            let tradeLog = TradeLog(grid: grid, tradeType: TradeLog.TradeType.buy.rawValue, tradeShares: buySharesValue, tradePrice: tradePrice, tradeAt: buyDate, mood: moodInex, note: note)
            grid.tradeLogs!.append(tradeLog)
            modelContext.insert(grid)
            strategy.grids!.append(grid)
            strategy.totalInvestmentAmount = strategy.calculateTotalInvestment()
            
            try? modelContext.save()

            // Task {
            //     if let dh = await dataHandler() {
            //         try await dh.newGrid(grid: grid, tradeLog: tradeLog, strategy: strategy, accountIndex: accountIndex)
            //     }
            // }
        }
    }
}

//#Preview {
//    BuyGridView()
//}
