//
//  GridStatisticView.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/7/17.
//

import SwiftUI
import SwiftData
import MijickCalendarView

class CalendarViewModel: ObservableObject {
    @Published var curMonth = Date.now
    @Published var curDay: Date? = Date.now
    @Published var buyCountMonth: [String: [TradeLog]] = [:]
    @Published var sellCountMonth: [String: [TradeLog]] = [:]
}

struct GridCalendarView: View {
    @Environment(\.modelContext) var modelContext;
    
    @Query var currentMonthTradeLog: [TradeLog]
    
    @StateObject var calendarVM: CalendarViewModel = .init()
    
//    @State private var selectedDate: Date? = .now
//    @State private var selectedMonth = Date.now
    
    @State private var buyCount: [String: [TradeLog]] = [:]
    @State private var sellCount: [String: [TradeLog]] = [:]
//    @State private var buyCountMonth: [String: [TradeLog]] = [:]
//    @State private var sellCountMonth: [String: [TradeLog]] = [:]
    
    var body: some View {
        ZStack {
            Color(UIColor.secondarySystemBackground)
                .edgesIgnoringSafeArea(.all)
            
            VStack {
                MCalendarView(selectedDate: nil, selectedRange: nil) { config in
                    config
                        .daysHorizontalSpacing(8)
                        .daysVerticalSpacing(8)
                        .startMonth(calendarVM.curMonth)
                        .endMonth(calendarVM.curMonth)
                        .monthLabel({ date in
                            ButtonMonthLabelView(month: date, calendarVM: calendarVM)
                        })
                        .dayView { date, isCurrentMonth, selectedDate, range in
                            return GridCalendarView.ColoredRectangle(date: date, isCurrentMonth: isCurrentMonth, selectedDate: $calendarVM.curDay, selectedRange: range, buyGrids: buyCount[date.toDayString()]?.count ?? nil, sellGrids: sellCount[date.toDayString()]?.count ?? nil)
                        }
                }
                .fixedSize(horizontal: false, vertical: true)
                .scrollDisabled(true)
//                .selectionDisabled(true)
                .onAppear {
                    let filteredLog = currentMonthTradeLog.filter { log in
                        log.grid != nil && log.grid!.strategy != nil && log.tradeShares > 0
                    }
                    self.buyCount = Dictionary(grouping: filteredLog.filter({ log in
                        log.tradeType == TradeLog.TradeType.buy.rawValue && log.tradeShares > 0
                    })) { $0.tradeAt.toDayString() }
                    
                    self.sellCount = Dictionary(grouping: filteredLog.filter({ log in
                        log.tradeType == TradeLog.TradeType.sell.rawValue && log.tradeShares > 0
                    })) { $0.tradeAt.toDayString() }
                    
                    calendarVM.buyCountMonth = Dictionary(grouping: filteredLog.filter({ log in
                        log.tradeType == TradeLog.TradeType.buy.rawValue && log.tradeShares > 0
                    })) { $0.tradeAt.toDayString(format: "yyyy-M") }
                    
                    calendarVM.sellCountMonth = Dictionary(grouping: filteredLog.filter({ log in
                        log.tradeType == TradeLog.TradeType.sell.rawValue && log.tradeShares > 0
                    })) { $0.tradeAt.toDayString(format: "yyyy-M") }
                }
//                .task {
//                    currentMonthTradeLog.forEach{ log in
//                        if log.grid == nil {
//                            modelContext.delete(log)
//                        } else {
//                            if log.grid!.strategy == nil {
//                                modelContext.delete(log.grid!)
//                            }
//                        }
//                    }
//                }
                
//                Divider()
                
                VStack {
                    Text(calendarVM.curDay?.toDayString(format: "M月d日EEEE") ?? "")
                        .bold()
                        .frame(maxWidth: .infinity, alignment: .leading)
                    ScrollView {
                        ForEach(tradeLogsOfSelectedDay, id: \.self) { log in
                            HStack(spacing: 8) {
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(log.isBuy ? Color.green.opacity(0.618) : Color.red.opacity(0.618))
                                    .frame(width: 6, height: 20)
                                VStack(spacing: 4) {
                                    Text((log.grid?.strategy?.name ?? "") + " - " + (log.grid?.displayGrade ?? ""))
                                        .font(.footnote)
                                        .fontWeight(.semibold)
                                        .frame(maxWidth: .infinity, alignment: .leading)
                                        
                                    HStack(spacing: 8) {
                                        Text(Utils.displayPrice(price: log.tradePrice) + "元")
                                            .font(.caption2)
                                            .frame(maxWidth: .infinity, alignment: .leading)
                                        Text(String(log.tradeShares) + "股")
                                            .font(.caption2)
                                            .frame(maxWidth: .infinity, alignment: .leading)
                                    }
                                    .foregroundColor(.gray)
                                }
                            }
                            .padding(.bottom, 8)
                        }
                    }
                }
                .padding(.horizontal)
                Spacer()
            }
            .padding()
            .background(.white)
            .clipShape(RoundedRectangle(cornerRadius: 20, style: .continuous))
            .shadow(color: .primary.opacity(0.2), radius: 5, x: 0, y: 5)
            .padding()
        }
        .navigationTitle("日历视图")
        .navigationBarTitleDisplayMode(.inline)
        
    }
    
    var tradeLogsOfSelectedDay: [TradeLog] {
        return (sellCount[calendarVM.curDay!.toDayString()] ?? []) + (buyCount[calendarVM.curDay!.toDayString()] ?? [])
    }
    
    private struct ButtonMonthLabelView: MonthLabel {
        var month: Date
        
        @StateObject var calendarVM: CalendarViewModel
        
//        init(month: Date, calendarVM: CalendarViewModel) {
//            self.month = month
//            self._calendarVM = calendarVM
//        }
        
        func createContent() -> AnyView { createCostomContent().erased() }
        
        func createCostomContent() -> some View {
            VStack {
                HStack {
                    Button {
                        let calendar = Calendar.current
                        let components = calendar.dateComponents([.year, .month], from: calendarVM.curMonth)
                        let firstDayOfMonth = calendar.date(from: components)!
                        let previousMonthDate = calendar.date(byAdding: .month, value: -1, to: firstDayOfMonth)
                        calendarVM.curMonth = previousMonthDate ?? .now
                    } label: {
                        Image(systemName: "chevron.left")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 8, height: 8)
                    }
                    .padding(.horizontal)
                    
                    
                    Text(getString(format: "y年M月"))
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.black)
                    
                    Button {
                        let calendar = Calendar.current
                        let components = calendar.dateComponents([.year, .month], from: calendarVM.curMonth)
                        let firstDayOfMonth = calendar.date(from: components)!
                        let nextMonthDate = calendar.date(byAdding: .month, value: 1, to: firstDayOfMonth)
                        calendarVM.curMonth = nextMonthDate ?? .now
                    } label: {
                        Image(systemName: "chevron.right")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 8, height: 8)
                    }
                    .padding(.horizontal)
                }
                
                HStack {
                    Spacer()
                    VStack {
                        Text("买入网格")
                            .font(.caption2)
                            .foregroundStyle(.secondary)
                        Text(String(calendarVM.buyCountMonth[calendarVM.curMonth.toDayString(format: "yyyy-M")]?.count ?? 0))
                            .font(.footnote)
                            .fontWeight(.semibold)
                            .foregroundStyle(.green)
                    }
                    .padding(4)
//                    .background(.green.opacity(0.2))
//                    .clipShape(RoundedRectangle(cornerRadius: 8, style: .continuous))
                    Spacer()
                    VStack {
                        Text("卖出网格")
                            .font(.caption2)
                            .foregroundStyle(.secondary)
                        Text(String(calendarVM.sellCountMonth[calendarVM.curMonth.toDayString(format: "yyyy-M")]?.count ?? 0))
                            .font(.footnote)
                            .fontWeight(.semibold)
                            .foregroundStyle(.red)
                    }
                    .padding(4)
//                    .background(.red.opacity(0.2))
//                    .clipShape(RoundedRectangle(cornerRadius: 8, style: .continuous))
                    Spacer()
                }
//                .padding()
            }
        }
    }
}

extension GridCalendarView { struct ColoredRectangle: DayView {
    var date: Date
    var color: Color?
    var isCurrentMonth: Bool
    var selectedDate: Binding<Date?>?
    var selectedRange: Binding<MijickCalendarView.MDateRange?>?
    var buyGrids: Int?
    var sellGrids: Int?
}}

extension GridCalendarView.ColoredRectangle {
    func createDayLabel() -> AnyView {
        ZStack {
            if isSelected() {
                RoundedRectangle(cornerRadius: 8)
                    .fill(.blue.opacity(0.1))
            }
            
            VStack(spacing: 0) {
                Text(getStringFromDay(format: "d"))
                    .font(.body)
                    .foregroundStyle(.black)
                
                Text(buyGrids == nil ? "0" : "买" + String(buyGrids!))
                    .font(.caption2)
                    .foregroundStyle(.green)
                    .padding(2)
                    .background(.green.opacity(0.2))
                    .clipShape(RoundedRectangle(cornerRadius: 4, style: .continuous))
                    .opacity(buyGrids == nil ? 0 : 1)
                
                Text(sellGrids == nil ? "0" : "卖" + String(sellGrids!))
                    .font(.caption2)
                    .foregroundStyle(.red)
                    .padding(2)
                    .background(.red.opacity(0.2))
                    .clipShape(RoundedRectangle(cornerRadius: 4, style: .continuous))
                    .opacity(sellGrids == nil ? 0 : 1)
            }
            .padding(4)
        }
        .erased()
    }
}


// MARK: - On Selection Logic
extension GridCalendarView.ColoredRectangle {
    
    func createSelectionView() -> AnyView {
        EmptyView().erased()
    }
}

struct TopMonthView: View {
    let currentMonth: Date
    @State private var _currentMonth: Date = .now

    var body: some View {
        HStack(spacing: 2) {
            createText()
            createIcon()
        }
        .animation(.bouncy, value: _currentMonth)
//        .onReceive(currentMonth) { _currentMonth = $0 }
    }
}
private extension TopMonthView {
    func createText() -> some View {
        Text(text)
            .font(.body)
            .fontWeight(.semibold)
            .foregroundStyle(.primary)
            .contentTransition(.numericText(countsDown: true))
    }
    func createIcon() -> some View {
        Image("chevron-down")
            .resizable()
            .frame(width: 20, height: 20)
            .background(.black)
//            .foregroundStyle(.black)
    }
}
private extension TopMonthView {
    var text: String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "MMMM yyyy"
        return dateFormatter.string(from: _currentMonth)
    }
}

// MARK: - ViewModel
// NOTE: EndMonth value was marked as CurrentValueSubject in order to optimise the view - otherwise the CalendarView would refresh every time a new month was loaded.
fileprivate class ViewModel: ObservableObject {
    @Published var selectedDate: Date? = nil
    var endMonth: Date = .now
}

#Preview {
    GridCalendarView()
}