//
//  StressTestView.swift
//  GridTradingToolbox
//
//  Created by 陈敏杰 on 2024/1/14.
//

import SwiftUI

struct StressTestView: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>;
    @Binding var grids: [StressGrid];
    @State private var displayGridType = "0";
    private var mediumLargeSwitch = false;
    private var minPrice: Int
        
    var maxInvestment: String {
        get {
            var total = 0;
            for grid in grids {
                total = grid.buyPrice * grid.shares + total
            }
            return String(Utils.displayDecimal(decimal: Double(total) / 1000.0, num: 3))
        }
    }
    
    var maxLoss: String {
        var maxInv = 0
        var totalShare = 0
        for grid in grids {
            totalShare += grid.shares
            maxInv += grid.buyPrice * grid.shares
        }
        
        return String(Utils.displayDecimal(decimal: Double(maxInv - totalShare * minPrice) / 1000.0, num: 3))
    }
    
    var maxShares: String {
        var totalShare = 0
        for grid in grids {
            totalShare += grid.shares
        }
        return String(Utils.displayDecimal(decimal: Double(totalShare), num: 0))
    }
    
    var body: some View {
        NavigationStack {
            
            List {
                Section {
                    VStack {
                        HStack {
                            Text("最大投入")
                                .font(.system(size: 14))
                            Spacer()
                        }
                        HStack {
                            Text(maxInvestment)
                                .font(.custom("D-DIN", size: 26))
                                .foregroundColor(.red)
                                .bold()
                            Spacer()
                        }
                        .padding(.bottom, 16)
                        HStack {
                            VStack(alignment: .leading) {
                                Text("最大浮亏")
                                    .font(.system(size: 12))
                                    .foregroundColor(.gray)
                                Text(maxLoss)
                                    .font(.custom("D-DIN", size: 17))
                                    .foregroundColor(.black)
                                    .bold()
                            }
                            Spacer()
                            VStack(alignment: .leading) {
                                Text("最大买入股数")
                                    .font(.system(size: 12))
                                    .foregroundColor(.gray)
                                Text(maxShares)
                                    .font(.custom("D-DIN", size: 17))
                                    .foregroundColor(.black)
                                    .bold()
                            }
                            Spacer()
                        }
                    }
                    .padding(8)
                }
                
                VStack {
                    if mediumLargeSwitch {
                        Picker("", selection: $displayGridType) {
                            Text(String(format: "小网(%d)", grids.filter({ grid in
                                grid.gridType == TradeGrid.GridType.small.rawValue
                            }).count)).font(.custom("D-DIN", size: 17)).tag("0")
                            Text(String(format: "中网(%d)", grids.filter({ grid in
                                grid.gridType == TradeGrid.GridType.medium.rawValue
                            }).count)).font(.custom("D-DIN", size: 17)).tag("1")
                            Text(String(format: "大网(%d)", grids.filter({ grid in
                                grid.gridType == TradeGrid.GridType.large.rawValue
                            }).count)).font(.custom("D-DIN", size: 17)).tag("2")
                        }
                        .pickerStyle(.segmented)
                        .padding()
                    }
                    
                    if displayGridType == "0" {
                        StressGridView(grids: grids.filter({ grid in
                            grid.gridType == TradeGrid.GridType.small.rawValue
                        }));
                    } else if displayGridType == "1" {
                        StressGridView(grids: grids.filter({ grid in
                            grid.gridType == TradeGrid.GridType.medium.rawValue
                        }));
                    } else {
                        StressGridView(grids: grids.filter({ grid in
                            grid.gridType == TradeGrid.GridType.large.rawValue
                        }));
                    }
                }
            }
            .navigationTitle("压力测试")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("返回") {
                        self.presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    } 
    
    init(grids: Binding<[StressGrid]>, minPrice: Int, mediumLargeSwitch: Bool = false) {
        self._grids = grids
        self.mediumLargeSwitch = mediumLargeSwitch
        self.minPrice = minPrice
    }
}

struct StressGridView: View {
    var grids: [StressGrid];
    
    var body: some View {
        Grid {
            GridRow {
                Text("档位")
                Text("买入价")
                Text("卖出价")
                Text("股数")
                Text("盈利额")
                Text("盈利比")
            }.id(1)
                .font(.system(size: 13))
                .bold()
            Divider()
           
            ForEach(grids) { x in
                GridRow {
                    Text(Utils.trimTrailingZeros(from:  String(x.displayGrade)))
                    Text(String(x.displayBuyPrice))
                    Text(String(x.displaySellPrice))
                    Text(String(x.displayShares))
                    Text(Utils.trimTrailingZeros(from:  String(x.displayProfit)))
                    Text(String(x.displayProfitRatio))
                }.padding(.top, 8)
                    .font(.system(size: 12))
            }
            Divider().padding(.top)
        }
    }
}

//struct Previews: PreviewProvider {
//    @State static var grids = Strategy.stressTest(triggerPrice: 1000, amount: 2000000, buyStrategy: 2, incrementalBuyRatio: 5000, interval: 5000, maxFall: 60000, mediumLargeSwitch: true, mediumInterval: 15000, largeInterval: 30000)
//    static var previews: some View {
//        StressTestView(grids: $grids, mediumLargeSwitch: true)
//    }
//}
