//
//  StocksSearchView.swift
//  GridTradingToolbox
//
//  Created by 陈敏杰 on 2024/1/5.
//

import SwiftUI
import SwiftData

struct StocksSearchView: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>;
    @State private var searchText = "";
    @Binding private var code: String;
        
    var body: some View {
        NavigationStack {
            SearchResultListView(searchString: $searchText, code: $code)
                .navigationTitle("追踪股票")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button("返回") {
                            self.presentationMode.wrappedValue.dismiss()
                        }
                    }
                }
        }
        .searchable(text: $searchText, prompt: "股票代码")
    }
    
    init(code: Binding<String>) {
        self._code = code
    }
}

struct SearchResultListView: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.modelContext) private var modelContext
    @State private var stocks: [Stock] = []
    @StateObject var viewModle = SearchStockViewModel()
    @Binding var code: String
    @Binding var searchString: String
    
    var body: some View {
        List {
            ForEach(viewModle.stocks) { stock in
                HStack {
                    VStack(alignment: .leading) {
                        Text(stock.code)
                        Text(stock.name)
                            .font(.footnote)
                            .foregroundStyle(.gray)
                    }
                    Spacer()
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    code = stock.code
                    self.presentationMode.wrappedValue.dismiss()
                }
            }
        }
        .onChange(of: searchString) { old, newValue in
            viewModle.loadData(searchString: newValue)
        }
    }
    
    init(searchString: Binding<String>, code: Binding<String>) {
        self._searchString = searchString
        self._code = code
    }
}

//#Preview {
//    StocksSearchView()
//}
