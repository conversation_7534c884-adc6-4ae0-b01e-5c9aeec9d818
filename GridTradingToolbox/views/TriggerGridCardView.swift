//
//  TriggerGridCardView.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/4/8.
//

import SwiftUI

struct TriggerGridCardView: View {
    var stockGridPrice: StockGridPrice
    var isSell: Bool
    @Binding var hideSensitiveInfo: Bool
    
    var bg: Color {
        if isSell {
            return Color.red
        } else {
            return Color.green
        }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4){
            Text(stockGridPrice.strategyName)
                .font(.footnote)
                .fontWeight(.semibold)
            HStack {
                VStack {
                    Text("触发价")
                        .font(.caption2)
                        .foregroundStyle(.gray)
                    if hideSensitiveInfo {
                        Text("*")
                            .font(.caption2)
                    } else {
                        Text(Utils.displayPrice(price: stockGridPrice.triggerPrice))
                            .font(.caption2)
                    }
                }
                Spacer()
                VStack {
                    Text("当前价")
                        .font(.caption2)
                        .foregroundStyle(.gray)
                    if hideSensitiveInfo {
                        Text("*")
                            .font(.caption2)
                    } else {
                        Text(Utils.displayPrice(price: stockGridPrice.curPrice))
                            .font(.caption2)
                    }
                }
            }
            HStack {
                Text("距触发价")
                    .font(.caption2)
                    .foregroundStyle(.gray)
                if isSell && stockGridPrice.triggerPercent <= 0 {
                    Text("已触发")
                        .font(.caption2)
                        .foregroundStyle(bg)
                } else if !isSell && stockGridPrice.triggerPercent >= 0 {
                    Text("已触发")
                        .font(.caption2)
                        .foregroundStyle(bg)
                } else {
                    Text("\(Utils.displayPrice(price: stockGridPrice.triggerPercent, num: 2)) %")
                        .font(.caption2)
                        .foregroundStyle(bg)
                }
            }
        }
        .padding()
        .background(bg.opacity(0.1))
        .cornerRadius(20)
    }
}

//#Preview {
//    TriggerGridCardView()
//}
