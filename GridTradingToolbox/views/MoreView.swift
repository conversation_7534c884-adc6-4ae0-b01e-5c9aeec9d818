//
//  MoreView.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/4/2.
//

import SwiftUI
import SwiftData
import Alamofire
import AuthenticationServices

struct MoreView: View {
    @EnvironmentObject var settings: Settings
    @EnvironmentObject var sessionManager: SessionManager
    @Environment(\.modelContext) var modelContext;
    
    @State var showPro = false
    @State var showFeatureUpdate = false
    @State private var showWeChatAlert = false
    @State private var showErrorAlert = false
    @State private var errorMessage = ""
    
    // 添加文档选择器代理属性
    @State private var documentPickerDelegate: DocumentPickerDelegate?
    
    // 添加状态变量
    @State private var isLoading = false
    @State private var loadingText = ""
    
    // Apple登录相关状态
    @State private var isSigningIn = false
    @State private var signInErrorMessage = ""
    @State private var showSignInError = false
    
    // 登出确认弹窗
    @State private var showLogoutConfirmation = false
    
    var body: some View {
        ZStack {
            List {
                HStack {
                    Image(systemName: "crown")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 20, height: 20)
                        .padding(.trailing, 8)
                        .foregroundColor(settings.subscriptionMember ? .orange : .black)
                    Text("Pro会员")
                    Spacer()
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    showPro = true
                }
                .sheet(isPresented: $showPro) {
                    ProMemberView()
                }
                
                Section(header: Text("账户")) {
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Image(systemName: "person.circle.fill")
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 20, height: 20)
                                .foregroundColor(.blue)
                                .padding(.trailing, 8)
                            Text(sessionManager.getUserName())
                                .font(.headline)
                            Spacer()
                        }
                        
//                        Text("登录后可同步数据到云端，避免数据丢失")
//                            .font(.caption)
//                            .foregroundColor(.gray)
                        
                        if (!sessionManager.isLoggedIn) {
                            SignInWithAppleButton(
                                onRequest: { request in
                                    request.requestedScopes = [.fullName, .email]
                                },
                                onCompletion: { result in
                                    handleAppleSignIn(result: result)
                                }
                            )
                            .signInWithAppleButtonStyle(.black)
                            .frame(height: 44)
                            .cornerRadius(8)
                            .disabled(isSigningIn)
                            .opacity(isSigningIn ? 0.6 : 1.0)
                        } else {
                            // 登出按钮
                            Button(action: {
                                showLogoutConfirmation = true
                            }) {
                                HStack {
                                    Image(systemName: "rectangle.portrait.and.arrow.right")
                                        .foregroundColor(.red)
                                    Text("登出")
                                        .foregroundColor(.red)
                                }
                                .frame(maxWidth: .infinity)
                                .frame(height: 44)
                                .background(Color(.systemGray6))
                                .cornerRadius(8)
                            }
                        }
                        
                        if isSigningIn {
                            HStack {
                                ProgressView()
                                    .scaleEffect(0.8)
                                Text("正在登录...")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        }
                    }
                    .padding(.vertical, 8)
                }
                
                Section(header: Text("文档")) {
                    NavigationLink(destination: {
                        UserGuideView()
                    }, label: {
                        HStack {
                            Image(systemName: "book.pages")
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 20, height: 20)
                                .padding(.trailing, 8)
                            Text("使用指南")
                        }
                    })
                    HStack {
                        Image(systemName: "speaker.wave.2")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 20, height: 20)
                            .padding(.trailing, 8)
                        Text("最近更新")
                        Spacer()
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        showFeatureUpdate = true
                    }
                    .sheet(isPresented: $showFeatureUpdate) {
                        FeatureUpdateView()
                    }
                }
                
                Section(header: Text("设置")) {
    //                HStack {
    //                    Image(systemName: "cloud")
    //                        .resizable()
    //                        .aspectRatio(contentMode: .fit)
    //                        .frame(width: 20, height: 20)
    ////                        .background(.green)
    ////                        .foregroundColor(.white)
    ////                        .cornerRadius(5)
    //                        .padding(.trailing, 8)
    //                    Text("iCloud备份")
    //                    Spacer()
    ////                    Text("已开启")
    ////                                .font(.caption)
    ////                        .foregroundColor(.green)
    //                }
                    // NavigationLink(destination: {
                    //     ICloudSyncStatusView()
                    // }, label: {
                    //     HStack {
                    //         Image(systemName: "cloud")
                    //             .resizable()
                    //             .aspectRatio(contentMode: .fit)
                    //             .frame(width: 20, height: 20)
                    //             .padding(.trailing, 8)
                    //         Text("iCloud同步状态")
                    //         Spacer()
                    //         Text(getLastSyncTime())
                    //             .font(.caption)
                    //             .foregroundColor(.gray)
                    //     }
                    // })
                    NavigationLink(destination: {
                        StockAccountView()
                    }, label: {
                        HStack {
                            Image(systemName: "creditcard")
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 20, height: 20)
                                .padding(.trailing, 8)
                            Text("账户管理")
                        }
                    })
                    NavigationLink(destination: {
                        ImportDataView()
                    }, label: {
                        HStack {
                            Image(systemName: "arrow.down.doc")
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 20, height: 20)
                                .padding(.trailing, 8)
                            Text("小程序数据导入")
                        }
                    })
                   
                   HStack {
                       Image(systemName: "square.and.arrow.down")
                           .resizable()
                           .aspectRatio(contentMode: .fit)
                           .frame(width: 20, height: 20)
                           .padding(.trailing, 8)
                       Text("导入数据")
                       Spacer()
                   }
                   .contentShape(Rectangle())
                   .onTapGesture {
                       importData()
                   }
                   
                   HStack {
                       Image(systemName: "square.and.arrow.up")
                           .resizable()
                           .aspectRatio(contentMode: .fit)
                           .frame(width: 20, height: 20)
                           .padding(.trailing, 8)
                       Text("导出数据")
                       Spacer()
                   }
                   .contentShape(Rectangle())
                   .onTapGesture {
                       exportData()
                   }
                //    HStack {
                //        Image(systemName: "square.and.arrow.up")
                //            .resizable()
                //            .aspectRatio(contentMode: .fit)
                //            .frame(width: 20, height: 20)
                //            .padding(.trailing, 8)
                //        Text("导出数据test")
                //        Spacer()
                //    }
                //    .contentShape(Rectangle())
                //    .onTapGesture {
                //        exportGrids()
                //    }
                }
                
                Section(header: Text("联系我们")) {
                    HStack {
                        Image(systemName: "ellipsis.message")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 20, height: 20)
                            .padding(.trailing, 8)
                            // .fontWeight(.semibold)
                        Text("微信")
                            .foregroundColor(.black)
                        Spacer()
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        UIPasteboard.general.string = "jiev1994"
                        showWeChatAlert = true
                        let generator = UINotificationFeedbackGenerator()
                        generator.notificationOccurred(.success)
                    }
                    .alert("复制微信ID成功!", isPresented: $showWeChatAlert) {
                        Button("确定", role: .cancel) { }
                    } message: {
                        Text("打开微信的\"添加好友\"，粘贴即可添加")
                    }
                    HStack {
                        Image(systemName: "person.2")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 20, height: 20)
                            .padding(.trailing, 8)
                            .fontWeight(.semibold)
                        Link("交流群", destination: URL(string: "https://h123qzrdps.feishu.cn/docx/NqFtda3vWoXRUVxwX0Tc0kRpnJd?from=from_copylink")!)
                                    .foregroundColor(.black)
                    }
                    HStack {
                        Image(systemName: "square.stack.3d.down.right")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 20, height: 20)
                            .padding(.trailing, 8)
                            .fontWeight(.semibold)
                        Link("群知识库", destination: URL(string: "https://h123qzrdps.feishu.cn/docx/E3VGdajBoo97cPxHeeTcXykKnnf?from=from_copylink")!)
                                    .foregroundColor(.black)
                    }
                }
            }
            
            if isLoading {
                Color.black.opacity(0.4)
                    .edgesIgnoringSafeArea(.all)
                
                VStack {
                    ProgressView()
                        .scaleEffect(1.5)
                        .tint(.black)
                        .padding()
                    Text(loadingText)
                        .foregroundColor(.black)
                        .padding(.top)
                }
                .frame(width: 150, height: 150)
                .background(Color(UIColor.systemGray6))
                .cornerRadius(10)
            }
        }
        .navigationTitle("更多")
//         .onAppear() {
//             do {
//                 try modelContext.delete(model: Strategy.self)
//                 try modelContext.delete(model: TradeGrid.self)
//                 try modelContext.delete(model: TradeLog.self)
//                 try modelContext.delete(model: FlexibleTradeLog.self)
//                 try modelContext.save()
//                 print("成功清除所有数据")
//             } catch {
//                 print("清除数据失败: \(error)")
//             }
//         }
//         .onDisappear() {
//             print("MoreView disappeared")
//         }
        .alert(errorMessage, isPresented: $showErrorAlert) {
            Button("确定", role: .cancel) { }
        }
        .alert("登录失败", isPresented: $showSignInError) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(signInErrorMessage)
        }
        .alert("确认登出", isPresented: $showLogoutConfirmation) {
            Button("取消", role: .cancel) { }
            Button("确认登出", role: .destructive) {
                sessionManager.logout()
            }
        }
    }

    private func exportGrids() {
        isLoading = true
        loadingText = "正在导出..."
        
        // 添加短暂延迟使动画更流畅
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            let context = ModelContext(try! ModelContainer(for: TradeGrid.self))
            let grids = try? context.fetch(FetchDescriptor<TradeGrid>())
            
            guard let grids = grids else { return }
            let strategies = grids.map { $0.strategy }
            
            // 构建导出数据结构
            let exportData = grids.map { grid -> [String: Any] in
                var gridDict: [String: Any] = [
                    "id": grid.id.uuidString,
                    "gridType": grid.gridType,
                    "grade": grid.grade,
                    "holdShares": grid.holdShares,
                    "theoreticalBuyPrice": grid.theoreticalBuyPrice,
                    "theoreticalBuyShares": grid.theoreticalBuyShares,
                    "theoreticalSellPrice": grid.theoreticalSellPrice,
                    "theoreticalSellShares": grid.theoreticalSellShares,
                    "triggerAmount": grid.triggerAmount,
                    "status": grid.status ?? 0,
                    "createAt": ISO8601DateFormatter().string(from: grid.createAt),
                    "updateAt": ISO8601DateFormatter().string(from: grid.updateAt),
                    "strategy": grid.strategy?.name ?? "",
                    "strategy.grid.count": grid.strategy?.grids?.count ?? 0
                ]

                gridDict["tradeLogs"] = grid.tradeLogs?.map { log -> [String: Any] in
                    return [
                        "id": log.id.uuidString,
                        "tradeType": log.tradeType,
                        "tradeShares": log.tradeShares,
                        "tradePrice": log.tradePrice,
                        "tradeAt": ISO8601DateFormatter().string(from: log.tradeAt),
                        "createAt": ISO8601DateFormatter().string(from: log.createAt),
                        "mood": log.mood ?? "",
                        "note": log.note ?? ""
                    ]
                }
                
                return gridDict
            }
            
            // 转换为JSON数据
            guard let jsonData = try? JSONSerialization.data(withJSONObject: exportData, options: .prettyPrinted) else { return }
            
            // 获取文档目录路径
            let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
            let fileName = "grid_trading_grid_data_\(Date().timeIntervalSince1970).json"
            let fileURL = documentsPath.appendingPathComponent(fileName)
            
            // 写入文件
            do {
                try jsonData.write(to: fileURL)
                
                // 分享文件
                let activityVC = UIActivityViewController(activityItems: [fileURL], applicationActivities: nil)
                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                   let window = windowScene.windows.first,
                   let rootVC = window.rootViewController {
                    rootVC.present(activityVC, animated: true)
                }
            } catch {
                print("导出数据失败: \(error.localizedDescription)")
            }
            
            // 在完成时关闭加载动画
            isLoading = false
        }
    }

    // 修改导出数据方法
    private func exportData() {
        isLoading = true
        loadingText = "正在导出..."
        
        // 添加短暂延迟使动画更流畅
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            let context = ModelContext(try! ModelContainer(for: Strategy.self))
            let strategies = try? context.fetch(FetchDescriptor<Strategy>())
            
            guard let strategies = strategies else { return }
            
            // 构建导出数据结构
            let exportData = strategies.map { strategy -> [String: Any] in
                var strategyDict: [String: Any] = [
                    "id": strategy.id.uuidString,
                    "name": strategy.name,
                    "code": strategy.code ?? "",
                    "interval": strategy.interval,
                    "targetPrice": strategy.targetPrice,
                    "amount": strategy.amount,
                    "maxFall": strategy.maxFall,
                    "buyStrategy": strategy.buyStrategy,
                    "incrementalBuyRatio": strategy.incrementalBuyRatio ?? 0,
                    "mediumLargeSwitch": strategy.mediumLargeSwitch,
                    "mediumInterval": strategy.mediumInterval ?? 0,
                    "largeInterval": strategy.largeInterval ?? 0,
                    "triggerNotify": strategy.triggerNotify,
                    "calculateInterest": strategy.calculateInterest,
                    "interestPeriod": strategy.interestPeriod ?? 0,
                    "interest": strategy.interest ?? 0,
                    "retainProfitSwitch": strategy.retainProfitSwitch,
                    "retainProfit": strategy.retainProfit ?? 0,
                    "status": strategy.status ?? 0,
                    "profit": strategy.profit ?? 0,
                    "totalInvestmentAmount": strategy.totalInvestmentAmount ?? 0,
                    "totalProfitAmount": strategy.totalProfitAmount ?? 0,
                    "realTotalProfitAmount": strategy.realTotalProfitAmount ?? 0,
                    "retainShares": strategy.retainShares ?? 0,
                    "createAt": ISO8601DateFormatter().string(from: strategy.createAt),
                    "updateAt": ISO8601DateFormatter().string(from: strategy.updateAt)
                ]
                
                // 添加网格数据
                strategyDict["grids"] = strategy.grids?.map { grid -> [String: Any] in
                    var gridDict: [String: Any] = [
                        "id": grid.id.uuidString,
                        "gridType": grid.gridType,
                        "grade": grid.grade,
                        "holdShares": grid.holdShares,
                        "theoreticalBuyPrice": grid.theoreticalBuyPrice,
                        "theoreticalBuyShares": grid.theoreticalBuyShares,
                        "theoreticalSellPrice": grid.theoreticalSellPrice,
                        "theoreticalSellShares": grid.theoreticalSellShares,
                        "triggerAmount": grid.triggerAmount,
                        "status": grid.status ?? 0,
                        "notificationId": grid.notificationId ?? "",
                        "accountId": grid.accountId ?? "",
                        "createAt": ISO8601DateFormatter().string(from: grid.createAt),
                        "updateAt": ISO8601DateFormatter().string(from: grid.updateAt)
                    ]
                    
                    // 添加交易记录
                    gridDict["tradeLogs"] = grid.tradeLogs?.map { log -> [String: Any] in
                        return [
                            "id": log.id.uuidString,
                            "tradeType": log.tradeType,
                            "tradeShares": log.tradeShares,
                            "tradePrice": log.tradePrice,
                            "tradeAt": ISO8601DateFormatter().string(from: log.tradeAt),
                            "createAt": ISO8601DateFormatter().string(from: log.createAt),
                            "mood": log.mood ?? "",
                            "note": log.note ?? ""
                        ]
                    }
                    
                    return gridDict
                }
                
                // 添加灵活交易记录
                strategyDict["flexibleTradeLogs"] = strategy.flexibleTradeLog?.map { log -> [String: Any] in
                    return [
                        "id": log.id.uuidString,
                        "tradeType": log.tradeType,
                        "tradeShares": log.tradeShares,
                        "tradePrice": log.tradePrice,
                        "tradeAt": ISO8601DateFormatter().string(from: log.tradeAt),
                        "createAt": ISO8601DateFormatter().string(from: log.createAt)
                    ]
                }
                
                return strategyDict
            }
            
            // 转换为JSON数据
            guard let jsonData = try? JSONSerialization.data(withJSONObject: exportData, options: .prettyPrinted) else { return }
            
            // 获取文档目录路径
            let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
            let fileName = "grid_trading_data_\(Date().timeIntervalSince1970).json"
            let fileURL = documentsPath.appendingPathComponent(fileName)
            
            // 写入文件
            do {
                try jsonData.write(to: fileURL)
                
                // 分享文件
                let activityVC = UIActivityViewController(activityItems: [fileURL], applicationActivities: nil)
                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                   let window = windowScene.windows.first,
                   let rootVC = window.rootViewController {
                    rootVC.present(activityVC, animated: true)
                }
            } catch {
                print("导出数据失败: \(error.localizedDescription)")
            }
            
            // 在完成时关闭加载动画
            isLoading = false
        }
    }

    // 修改导入数据方法
    @MainActor
    private func importData() {
        documentPickerDelegate = DocumentPickerDelegate { url in
            isLoading = true
            loadingText = "正在导入..."
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                do {
                    guard url.startAccessingSecurityScopedResource() else {
                        throw NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "无法访问选择的文件"])
                    }
                    
                    defer {
                        url.stopAccessingSecurityScopedResource()
                    }
                    let jsonData = try Data(contentsOf: url)
                    print("jsonData: \(jsonData)")
                    let jsonArray = try JSONSerialization.jsonObject(with: jsonData) as! [[String: Any]]
                    
                    // let context = ModelContext(try! ModelContainer(for: Strategy.self))

                    print("jsonArray: \(jsonArray.count)")
                    
                    for strategyDict in jsonArray {
                        print("strategyDict: \(strategyDict)")
                        // 创建策略时初始化数组
                        let strategy = Strategy()
                        strategy.grids = []
                        strategy.flexibleTradeLog = []
                        
                        strategy.id = UUID()
                        strategy.name = strategyDict["name"] as! String
                        strategy.code = strategyDict["code"] as? String
                        strategy.interval = strategyDict["interval"] as! Int
                        strategy.targetPrice = strategyDict["targetPrice"] as! Int
                        strategy.amount = strategyDict["amount"] as! Int
                        strategy.maxFall = strategyDict["maxFall"] as! Int
                        strategy.buyStrategy = strategyDict["buyStrategy"] as! Int
                        strategy.incrementalBuyRatio = strategyDict["incrementalBuyRatio"] as? Int
                        strategy.mediumLargeSwitch = strategyDict["mediumLargeSwitch"] as! Bool
                        strategy.mediumInterval = strategyDict["mediumInterval"] as? Int
                        strategy.largeInterval = strategyDict["largeInterval"] as? Int
                        strategy.triggerNotify = strategyDict["triggerNotify"] as! Bool
                        strategy.calculateInterest = strategyDict["calculateInterest"] as! Bool
                        strategy.interestPeriod = strategyDict["interestPeriod"] as? Int
                        strategy.interest = strategyDict["interest"] as? Int
                        strategy.retainProfitSwitch = strategyDict["retainProfitSwitch"] as! Bool
                        strategy.retainProfit = strategyDict["retainProfit"] as? Int
                        strategy.status = strategyDict["status"] as? Int
                        strategy.profit = strategyDict["profit"] as? Int
                        strategy.totalInvestmentAmount = strategyDict["totalInvestmentAmount"] as? Int
                        strategy.totalProfitAmount = strategyDict["totalProfitAmount"] as? Int
                        strategy.realTotalProfitAmount = strategyDict["realTotalProfitAmount"] as? Int
                        strategy.setRetainShares(strategyDict["retainShares"] as? Int ?? 0)
                        strategy.grids = []
                        strategy.flexibleTradeLog = []
                        
                        let dateFormatter = ISO8601DateFormatter()
                        strategy.createAt = dateFormatter.date(from: strategyDict["createAt"] as! String)!
                        strategy.updateAt = dateFormatter.date(from: strategyDict["updateAt"] as! String)!

                        modelContext.insert(strategy)
                        print("insert strategy: \(strategy.id)")
                        
                        // 导入网格数据
                        if let gridsArray = strategyDict["grids"] as? [[String: Any]] {
                            for gridDict in gridsArray {
                                let grid = TradeGrid(
                                    id: UUID(),
                                    strategy: strategy,
                                    gridType: gridDict["gridType"] as! Int,
                                    grade: gridDict["grade"] as! Int,
                                    holdShares: gridDict["holdShares"] as! Int,
                                    theoreticalBuyPrice: gridDict["theoreticalBuyPrice"] as! Int,
                                    theoreticalBuyShares: gridDict["theoreticalBuyShares"] as! Int,
                                    theoreticalSellPrice: gridDict["theoreticalSellPrice"] as! Int,
                                    theoreticalSellShares: gridDict["theoreticalSellShares"] as! Int,
                                    triggerAmount: gridDict["triggerAmount"] as! Int,
                                    status: gridDict["status"] as? Int,
                                    notificationId: gridDict["notificationId"] as? String,
                                    accountId: gridDict["accountId"] as? String,
                                    createAt: dateFormatter.date(from: gridDict["createAt"] as! String)!,
                                    updateAt: dateFormatter.date(from: gridDict["updateAt"] as! String)!
                                )
                                grid.tradeLogs = [] // 初始化网格的交易记录数组
                                modelContext.insert(grid)
                                // 导入交易记录
                                if let tradeLogsArray = gridDict["tradeLogs"] as? [[String: Any]] {
                                    for logDict in tradeLogsArray {
                                        let tradeLog = TradeLog(
                                            id: UUID(),
                                            grid: grid,
                                            tradeType: logDict["tradeType"] as! Int,
                                            tradeShares: logDict["tradeShares"] as! Int,
                                            tradePrice: logDict["tradePrice"] as! Int,
                                            tradeAt: dateFormatter.date(from: logDict["tradeAt"] as! String)!,
                                            createAt: dateFormatter.date(from: logDict["createAt"] as! String)!,
                                            mood: logDict["mood"] as? String,
                                            note: logDict["note"] as? String
                                        )
                                        modelContext.insert(tradeLog)
                                        grid.tradeLogs?.append(tradeLog)
                                    }
                                }
                                
                                strategy.grids?.append(grid)
                            }
                        }
                        
                        // 导入灵活交易记录
                        if let flexibleTradeLogsArray = strategyDict["flexibleTradeLogs"] as? [[String: Any]] {
                            for logDict in flexibleTradeLogsArray {
                                let flexibleTradeLog = FlexibleTradeLog(
                                    strategy: strategy,
                                    tradeType: logDict["tradeType"] as! Int,
                                    tradeShares: logDict["tradeShares"] as! Int,
                                    tradePrice: logDict["tradePrice"] as! Int,
                                    tradeAt: dateFormatter.date(from: logDict["tradeAt"] as! String)!,
                                    createAt: dateFormatter.date(from: logDict["createAt"] as! String)!
                                )
                                modelContext.insert(flexibleTradeLog)
                                strategy.flexibleTradeLog?.append(flexibleTradeLog)
                            }
                        }

                        strategy.totalInvestmentAmount = strategy.calculateTotalInvestment()
                        strategy.totalProfitAmount = strategy.calculateTotalProfit()
                        strategy.realTotalProfitAmount = strategy.calculateRealTotalProfit()
                        strategy.setRetainShares(strategy.calculateRetainShares())

                        
                        // context.insert(strategy)
                    }
                    
                    try? modelContext.save()
                    
                    // 导入成功后可以添加成功提示
                    errorMessage = "数据导入成功！"
                    showErrorAlert = true
                    
                } catch {
                    print("导入数据失败: \(error.localizedDescription)")
                    errorMessage = "导入数据失败: \(error.localizedDescription)"
                    showErrorAlert = true
                }
                
                // 在完成或出错时关闭加载动画
                isLoading = false
            }
        }
        
        let documentPicker = UIDocumentPickerViewController(forOpeningContentTypes: [.json])
        documentPicker.delegate = documentPickerDelegate
        documentPicker.allowsMultipleSelection = false
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first,
           let rootVC = window.rootViewController {
            rootVC.present(documentPicker, animated: true)
        }
    }

    private func getLastSyncTime() -> String {
        let defaults = UserDefaults.standard
        if let lastSync = defaults.object(forKey: "LastiCloudSync") as? Date {
            let formatter = RelativeDateTimeFormatter()
            formatter.locale = Locale(identifier: "zh_CN")
            return formatter.localizedString(for: lastSync, relativeTo: Date())
        }
        return "未同步"
    }
    
    // MARK: - Apple登录处理
    private func handleAppleSignIn(result: Result<ASAuthorization, Error>) {
        isSigningIn = true
        
        switch result {
        case .success(let authResults):
            handleSuccessfulSignIn(authResults)
        case .failure(let error):
            handleSignInError(error)
        }
    }
    
    private func handleSuccessfulSignIn(_ authResults: ASAuthorization) {
        guard let appleIDCredential = authResults.credential as? ASAuthorizationAppleIDCredential else {
            isSigningIn = false
            errorMessage = "登录凭证无效"
            showErrorAlert = true
            return
        }
        
        var identityToken = ""
        // 验证identityToken以增强安全性
        if let tokenData = appleIDCredential.identityToken,
           let iToken = String(data: tokenData, encoding: .utf8) {
            identityToken = iToken
        }
        // 调用登录服务
        sessionManager.login(
            identityToken: identityToken,
            appleId: appleIDCredential.user,
            fullname: appleIDCredential.fullName?.givenName,
            email: appleIDCredential.email
        )
        
        // 重置登录状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            isSigningIn = false
        }
    }
    
    private func handleSignInError(_ error: Error) {
        isSigningIn = false
        
        if let authError = error as? ASAuthorizationError {
            switch authError.code {
            case .canceled:
                // 用户取消，不显示错误
                return
            case .failed:
                showSignInError("登录验证失败，请稍后重试")
            case .invalidResponse:
                showSignInError("登录响应无效，请检查网络连接")
            case .notHandled:
                showSignInError("登录未完成，请重新尝试")
            case .unknown:
                showSignInError("未知错误，请稍后重试")
            default:
                showSignInError("登录出现问题，请稍后重试")
            }
        } else {
            showSignInError("网络连接异常，请检查网络设置")
        }
    }
    
    private func showSignInError(_ message: String) {
        signInErrorMessage = message
        showSignInError = true
    }
}

// 文档选择器代理类
class DocumentPickerDelegate: NSObject, UIDocumentPickerDelegate {
    private let completion: (URL) -> Void
    
    init(completion: @escaping (URL) -> Void) {
        self.completion = completion
    }
    
    func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
        guard let sourceURL = urls.first else { return }
        
        // 直接使用源文件URL
        if sourceURL.startAccessingSecurityScopedResource() {
            completion(sourceURL)
            sourceURL.stopAccessingSecurityScopedResource()
        }
    }
}

//#Preview {
//    MoreView()
//}
