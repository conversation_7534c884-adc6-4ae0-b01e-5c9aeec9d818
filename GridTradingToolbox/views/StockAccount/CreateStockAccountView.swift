//
//  CreateStockAccountView.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/5/11.
//

import SwiftUI

struct CreateStockAccountView: View {
    enum Field {
        case name
        case rate
        case minFee
    }
    
    @Environment(\.modelContext) private var modelContext
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    
    @State private var name = ""
    @State private var rate = "0.01"
    @State private var minFee = "0.2"
    
    @State private var errorMsg = ""
    @State private var showErrorMsg = false
    
    @FocusState private var focusedField: Field?
    
    var body: some View {
        Form {
            Section(footer: showErrorMsg ? Text(errorMsg).foregroundColor(.red) : nil) {
                HStack {
                    Text("账户名称")
                    TextField("如: 华宝证券", text: $name)
                        .multilineTextAlignment(.trailing)
                        .focused($focusedField, equals: .name)
                        .submitLabel(.next)
                }
                HStack {
                    Text("佣金费率")
                    TextField("", text: $rate)
                        .multilineTextAlignment(.trailing)
                        .focused($focusedField, equals: .rate)
                        .onChange(of: rate, initial: false) { oldValue, newValue in
                            if newValue.count == 0 {
                                return;
                            }
                            let result = Utils.checkNumberFormat(number: newValue)
                            if !result {
                                rate = oldValue
                            }
                        }
                        .submitLabel(.next)
                    Text("%").font(.callout)
                }
                HStack {
                    Text("最低佣金")
                    TextField("", text: $minFee)
                        .multilineTextAlignment(.trailing)
                        .focused($focusedField, equals: .minFee)
                        .onChange(of: minFee, initial: false) { oldValue, newValue in
                            if newValue.count == 0 {
                                return;
                            }
                            let result = Utils.checkNumberFormat(number: newValue)
                            if !result {
                                minFee = oldValue
                            }
                        }
                        .submitLabel(.done)
                    Text("元").font(.callout)
                }
            }
            .onSubmit {
                switch focusedField {
                case .name:
                    focusedField = .rate
                case .rate:
                    focusedField = .minFee
                default:
                    print("done")
                }
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .navigationTitle("新增账户")
        .toolbar {
            Button("保存") {
                if checkParams() {
                    let transactionRate = Int((rate as NSString).doubleValue * 10000)
                    let fee = Int((minFee as NSString).doubleValue * 1000)
                    let account = StockAccount(name: name, transactionRate: transactionRate, minFee: fee)
                    modelContext.insert(account)
                    do {
                        try modelContext.save()
                        self.presentationMode.wrappedValue.dismiss()
                    } catch {
                        errorMsg = "保存失败: \(error.localizedDescription)"
                        showErrorMsg = true
                    }
                } else {
                    showErrorMsg = true
                }
            }
        }
    }
    
    func checkParams() -> Bool {
        if name.isEmpty {
            errorMsg = "账户名称不能为空"
            return false
        }
        
        if rate.isEmpty {
            errorMsg = "佣金费率不能为空"
            return false
        }
        
        if minFee.isEmpty {
            errorMsg = "最低佣金不能为空"
            return false
        }
        
        return true
    }
}

//#Preview {
//    CreateStockAccountView()
//}
