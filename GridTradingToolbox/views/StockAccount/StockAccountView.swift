//
//  StockAccountView.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/5/11.
//

import SwiftUI
import SwiftData

struct StockAccountView: View {
    @Environment(\.modelContext) var modelContext;
    @EnvironmentObject var settings: Settings

    @Query(sort: \StockAccount.createAt, order: .reverse) private var accounts: [StockAccount]
    
    var body: some View {
        VStack {
            if accounts.isEmpty {
                Spacer()
                Text("暂无证券账号")
                    .foregroundStyle(.gray)
                    .font(.footnote)
                    .bold()
                    .padding(.top)
                Spacer()
                Spacer()
            } else {
                List {
                    Section(header: Text("默认账户")) {
                        Picker("证券账户", selection: $settings.defaultAccountId) {
                            Text("无").tag("default")
                            ForEach(accounts, id: \.id) { account in
                                Text(account.name).tag(account.id)
                            }
                        }
                    }
                    Section(header: Text("所有账户")) {
                        ForEach(accounts, id: \.id) { account in
                            VStack(alignment: .leading) {
                                Text(account.name)
                                    .fontWeight(.semibold)
                                HStack {
                                    Text("佣金费率: \(Utils.displayPrice(price: Double(account.transactionRate) / 10000.0 ))%")
                                        .font(.footnote)
                                    Text("最低佣金: \(Utils.displayPrice(price: account.minFee))元")
                                        .font(.footnote)
                                }
                            }
                            .contextMenu {
                                Button(action: {
                                    if settings.defaultAccountId == account.id {
                                        settings.defaultAccountId = "default"
                                    }
                                    modelContext.delete(account)
                                }, label: {
                                    Text("删除")
                                })
                            }
                        }
                    }
                    
                }
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .navigationTitle("账户管理")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                NavigationLink(destination: CreateStockAccountView()) {
                    Image(systemName: "plus")
                }
            }
        }
    }
}

//#Preview {
//    StockAccountView()
//}
