//
//  IndexView.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/11/3.
//

import SwiftUI
import SwiftData
import Combine

struct IndexView: View {
    // @EnvironmentObject var settings: Settings
    @Environment(\.modelContext) private var modelContext
    @Query(sort: \FavoriteStock.createTime, order: .reverse, animation: .smooth) private var favoriteStocks: [FavoriteStock]
    @StateObject var viewModel = IndexViewModel()
    @State var showIndexSearch = false
    @State private var navigateToFilter = false
    @State private var showHelp = false

    var body: some View {
        ScrollView {
            ZStack {
                Color(UIColor.secondarySystemBackground)
                    .edgesIgnoringSafeArea(.all)

                VStack {
                    if viewModel.indexInfos.count == 0 {
                        Image("no-data")
                            .resizable()
                            .scaledToFit()
                            .frame(height: 200)
                            .padding([.horizontal, .top])
                            .padding(.bottom, -28)
                        Text("暂无自选指数")
                            .font(.callout)
                            .padding(.bottom)
                        But<PERSON>(action: {
                            showIndexSearch = true
                        }, label: {
                            Text("添加自选")
                                .font(.system(size: 15))
                                .bold()
                                .frame(width: 100, height: 32)
                                .foregroundColor(.white)
                                .background(.blue)
                                .cornerRadius(20)
                        })
                        Spacer()
                    }
                    ForEach(viewModel.indexInfos) { indexInfo in
                        IndexProgressView(favoriteStocks: favoriteStocks, indexInfo: indexInfo, viewModel: viewModel)
                            .padding(.bottom)
                            .transition(.asymmetric(
                                insertion: .scale.combined(with: .opacity),
                                removal: .scale.combined(with: .opacity)
                            ))
                    }
                    Spacer()
                }
                .padding(.horizontal, 16)
                .animation(.spring(response: 0.3, dampingFraction: 0.8), value: viewModel.indexInfos)
            }
        }
        .onAppear {
            let codes = favoriteStocks.map { $0.code }
            if codes.count > 0 {   
                viewModel.fetchIndexInfos(codes: codes)
            }
        }
        .background(Color(UIColor.secondarySystemBackground))
        .navigationTitle("指数点位")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                HStack(spacing: 16) {
                    
                    Button(action: {
                        showHelp = true
                    }) {
                        Image(systemName: "questionmark.circle")
                    }
                    
                    NavigationLink(destination: IndexFilterView(), isActive: $navigateToFilter) {
                        Button(action: {
                            navigateToFilter = true
                        }) {
                            Image(systemName: "slider.vertical.3")
                        }
                    }

                    Button(action: {
                        showIndexSearch = true
                    }) {
                        Image(systemName: "plus")
                    }
                }
            }
        }
        .sheet(isPresented: $showIndexSearch, onDismiss: {
            // DispatchQueue.main.asyncAfter(deadline: .now() + 0.01) {
            //     let codes = favoriteStocks.map { $0.code }
            //     if codes.count > 0 {   
            //         viewModel.fetchIndexInfos(codes: codes)
            //     }
            // }
        }) {
            IndexSearchView(viewModel: viewModel)
        }
        .sheet(isPresented: $showHelp) {
            IndexHelpView()
        }
    }
}

@MainActor
struct IndexProgressView: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.modelContext) private var modelContext
    var favoriteStocks: [FavoriteStock]
    var indexInfo: IndexInfo
    @ObservedObject var viewModel: IndexViewModel
    @State var selectedYearType = "0" // 5年或10年百分位
    @State var selectedMetricType = "mcw" // 等权或市值加权

    var body: some View {
        VStack(alignment: .leading) {
            HStack {
                VStack {
                    HStack(alignment: .firstTextBaseline) {
                        Text(indexInfo.name)
                            .font(.footnote)
                            .fontWeight(.semibold)
                        Text(indexInfo.code)
                            .font(.caption)
                            .foregroundColor(.gray)
                        Spacer()
                    }

                    HStack(alignment: .firstTextBaseline) {
                        Text("\(Utils.trimTrailingZeros(from: String(format: "%.2f", indexInfo.closePoint)))")
                            .font(.callout)
                            .fontWeight(.semibold)
                            .foregroundColor(indexInfo.changeRate > 0 ? .red : .green)
                        Text("\(indexInfo.changeRate > 0 ? "+" : "")\(Utils.trimTrailingZeros(from: String(format: "%.2f", indexInfo.changeRate * 100)) + "%")")
                            .font(.footnote)
                            .foregroundColor(indexInfo.changeRate > 0 ? .red : .green)
                        Spacer()
                    }
                }

                Spacer()

                VStack {
                    Image(systemName: isFavorite ? "star.fill" : "star")
                        .foregroundColor(.yellow)
                        .font(.callout)
                        .onTapGesture {
                            withAnimation {
                                handleFavorite()
                            }
                        }
                }
            }
            .padding([.horizontal, .top])
    
            Divider()
            
            HStack {
                Spacer()
                Picker("", selection: $selectedYearType.animation(.smooth)) {
                    Text("五年百分位").tag("0")
                    Text("十年百分位").tag("1")
                }
                .scaleEffect(0.9)
                .tint(.black)
                
                Spacer()
                Picker("", selection: $selectedMetricType.animation(.smooth)) {
                    Text("市值加权").tag("mcw")
                    Text("正数等权").tag("ewpvo")
                }
                .scaleEffect(0.9)
                .tint(.black)
                Spacer()
            }
            .padding([.horizontal, .top])

            GeometryReader { geometry in
                VStack {
                    HStack {
                        Text("PE")
                            .font(.footnote)
                            .foregroundColor(.black)
                        Spacer()
                        Text("\(Utils.trimTrailingZeros(from: String(format: "%.2f", indexInfo.getPeTtmPercentile(yearType: selectedYearType == "0" ? "5Y" : "10Y", metricType: selectedMetricType) * 100)))%")
                            .font(.footnote)
                            .foregroundColor(.black)
                    }
                    .padding(.top)
                    ZStack {
                        // Background track
                        Capsule()
                            .fill(Material.ultraThinMaterial)
                            .frame(width: geometry.size.width, height: 6)
                            .overlay(
                                Capsule()
                                    .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                            )
                        
                        // Progress bar with gradient
                        HStack {
                            Capsule()
                                .fill(
                                    LinearGradient(
                                        colors: getProgressGradient(percentile: indexInfo.getPeTtmPercentile(yearType: selectedYearType == "0" ? "5Y" : "10Y", metricType: selectedMetricType)),
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .frame(width: max(6, min(geometry.size.width * indexInfo.getPeTtmPercentile(yearType: selectedYearType == "0" ? "5Y" : "10Y", metricType: selectedMetricType), geometry.size.width)), height: 6)
                                .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                            Spacer()
                        }
                    }
                    .padding(.bottom)

                    HStack {
                        Text("PB")
                            .font(.footnote)
                            .foregroundColor(.black)
                        Spacer()
                        Text("\(Utils.trimTrailingZeros(from: String(format: "%.2f", indexInfo.getPbPercentile(yearType: selectedYearType == "0" ? "5Y" : "10Y", metricType: selectedMetricType) * 100)))%")
                            .font(.footnote)
                            .foregroundColor(.black)
                    }
                    ZStack {
                        // Background track
                        Capsule()
                            .fill(Material.ultraThinMaterial)
                            .frame(width: geometry.size.width, height: 6)
                            .overlay(
                                Capsule()
                                    .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                            )
                        
                        // Progress bar with gradient
                        HStack {
                            Capsule()
                                .fill(
                                    LinearGradient(
                                        colors: getProgressGradient(percentile: indexInfo.getPbPercentile(yearType: selectedYearType == "0" ? "5Y" : "10Y", metricType: selectedMetricType)),
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .frame(width: max(6, min(geometry.size.width * indexInfo.getPbPercentile(yearType: selectedYearType == "0" ? "5Y" : "10Y", metricType: selectedMetricType), geometry.size.width)), height: 6)
                                .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                            Spacer()
                        }
                    }
                }
            }
            .frame(height: 68)
            .padding([.horizontal, .bottom])
            .animation(.spring(response: 0.3, dampingFraction: 0.8), value: selectedYearType)
            .animation(.spring(response: 0.3, dampingFraction: 0.8), value: selectedMetricType)
            
            // HStack {
            //     Spacer()
            //     Text("发布日期：\(indexInfo.publishDate.formatted(date: .abbreviated, time: .omitted))")
            //         .font(.caption)
            //         .foregroundColor(.gray)
            // }
            // .padding([.horizontal, .top])

            HStack {
                Spacer()
                Text("数据日期：\(Date(timeIntervalSince1970: TimeInterval(indexInfo.updateTimestamp / 1000)).formatted(date: .abbreviated, time: .omitted))")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            .padding()

        }
        .padding(.horizontal, 4)
        .padding(.horizontal, 4)
        .background(Color.white)
        .cornerRadius(20)
    }

    @MainActor
    func getProgressGradient(percentile: Double) -> [Color] {
        if percentile > 0.8 {
            return [
                Color.red.opacity(0.8),
                Color.orange.opacity(0.6)
            ]
        } else if percentile < 0.2 {
            return [
                Color.green.opacity(0.8),
                Color.mint.opacity(0.6)
            ]
        }
        return [
            Color.blue.opacity(0.8),
            Color.cyan.opacity(0.6)
        ]
    }

    @MainActor
    func getColor(percentile: Double) -> Color {
        if percentile > 0.8 {
            return .red
        } else if percentile < 0.2 {
            return .green
        }
        return .blue
    }

    @MainActor
    private var isFavorite: Bool {
        favoriteStocks.contains { $0.code == indexInfo.code }
    }

    @MainActor
    private func handleFavorite() {
        DispatchQueue.main.async {
            if isFavorite {
                if let stock = favoriteStocks.first(where: { $0.code == indexInfo.code }) {
                    viewModel.removeIndexInfo(code: indexInfo.code)
                    modelContext.delete(stock)
                }
            } else {
                let stock = FavoriteStock(code: indexInfo.code, name: indexInfo.name, createTime: Date())
                modelContext.insert(stock)
                viewModel.addIndexInfo(indexInfo: indexInfo)
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                do {
                    try modelContext.save()
                } catch {
                    print("Error saving context: \(error)")
                    // TODO: Show error alert to user if needed
                }
            }
        }
    }
}

//#Preview {
//    IndexView()
//}
