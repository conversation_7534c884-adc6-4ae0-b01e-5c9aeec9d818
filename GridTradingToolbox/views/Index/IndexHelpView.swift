import SwiftUI

struct ExpandableHelpCard: View {
    let title: String
    let summary: String
    let content: String
    let iconName: String
    let gradientColors: [Color]
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Header
            HStack(spacing: 16) {
                // Icon
                ZStack {
                    Circle()
                        .fill(LinearGradient(
                            colors: gradientColors.map { $0.opacity(0.2) },
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ))
                        .frame(width: 40, height: 40)
                    
                    Image(systemName: iconName)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(gradientColors[0])
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.primary)
                    
                    if !isExpanded {
                        Text(summary)
                            .font(.system(size: 13))
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }
                }
                
                Spacer()
                
                Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.secondary)
            }
            .contentShape(Rectangle())
            .onTapGesture {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                    isExpanded.toggle()
                }
            }
            
            // Expanded Content
            if isExpanded {
                Text(content)
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
                    .padding(.top, 12)
                    .padding(.leading, 56)
                    // .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .padding(.vertical, 16)
        .padding(.horizontal, 20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Material.regularMaterial)
                .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 4)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(
                    LinearGradient(
                        colors: [
                            .white.opacity(0.6),
                            .white.opacity(0.3),
                            .clear
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1
                )
        )
    }
}

struct IndexHelpView: View {
    @Environment(\.dismiss) var dismiss
    
    var body: some View {
        NavigationView {
            ZStack {
                // 背景
                Color(UIColor.systemGray6)
                    .edgesIgnoringSafeArea(.all)
                
                ScrollView {
                    VStack(spacing: 16) {
                        ExpandableHelpCard(
                            title: "百分位说明",
                            summary: "了解五年和十年百分位的计算方法与意义",
                            content: """
                            百分位是一个统计学概念，表示当前指标在历史分布中的位置。
                            
                            五年百分位：
                            • 计算最近5年的所有PE/PB值
                            • 当前PE/PB值在这些历史数据中的位置
                            • 例如：80%表示当前值高于80%的历史数据
                            
                            十年百分位：
                            • 与五年百分位类似，但使用10年的历史数据
                            • 更长的时间跨度可以反映更完整的市场周期
                            • 适合判断长期投资价值
                            """,
                            iconName: "chart.bar.fill",
                            gradientColors: [.blue, .purple]
                        )
                        
                        ExpandableHelpCard(
                            title: "权重类型说明",
                            summary: "了解市值加权与正数等权的区别和应用场景",
                            content: """
                            不同的权重计算方式会导致指标值的差异。
                            
                            市值加权：
                            • 根据公司市值大小确定权重
                            • 市值越大的公司影响越大
                            • 更能反映大市值公司的估值水平
                            • 适合判断主流公司的整体估值
                            
                            正数等权：
                            • 剔除负值后平均计算
                            • 每个公司权重相同
                            • 更能反映整体市场的估值水平
                            • 适合判断一般公司的整体估值
                            """,
                            iconName: "scale.3d",
                            gradientColors: [.orange, .red]
                        )
                        
                        ExpandableHelpCard(
                            title: "颜色说明",
                            summary: "了解不同颜色所代表的估值区间含义",
                            content: """
                            进度条颜色反映当前估值的市场位置。
                            
                            红色区间 (>80%)：
                            • 表示估值处于历史高位
                            • 市场可能偏贵
                            • 建议谨慎对待
                            
                            蓝色区间 (20%-80%)：
                            • 表示估值处于中性区间
                            • 市场相对合理
                            • 可以正常操作
                            
                            绿色区间 (<20%)：
                            • 表示估值处于历史低位
                            • 市场可能偏便宜
                            • 可以适当积极
                            """,
                            iconName: "paintpalette.fill",
                            gradientColors: [.green, .mint]
                        )
                    }
                    .padding()
                    
                    Text("估值信息仅供参考，不构成投资建议")
                        .font(.system(size: 13))
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity)
                        .padding(.bottom, 16)
                }
            }
            .navigationTitle("指标说明")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") { dismiss() }
                }
            }
        }
    }
}

#Preview {
    IndexHelpView()
}
