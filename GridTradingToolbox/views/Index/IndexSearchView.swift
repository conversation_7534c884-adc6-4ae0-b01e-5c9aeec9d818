//
//  IndexSearchView.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/11/6.
//

import SwiftUI
import SwiftData
import Combine

@MainActor
struct IndexSearchView: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>;
    @ObservedObject var viewModel: IndexViewModel
    @State private var searchText = "";
        
    var body: some View {
        NavigationStack {
            ZStack {
                Color(UIColor.secondarySystemBackground)
                    .edgesIgnoringSafeArea(.all)
                SearchResultView(searchString: $searchText, indexViewModel: viewModel)
                    .navigationTitle("添加指数")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button("返回") {
                            self.presentationMode.wrappedValue.dismiss()
                        }
                    }
                }
            }
        }
        .background(Color(UIColor.secondarySystemBackground))
        .searchable(text: $searchText, prompt: "指数代码")
    }
}

@MainActor
struct SearchResultView: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.modelContext) private var modelContext
    @EnvironmentObject var settings: Settings
    @Query private var favoriteStocks: [FavoriteStock]
    @State private var indexInfos: [IndexInfo] = []
    @StateObject var viewModle = IndexSearchViewModel()
    @ObservedObject var indexViewModel: IndexViewModel
    @Binding var searchString: String

    init(searchString: Binding<String>, indexViewModel: IndexViewModel) {
        self._searchString = searchString
        self.indexViewModel = indexViewModel
    }
    
    var body: some View {
        ScrollView {
            LazyVStack {
                ForEach(viewModle.indexInfos) { indexInfo in
                    IndexProgressView(favoriteStocks: favoriteStocks, indexInfo: indexInfo, viewModel: indexViewModel)
                        .padding([.horizontal, .bottom])
                        .cornerRadius(20)
                        .transition(.asymmetric(
                            insertion: .scale.combined(with: .opacity).combined(with: .slide),
                            removal: .scale.combined(with: .opacity)
                        ))
                }
            }
            .animation(.spring(response: 0.3, dampingFraction: 0.8), value: viewModle.indexInfos)
        }
        .onChange(of: searchString) { old, newValue in
            withAnimation {
                viewModle.loadData(searchString: newValue)
            }
        }
    }
}

// #Preview {
//     IndexSearchView()
// }
