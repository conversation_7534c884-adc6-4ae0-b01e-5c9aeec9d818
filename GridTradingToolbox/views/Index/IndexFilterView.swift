import SwiftUI
import SwiftData

struct IndexFilterView: View {
    @Environment(\.modelContext) private var modelContext
    @Query private var favoriteStocks: [FavoriteStock]
    @StateObject private var viewModel = IndexFilterViewModel()
    @State private var hasAppliedFilter = false
    
    @State private var selectedYearType = "5Y"
    @State private var selectedMetricType = "mcw"
    @State private var pePercentile = "30"
    @State private var pbPercentile = "30"
    @State private var isFilterExpanded = true
    
    private let percentileOptions = stride(from: 0.0, through: 100.0, by: 5.0).map { $0 }
    
    // 添加新状态变量
    @State private var showFilterSheet = false
    @State private var showEmptyView = false
    
    @State private var showError = false
    @State private var errorMessage = ""

    var body: some View {
        ZStack {
            Color(UIColor.systemGroupedBackground)
                .ignoresSafeArea()
            
            VStack(spacing: 0) {
                    // 优化筛选条件区域 - 改为卡片式设计
                    VStack(spacing: 20) {
                        // 标题区
                        HStack {
                            Text("筛选条件")
                                .font(.headline)
                                .foregroundColor(.primary)
                            Spacer()
                            Button(action: {
                                withAnimation {
                                    isFilterExpanded.toggle()
                                }
                            }) {
                                Image(systemName: isFilterExpanded ? "chevron.up" : "chevron.down")
                                    .foregroundColor(.gray)
                            }
                        }
                        
                        if isFilterExpanded {
                            // 统计区间和加权方式
                            HStack(spacing: 16) {
                                // 统计区间
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("统计区间")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                    Picker("", selection: $selectedYearType) {
                                        Text("5年").tag("5Y")
                                        Text("10年").tag("10Y")
                                    }
                                    .pickerStyle(SegmentedPickerStyle())
                                }
                                .frame(maxWidth: .infinity)
                                
                                // 加权方式
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("加权方式")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                    Picker("", selection: $selectedMetricType) {
                                        Text("市值加权").tag("mcw")
                                        Text("正数等权").tag("ewpvo")
                                    }
                                    .pickerStyle(SegmentedPickerStyle())
                                }
                                .frame(maxWidth: .infinity)
                            }
                            
                            // PE和PB百分位
                            HStack(spacing: 16) {
                                // PE筛选区
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("PE百分位")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                    Menu {
                                        Picker("PE百分位", selection: Binding(
                                            get: { Double(pePercentile) ?? 0 },
                                            set: { pePercentile = String(Int($0)) }
                                        )) {
                                            ForEach(percentileOptions, id: \.self) { value in
                                                Text(String(format: "%.0f%%", value)).tag(value)
                                            }
                                        }
                                        .pickerStyle(.inline)
                                    } label: {
                                        HStack {
                                            Text("\(pePercentile)%")
                                                .foregroundColor(.primary)
                                            Spacer()
                                            Image(systemName: "chevron.down")
                                                .foregroundColor(.gray)
                                                .font(.caption)
                                        }
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 8)
                                        .background(Color(UIColor.tertiarySystemBackground))
                                        .cornerRadius(8)
                                    }
                                }
                                .frame(maxWidth: .infinity)
                                
                                // PB筛选区
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("PB百分位")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                    Menu {
                                        Picker("PB百分位", selection: Binding(
                                            get: { Double(pbPercentile) ?? 0 },
                                            set: { pbPercentile = String(Int($0)) }
                                        )) {
                                            ForEach(percentileOptions, id: \.self) { value in
                                                Text(String(format: "%.0f%%", value)).tag(value)
                                            }
                                        }
                                        // .pickerStyle(.wheel)
                                    } label: {
                                        HStack {
                                            Text("\(pbPercentile)%")
                                                .foregroundColor(.primary)
                                            Spacer()
                                            Image(systemName: "chevron.down")
                                                .foregroundColor(.gray)
                                                .font(.caption)
                                        }
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 8)
                                        .background(Color(UIColor.tertiarySystemBackground))
                                        .cornerRadius(8)
                                    }
                                }
                                .frame(maxWidth: .infinity)
                            }
                            
                            // 应用按钮
                            Button(action: {
                                Task {
                                    await filter()
                                    withAnimation {
                                        isFilterExpanded = false
                                    }
                                }
                            }) {
                                Text("筛选")
                                    .font(.subheadline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity)
                                    .padding(.vertical, 12)
                                    .background(Color.blue)
                                    .cornerRadius(8)
                            }
                            // .padding(.top, 16)
                        } else {
                            // 显示当前筛选条件摘要
                            HStack {
                                Text("当前筛选: \(selectedYearType), \(selectedMetricType == "mcw" ? "市值加权" : "正数等权"), PE \(pePercentile)%, PB \(pbPercentile)%")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                Spacer()
                            }
                        }
                        
                    }
                    .padding(16)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color(UIColor.secondarySystemBackground))
                    )
                    .padding(.horizontal, 16)
                    .padding(.top, 16)
                    .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
                    
                    // 结果列表
                    if viewModel.isLoading {
                        Spacer()
                        VStack(spacing: 16) {
                            ProgressView()
                                .scaleEffect(1.2)
                            Text("正在加载数据...")
                                .foregroundColor(.secondary)
                        }
                        Spacer()
                    } else if hasAppliedFilter && viewModel.filteredIndexes.isEmpty {
                        Spacer()
                        VStack(spacing: 16) {
                            Image(systemName: "magnifyingglass")
                                .font(.system(size: 40))
                                .foregroundColor(.gray)
                            Text("没有找到符合条件的指数")
                                .font(.headline)
                            Text("请尝试调整筛选条件")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        Spacer()
                    } else if !hasAppliedFilter {
                        Spacer()
                        VStack(spacing: 16) {
                            Image(systemName: "slider.vertical.3")
                                .font(.system(size: 40))
                                .foregroundColor(.gray)
                            Text("调整上方筛选条件")
                                .font(.headline)
                            Text("点击\"应用\"按钮开始筛选")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        Spacer()
                    } else {
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text("筛选结果: \(viewModel.filteredIndexes.count)个指数")
                                    .font(.subheadline)
                                    .foregroundColor(.black)
                                    .fontWeight(.semibold)
                                Spacer()
                            }
                            .padding([.horizontal, .top], 16)
                            
                            List {
                                ForEach(viewModel.filteredIndexes) { index in
                                    HStack(spacing: 12) {
                                        // 指数信息
                                        VStack(alignment: .leading, spacing: 4) {
                                            Text(index.name)
                                                .font(.system(size: 16))
                                                .lineLimit(1)
                                                .truncationMode(.tail)
                                            Text(index.code)
                                                .font(.system(size: 13))
                                                .foregroundColor(.gray)
                                        }
                                        .frame(width: 100, alignment: .leading)
                                        
                                        Spacer()
                                        
                                        // PE-TTM 数据
                                        VStack(alignment: .center, spacing: 4) {
                                            Text("PE")
                                                .font(.system(size: 13))
                                                .foregroundColor(.secondary)
                                            
                                            let peValue = selectedYearType == "5Y" ?
                                                index.peTtmPercentile5Y :
                                                index.peTtmPercentile10Y
                                            
                                            Text(String(format: "%.1f%%", peValue * 100))
                                                .font(.system(size: 15, weight: .medium))
                                                .foregroundColor(getProgressColor(percentile: peValue))
                                        }
                                        .frame(width: 80)
                                        
                                        // PB 数据
                                        VStack(alignment: .center, spacing: 4) {
                                            Text("PB")
                                                .font(.system(size: 13))
                                                .foregroundColor(.secondary)
                                            
                                            let pbValue = selectedYearType == "5Y" ?
                                                index.pbPercentile5Y :
                                                index.pbPercentile10Y
                                            
                                            Text(String(format: "%.1f%%", pbValue * 100))
                                                .font(.system(size: 15, weight: .medium))
                                                .foregroundColor(getProgressColor(percentile: pbValue))
                                        }
                                        .frame(width: 80)
                                        
                                        // 收藏按钮
                                        let isFavorite = favoriteStocks.contains { $0.code == index.code }
                                        Button(action: {
                                            withAnimation {
                                                handleFavorite(index: index)
                                            }
                                        }) {
                                            Image(systemName: isFavorite ? "star.fill" : "star")
                                                .font(.system(size: 18))
                                                .foregroundColor(isFavorite ? .yellow : .gray.opacity(0.7))
                                                .scaleEffect(isFavorite ? 1.1 : 1.0)
                                                .animation(.spring(response: 0.3, dampingFraction: 0.6, blendDuration: 0), value: isFavorite)
                                        }
                                        .alert("错误", isPresented: $showError) {
                                            Button("确定", role: .cancel) { }
                                        } message: {
                                            Text(errorMessage)
                                        }
                                        .buttonStyle(BorderlessButtonStyle())
                                        .frame(width: 40)
                                    }
                                    .padding(8)
                                    .background(Color(UIColor.systemBackground))
                                    .cornerRadius(8)
                                }
                                .padding(.horizontal, 16)
                                .listRowBackground(Color.clear)
                                .listRowSeparator(.hidden)
                                .listRowInsets(EdgeInsets(top: 4, leading: 16, bottom: 4, trailing: 16))
                            }
                            .listStyle(.plain)
                            .background(Color.clear)
                        }
                    }
                }
            }
            .navigationTitle("指数筛选")
            .navigationBarTitleDisplayMode(.inline)
        }
    
    // 保持原有功能不变
    private func filter() async {
        hasAppliedFilter = true
        let pe = (Double(pePercentile) ?? 0) / 100
        let pb = (Double(pbPercentile) ?? 0) / 100
        
        if selectedYearType == "5Y" {
            await viewModel.fetchFilteredIndexes(
                pePercentile5Y: pe,  // 直接传递pe值，包括0
                pbPercentile5Y: pb,  // 直接传递pb值，包括0
                metricType: selectedMetricType
            )
        } else {
            await viewModel.fetchFilteredIndexes(
                pePercentile10Y: pe,  // 直接传递pe值，包括0
                pbPercentile10Y: pb,  // 直接传递pb值，包括0
                metricType: selectedMetricType
            )
        }
    }
    
    // 优化进度条颜色逻辑
    private func getProgressColor(percentile: Double) -> Color {
        if percentile > 0.8 {
            return .red
        } else if percentile < 0.2 {
            return .green
        }
        return .blue
    }
    
    private func handleFavorite(index: FilteredIndex) {
        guard !index.code.isEmpty, !index.name.isEmpty else {
            errorMessage = "指数数据不完整，无法添加收藏"
            showError = true
            return
        }
        
        do {
            // 检查是否已存在
            let exists = favoriteStocks.contains(where: { $0.code == index.code })
            
            if exists {
                // 删除收藏
                if let stock = favoriteStocks.first(where: { $0.code == index.code }) {
                    modelContext.delete(stock)
                    try modelContext.save()
                } else {
                    errorMessage = "找不到要删除的收藏记录"
                    showError = true
                }
            } else {
                // 添加新收藏
                let stock = FavoriteStock(code: index.code, name: index.name, createTime: Date())
                modelContext.insert(stock)
                try modelContext.save()
            }
        } catch {
            errorMessage = "收藏操作失败：\(error.localizedDescription)"
            showError = true
        }
    }
}

#Preview {
    IndexFilterView()
}
