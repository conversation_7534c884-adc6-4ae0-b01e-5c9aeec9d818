//
//  TradeHistoryView.swift
//  GridTradingToolbox
//
//  Created by 陈敏杰 on 2024/1/23.
//

import SwiftUI

struct FlexibleTradeLogView: View {
    @Bindable var tradeLog: FlexibleTradeLog
    @State var showEditGrid = false

    var body: some View {
        VStack {
            HStack {
                Text("卖出")
                    .font(.callout)
                    .fontWeight(.semibold)
                    .foregroundColor(.red)

                Spacer()
                
                Image(systemName: "chevron.right")
                    .resizable()
                    .scaledToFit()
                    .frame(width: 10, height: 10)
                    .foregroundColor(.gray)
            }

            HStack {
                Text(Utils.displayDate(date: tradeLog.tradeAt))
                    .font(.caption)
                    .foregroundColor(.gray)

                Spacer()
            }

            HStack {
                VStack {
                    HStack {
                        Text("卖出价格  ")
                            .font(.caption)
                            .foregroundColor(.gray)
                        Text(tradeLog.displayTradePrice)
                            .font(.caption)
                            .foregroundColor(.black)
                    }
                }

                Spacer()

                VStack {
                    HStack {
                        Text("卖出股数  ")
                            .font(.caption)
                            .foregroundColor(.gray)
                        Text(tradeLog.tradeShares.formattedWithSeparator)
                            .font(.caption)
                            .foregroundColor(.black)
                    }
                }

                Spacer()
            }
        }
        .padding()
        .background(Color.white)
        .cornerRadius(20)
        .padding(.bottom)
        .contentShape(Rectangle())
        .onTapGesture {
            withAnimation {
                showEditGrid.toggle()
            }
        }
        .sheet(isPresented: $showEditGrid) {
            EditSellRetainShareView(tradeLog: tradeLog)
        }
    }
}

struct BuyTradeLogView: View {
    @Bindable var tradeLog: TradeLog
    @State var showEditGrid = false
    var body: some View {
        VStack {
            HStack {
                Text("买入")
                    .font(.callout)
                    .fontWeight(.semibold)
                    .foregroundColor(.green)

                Spacer()
                
                Image(systemName: "chevron.right")
                    .resizable()
                    .scaledToFit()
                    .frame(width: 10, height: 10)
                    .foregroundColor(.gray)
            }

            HStack {
                Text(Utils.displayDate(date: tradeLog.tradeAt))
                    .font(.caption)
                    .foregroundColor(.gray)
                    // .fontWeight(.thin)

                Spacer()
            }

            HStack {
                VStack {
                    HStack {
                        Text("买入价格  ")
                            .font(.caption)
                            .foregroundColor(.gray)
                        Text(tradeLog.displayTradePrice)
                            .font(.caption)
                            .foregroundColor(.black)
                    }
                }

                Spacer()

                VStack {
                    HStack {
                        Text("买入股数  ")
                            .font(.caption)
                            .foregroundColor(.gray)
                        Text(tradeLog.tradeShares.formattedWithSeparator)
                            .font(.caption)
                            .foregroundColor(.black)
                    }
                }

                Spacer()
            }
        }
        .contentShape(Rectangle())
        .onTapGesture {
            withAnimation {
                showEditGrid.toggle()
            }
        }
        .sheet(isPresented: $showEditGrid) {
            BuyGridView(strategy: tradeLog.grid!.strategy!, grid: tradeLog.grid!, isEditMode: true)
        }
    }
}

struct SellTradeLogView: View {
    @Bindable var tradeLog: TradeLog
    @State var showEditGrid = false

    var body: some View {
        VStack {
            HStack {
                Text("卖出")
                    .font(.callout)
                    .fontWeight(.semibold)
                    .foregroundColor(.red)

                Spacer()
                
                Image(systemName: "chevron.right")
                    .resizable()
                    .scaledToFit()
                    .frame(width: 10, height: 10)
                    .foregroundColor(.gray)
            }

            HStack {
                Text(Utils.displayDate(date: tradeLog.tradeAt))
                    .font(.caption)
                    .foregroundColor(.gray)
                    // .fontWeight(.thin)

                Spacer()
            }

            HStack {
                VStack {
                    HStack {
                        Text("卖出价格  ")
                            .font(.caption)
                            .foregroundColor(.gray)
                        Text(tradeLog.displayTradePrice)
                            .font(.caption)
                            .foregroundColor(.black)
                    }
                }

                Spacer()

                VStack {
                    HStack {
                        Text("卖出股数  ")
                            .font(.caption)
                            .foregroundColor(.gray)
                        Text(tradeLog.tradeShares.formattedWithSeparator)
                            .font(.caption)
                            .foregroundColor(.black)
                    }
                }

                Spacer()
            }
        }
        .contentShape(Rectangle())
        .onTapGesture {
            withAnimation {
                showEditGrid.toggle()
            }
        }
        .sheet(isPresented: $showEditGrid) {
            SellGridView(strategy: tradeLog.grid!.strategy!, grid: tradeLog.grid!, confettiFlag: .constant(0), isEditMode: true)
        }
    }
}

struct TradeHistoryCardView: View {
    @Bindable var grid: TradeGrid
    @State var showMore = false

    var body: some View {
        VStack {
            VStack {
                HStack {
                    VStack(alignment: .leading) {
                        Text("\(Utils.displayDate(date: grid.buyTradeLog!.tradeAt)) 至 \(Utils.displayDate(date: grid.sellTradeLog!.tradeAt))")
                            .font(.callout)
                            // .fontWeight(.semibold)
                        Text("持有 \(grid.displayHoldDays) 天")
                            .foregroundColor(.gray)
                            .font(.footnote)
                            // .fontWeight(.semibold)
                    }

                    Spacer()

                    VStack(alignment: .trailing) {
                        Text(grid.displayGridType)
                            .font(.callout)
                            // .fontWeight(.semibold)
                        Text(grid.displayGrade)
                            .font(.footnote)
                            .foregroundColor(.gray)
                    }
                    // .padding(.trailing)

                    Image(systemName: "triangle.fill")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 8, height: 8)
                        .foregroundColor(.black )
                        .rotationEffect(Angle(degrees: showMore ? 180 : 0))
                }
                .padding(.bottom)

                HStack {
                    VStack(alignment: .leading) {
                        if grid.holdShares > 0 {
                            HStack {
                                Text("预估盈利: ")
                                    .font(.callout)
                                Text("\(grid.displayProfit)")
                                    .font(.callout)
                                    .fontWeight(.semibold)
                                    .foregroundColor(grid.isProfit() ? .red : .green)
                                Text(grid.getProfitRatio())
                                    .font(.callout)
                                    .fontWeight(.semibold)
                                    .foregroundColor(grid.isProfit() ? .red : .green)
                            }
                            HStack {
                                Text("预留利润: \(grid.holdShares) 股")
                                    .font(.footnote)
                                    .foregroundColor(.gray)
                            }
                        }  else {
                            HStack {
                                Text("盈利: ")
                                    .font(.callout)
                                Text("\(grid.displayProfit)")
                                    .font(.callout)
                                    .fontWeight(.semibold)
                                    .foregroundColor(grid.isProfit() ? .red : .green)
                                Text(grid.getProfitRatio())
                                    .font(.callout)
                                    .fontWeight(.semibold)
                                    .foregroundColor(grid.isProfit() ? .red : .green)
                            }
                        }
                    }

                    Spacer()
                }
            }
            .contentShape(Rectangle())
            .onTapGesture {
                withAnimation {
                    showMore.toggle()
                }
            }
            

            if showMore {
                Divider()
                    .padding(.vertical)

                SellTradeLogView(tradeLog: grid.sellTradeLog!)
                    .padding(.bottom, 4)
                BuyTradeLogView(tradeLog: grid.buyTradeLog!)
            }
        }
        .padding()
        .background(Color.white)
        .cornerRadius(20)
        .padding(.bottom)
    }   
}

struct TradeHistoryView: View {
    @Environment(\.modelContext) private var modelContext

    @Bindable var strategy: Strategy

    @State var showMore = false
    @State var selectedTag = "网格"

    @Namespace private var animation

    let tags = ["网格", "预留利润"]
    
    var body: some View {
        ScrollView {
            ZStack {
                Color(UIColor.secondarySystemBackground)
                    .edgesIgnoringSafeArea(.all)
                
                VStack {
                    tagsView()
                    if selectedTag == "网格" {
                        if strategy.soldGrids.count == 0 {
                            Image("no-data")
                                .resizable()
                                .scaledToFit()
                                .frame(height: 300)
                                .padding([.horizontal, .top])
                            Text("暂无数据")
                                .font(.callout)
                                .foregroundStyle(.gray)
                                .padding()
                        } else {
                            ForEach(strategy.soldGrids.sorted{ $0.sellTradeLog!.tradeAt > $1.sellTradeLog!.tradeAt }) { grid in
                                TradeHistoryCardView(grid: grid)
                                .padding(.horizontal, 16)
                                .contextMenu {
                                    Button(action: {
                                        deleteGrid(grid: grid)
                                    }, label: {
                                        Text("删除")    
                                    })
                                }
                            }
                        }
                    } else {
                        if strategy.flexibleTradeLog!.count == 0 {
                            Image("no-data")
                                .resizable()
                                .scaledToFit()
                                .frame(height: 300)
                                .padding([.horizontal, .top])
                            Text("暂无数据")
                                .font(.callout)
                                .foregroundStyle(.gray)
                                .padding()
                        } else {
                            ForEach(strategy.flexibleTradeLog!.sorted{ $0.tradeAt > $1.tradeAt }) { tradeLog in
                                FlexibleTradeLogView(tradeLog: tradeLog)
                                    .padding(.horizontal, 16)
                                    .contextMenu {
                                        Button(action: {
                                            deleteFlexibleTradeLog(tradeLog: tradeLog)
                                        }, label: {
                                            Text("删除")    
                                        })
                                    }
                            }
                        }
                    }
                }
            }
        }
        // .padding()
        .background(Color(UIColor.secondarySystemBackground))
            .navigationTitle("交易历史")
            .navigationBarTitleDisplayMode(.inline)
    }

    @MainActor
    private func deleteGrid(grid: TradeGrid) {
        if grid.isSold {
            // todo 检测预留利润的股数是否已经被卖出
            let index = strategy.grids!.firstIndex(of: grid)
            if index != nil {
                strategy.grids!.remove(at: index!)
            }
            strategy.totalProfitAmount = strategy.calculateTotalProfit()
            strategy.realTotalProfitAmount = strategy.calculateRealTotalProfit()
            strategy.setRetainShares(strategy.calculateRetainShares())
        } else {
            if grid.gridType == TradeGrid.GridType.small.rawValue && strategy.sortedSmallGrids[0] == grid {
            let index = strategy.grids!.firstIndex(of: grid)
            if index != nil {
                strategy.grids!.remove(at: index!)
                }
            } else if grid.gridType == TradeGrid.GridType.medium.rawValue && strategy.sortedMediumGrids[0] == grid {
                let index = strategy.grids!.firstIndex(of: grid)
                if index != nil {
                    strategy.grids!.remove(at: index!)
                }
            } else if grid.gridType == TradeGrid.GridType.large.rawValue && strategy.sortedLargeGrids[0] == grid {
                let index = strategy.grids!.firstIndex(of: grid)
                if index != nil {
                    strategy.grids!.remove(at: index!)
                }
            }
            strategy.totalInvestmentAmount = strategy.calculateTotalInvestment()
        }
        modelContext.delete(grid)
        try? modelContext.save()
    }

    @MainActor
    private func deleteFlexibleTradeLog(tradeLog: FlexibleTradeLog) {
        let index = strategy.flexibleTradeLog!.firstIndex(of: tradeLog)
        if index != nil {
            strategy.flexibleTradeLog!.remove(at: index!)
        }
        strategy.realTotalProfitAmount = strategy.calculateRealTotalProfit()
        strategy.setRetainShares(strategy.calculateRetainShares())
        modelContext.delete(tradeLog)
        try? modelContext.save()
        if strategy.flexibleTradeLog!.count == 0 {
            selectedTag = "网格"
        }
    }

    @ViewBuilder
    func tagsView() -> some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                ForEach(tags, id: \.self) { tag in
                    Text(tag)
                        .font(.caption)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 4)
                        .background {
                            if selectedTag == tag { 
                                Capsule()
                                    .fill(Color.blue)
                                    .matchedGeometryEffect(id: "ACTIVEABLE", in: animation)
                            } else {
                                Capsule()
                                    .fill(Color.gray.opacity(0.2))
                            }
                        }
                        .foregroundColor(selectedTag == tag ? .white : .black)
                        .cornerRadius(10)
                        .onTapGesture {
                            withAnimation(.interactiveSpring(response: 0.5, dampingFraction: 0.7, blendDuration: 0.7)) {
                                selectedTag = tag
                            }
                        }
                }
            }
            .padding(.horizontal, 16)
        }
    }   
}

//#Preview {
//    TradeHistoryView()
//}
