//
//  Utils.swift
//  GridTradingToolbox
//
//  Created by 陈敏杰 on 2024/1/27.
//

import Foundation

class Utils {
    public static func trimTrailingZeros(from string: String) -> String {
        var result = string
        // 如果字符串以 .0 或者 . 结尾，则移除它们
        while result.hasSuffix(".0") || result.hasSuffix(".") {
            result = String(result.dropLast())
        }
        // 如果字符串以 0 结尾并且包含 "."，则移除尾部的零
        while result.contains(".") && result.hasSuffix("0") {
            result = String(result.dropLast())
        }
        
        if result.hasSuffix(".") {
            result = String(result.dropLast())
        }
        return result
    }

    public static func displayDecimal(decimal: Double, num: Int = 2) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal  // 这会添加千位分隔符
        formatter.minimumFractionDigits = 0
        formatter.maximumFractionDigits = num
        return formatter.string(from: NSNumber(value: decimal)) ?? "0"
    }

    public static func displayPrice(price: Double, num: Int = 3) -> String {  
        let roundedPrice = price.rounded(toPlaces: num)
        return trimTrailingZeros(from: String(format: "%.\(num)f", roundedPrice))
    }
    
    public static func displayPrice(price: Int, num: Int = 3) -> String {
        let roundedPrice = (Double(price) / 1000.0).rounded(toPlaces: num)
        return trimTrailingZeros(from: String(format: "%.\(num)f", roundedPrice))
    }
    
    public static func displayPrice(price: Int) -> String {
        // 先将整数转换为浮点数并除以1000，然后四舍五入到3位小数
        let roundedPrice = (Double(price) / 1000.0).rounded(toPlaces: 3)
        return trimTrailingZeros(from: String(format: "%.3f", roundedPrice))
    }
    
    public static func div(first: Int, last: Int) -> Double {
        if last == 0 {
            return 0.0
        }
        
        return Double(first) / Double(last)
    }
    
    public static func isSameDay(_ date1: Date, _ date2: Date) -> Bool {
        let calendar = Calendar.current
        return calendar.isDate(date1, inSameDayAs: date2)
    }
    
    public static func checkNumberFormat(number: String, isInterval: Bool = false) -> Bool {
        let numberStr = number.replacingOccurrences(of: ",", with: "")
        if (isInterval) {
            guard let _ = intervalNumberFormatter.number(from: numberStr) else { return false }
        } else {
            guard let _ = numberFormatter.number(from: numberStr) else { return false }
        }
        let splits = numberStr.split(separator: ".")
        if splits.count < 2 { // No decimal part
            return true
        } else {
            if (isInterval) {
                return splits[1].count <= 1
            }
            return splits[1].count <= 3
        }
    }
    
    public static func formatShares(_ input: String) -> String {
        let result = Utils.isNonNegativeIntegerWithoutDecimal(input)
        var shares = "0"
        if result {
            shares = String(Utils.stringToRoundedInt(input) ?? 0)
        }
        return shares
    }
    
    public static func isNonNegativeIntegerWithoutDecimal(_ input: String) -> Bool {
        // 确认字符串不为空
        guard !input.isEmpty else {
            return false
        }
        
        // 使用正则表达式确认字符串只包含数字
        let regex = "^[0-9]+$"
        let predicate = NSPredicate(format: "SELF MATCHES %@", regex)
        return predicate.evaluate(with: input)
    }
    
    public static func stringToRoundedInt(_ input: String) -> Int? {
        // 尝试将字符串转换为整数
        if let number = Int(input) {
            // 按100向下取整
            return (number / 100) * 100
        } else {
            // 转换失败，返回nil
            return nil
        }
    }

    public static func times1000Round(price: String) -> Int {
        return Int(round((price as NSString).doubleValue * 1000))
    }
    
    public static func calcDaysBetweenDates(startDate: Date, endDate: Date) -> Int {
        let calendar = Calendar.current

        let date1 = calendar.startOfDay(for: startDate)
        let date2 = calendar.startOfDay(for: endDate)

        let flags: Set<Calendar.Component> = [.day, .hour]
        let components = calendar.dateComponents(flags, from: date1, to: date2)

        var days = components.day!

        // if components.hour! > 0 {
        //     days += 1
        // }

        return days > 0 ? days : 1
    }
    
    public static func displayDate(date: Date) -> String {
        return dateFormatter.string(from: date)
    }
    
    public static func xirr(values: [Double], dates: [Date], guess: Double = 0.15) -> Double? {
        var sortedValues = [Double]();
        var sortedDates = [Date]();
        zip(dates, values).sorted { $0.0 < $1.0 }.forEach { sortedDates.append($0.0); sortedValues.append($0.1) }

        let tolerance = 1e-7
        let maxIterations = 100
        var guess = guess
        
        for _ in 0..<maxIterations {
            let f = computeF(values: sortedValues, dates: sortedDates, rate: guess)
            let fDerivative = computeFDerivative(values: sortedValues, dates: sortedDates, rate: guess)
            let newGuess = guess - f / fDerivative
            if abs(newGuess - guess) < tolerance {
                return newGuess
            }
            guess = newGuess
        }
        
        return nil // Return nil if no solution is found after maxIterations
    }

    private static func computeF(values: [Double], dates: [Date], rate: Double) -> Double {
        let dayCountBasis: Double = 365 // This can be adjusted based on the day count convention
        var result = 0.0
        
        for i in 0..<values.count {
            let dayCount = Double(Calendar.current.dateComponents([.day], from: dates[0], to: dates[i]).day!)
            let fraction = dayCount / dayCountBasis
            result += (values[i] / pow(1 + rate, fraction))
        }
        
        return result
    }

    private static func computeFDerivative(values: [Double], dates: [Date], rate: Double) -> Double {
        let dayCountBasis: Double = 365 // This can be adjusted based on the day count convention
        var result = 0.0
        
        for i in 0..<values.count {
            let dayCount = Double(Calendar.current.dateComponents([.day], from: dates[0], to: dates[i]).day!)
            let fraction = dayCount / dayCountBasis
            result -= (fraction * values[i] / pow(1 + rate, fraction + 1))
        }
        
        return result
    }
    
    private static var numberFormatter: NumberFormatter = {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        formatter.maximumFractionDigits = 3
        return formatter
    }()
    
    private static var intervalNumberFormatter: NumberFormatter = {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        formatter.maximumFractionDigits = 1
        return formatter
    }()
    
    private static var dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter
    }()
}

extension Double {
    func rounded(toPlaces places: Int) -> Double {
        let multiplier = pow(10.0, Double(places))
        return (self * multiplier).rounded() / multiplier
    }
}
