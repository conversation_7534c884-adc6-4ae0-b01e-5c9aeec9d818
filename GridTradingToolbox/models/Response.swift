//
//  Response.swift
//  GridTradingToolbox
//
//  Created by 陈敏杰 on 2024/1/29.
//

import Foundation

struct Response: Decodable {
    let msg: String
    let result: [SyncStrategy]
}

struct SearchCodeResponse: Decodable {
    let stocks: [StockCandidate]
    let msg: String
}

struct StockCandidate: Decodable {
    let code: String
    let name: String
}

struct StocksPriceResponse: Decodable {
    let success: [StockPriceResponse]
    let failed: [String]
}

struct StockPriceResponse: Decodable {
    let code: String
    let open: Double
    let close: Double
    let price: Double
    let high: Double
    let low: Double
    let changePercent: Double
}

struct IndexsInfoResponse: Decodable {
    let success: [IndexInfoResponse]
    let failed: [String]
}

struct IndexInfoResponse: Decodable {
    var code: String
    var name: String
    var metricType: String
    var publishDate: Date
    var changeRate: Double
    var closePoint: Double

    var peTtmPercentile5Y: Double
    var pbPercentile5Y: Double
    var psTtmPercentile5Y: Double

    var peTtmPercentile10Y: Double
    var pbPercentile10Y: Double
    var psTtmPercentile10Y: Double

    var updateTimestamp: Int64
}

struct SyncStrategy: Decodable {
    let id: Int
    let name: String
    let code: String?
    let interval: Int
    let targetPrice: Int
    let amount: Int
    let maxFall: Int
    let buyStrategy: Int
    let incrementalBuyRatio: Int?
    let mediumLargeSwitch: Bool
    let mediumInterval: Int
    let largeInterval: Int
    let grids: [SyncGrid]
    let soldGrids: [SyncGrid]
    let createAt: Date
    let updateAt: Date
}

struct SyncGrid: Decodable {
    let id: Int
    let strategyId: Int
    let gridType: Int
    let grade: Int
    let holdShares: Int
    let theoreticalBuyPrice: Int
    let theoreticalBuyShares: Int
    let theoreticalSellPrice: Int
    let theoreticalSellShares: Int
    let triggerAmount: Int
    let status: Int
    let tradeLogs: [SyncTradeLog]
    let createAt: Date
    let updateAt: Date
    let buyAt: Date
}

struct SyncTradeLog: Decodable {
    let id: Int
    let strategyId: Int
    let gridId: Int
    let tradeType: Int
    let tradeShares: Int
    let tradePrice: Int
    let tradeAt: Date
    let createAt: Date
}
