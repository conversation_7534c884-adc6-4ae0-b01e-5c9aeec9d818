//
//  Stock.swift
//  GridTradingToolbox
//
//  Created by 陈敏杰 on 2024/1/5.
//

import Foundation
import SwiftData

typealias Stock = SchemaV1.Stock
typealias FavoriteStock = SchemaV1.FavoriteStock

extension SchemaV1 {
    @Model
    class Stock: Codable {
        enum CodingKeys: CodingKey {
            case code, name
        }
        
        var code: String = ""
        var name: String = ""
        
        init(code: String, name: String) {
            self.code = code
            self.name = name
        }
        
        required init(from decoder: Decoder) throws {
            let container = try decoder.container(keyedBy: CodingKeys.self)
            self.code = try container.decode(String.self, forKey: .code)
            self.name = try container.decode(String.self, forKey: .name)
        }
        
        func encode(to encoder: Encoder) throws {
            var container = encoder.container(keyedBy: CodingKeys.self)
            try container.encode(code, forKey: .code)
            try container.encode(name, forKey: .name)
        }
    }
    
    @Model
    class FavoriteStock: Identifiable {
        var id = UUID()
        var code: String = ""
        var name: String = ""
        var createTime: Date = Date()
        
        init(code: String, name: String, createTime: Date) {
            self.code = code
            self.name = name
            self.createTime = createTime
        }
    }
}
