//
//  DataHandler.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/11/19.
//

import Foundation
import SwiftData

//@ModelActor
public actor DataHandler {
//    @MainActor
//    init(container: ModelContainer) {
//      let modelContext = container.mainContext
//      modelExecutor = DefaultSerialModelExecutor(modelContext: modelContext)
//      self.modelContainer = container
//    }
//
//    @discardableResult
//    func newStrategy(strategy: Strategy) throws -> PersistentIdentifier {
//        modelContext.insert(strategy)
//        try modelContext.save()
//        return strategy.persistentModelID
//    }
//
//    func updateStrategy(id: PersistentIdentifier, strategy: Strategy) throws {
//      guard let item = self[id, as: Strategy.self] else { return }
//      item.editFrom(source: strategy)
//      try modelContext.save()
//    }
//
//    func activateStrategy(id: PersistentIdentifier) throws {
//      guard let strategy = self[id, as: Strategy.self] else { return }
//      strategy.status = Strategy.StrategyStatus.active.rawValue
//      try modelContext.save()
//    }
//
//    func deleteStrategy(id: PersistentIdentifier) throws {
//      guard let strategy = self[id, as: Strategy.self] else { return }
//      if strategy.calculateInterest {
//        for grid in strategy.grids! {
//          grid.cancelNotification()
//        }
//      }
//      modelContext.delete(strategy)
//      try modelContext.save()
//    }
//
//    func newGrid(grid: TradeGrid, tradeLog: TradeLog, strategy: Strategy, accountIndex: String?) throws {
//      grid.strategy = strategy
//      grid.accountId = accountIndex
//      grid.holdShares = tradeLog.tradeShares
//      grid.theoreticalSellShares = grid.recalculateTheoreticalSellShares(buyPrice: tradeLog.tradePrice, buyShares: tradeLog.tradeShares)
//      grid.tradeLogs!.append(tradeLog)
//      strategy.addGrid(grid: grid)
//      modelContext.insert(grid)
//      modelContext.insert(tradeLog)
//      try modelContext.save()
//    }
//
//    func updateBuyTradeLog(grid: TradeGrid, tradePrice: Int, tradeShares: Int, tradeAt: Date, mood: String?, note: String?) throws {
//      guard let buyTradeLog = grid.buyTradeLog else { 
//        print("updateBuyTradeLog: buyTradeLog not found")
//        return
//      }
//      if grid.isSold {
//          grid.holdShares = tradeShares - grid.sellTradeLog!.tradeShares
//      } else {
//          grid.holdShares = tradeShares
//          if grid.strategy!.calculateInterest && !Utils.isSameDay(buyTradeLog.tradeAt, tradeAt) {
//              grid.cancelNotification()
//              grid.addNotification()
//          }
//      }
//      grid.theoreticalSellShares = grid.recalculateTheoreticalSellShares(buyPrice: tradePrice, buyShares: tradeShares)
//      buyTradeLog.tradePrice = tradePrice
//      buyTradeLog.tradeShares = tradeShares
//      buyTradeLog.tradeAt = tradeAt
//      buyTradeLog.mood = mood
//      buyTradeLog.note = note
//      if !grid.isSold {
//        grid.strategy!.totalInvestmentAmount = grid.strategy!.calculateTotalInvestment()
//      }
//      try modelContext.save()
//    }
//
//    func deleteGrid(grid: TradeGrid, strategy: Strategy) throws {
//      if grid.isSold {
//        // todo 检测预留利润的股数是否已经被卖出
//        let index = strategy.grids!.firstIndex(of: grid)
//        if index != nil {
//          strategy.grids!.remove(at: index!)
//        }
//      } else {
//        if grid.gridType == TradeGrid.GridType.small.rawValue && strategy.sortedSmallGrids[0] == grid {
//          let index = strategy.grids!.firstIndex(of: grid)
//          if index != nil {
//            strategy.grids!.remove(at: index!)
//          }
//        } else if grid.gridType == TradeGrid.GridType.medium.rawValue && strategy.sortedMediumGrids[0] == grid {
//          let index = strategy.grids!.firstIndex(of: grid)
//          if index != nil {
//            strategy.grids!.remove(at: index!)
//          }
//        } else if grid.gridType == TradeGrid.GridType.large.rawValue && strategy.sortedLargeGrids[0] == grid {
//          let index = strategy.grids!.firstIndex(of: grid)
//          if index != nil {
//            strategy.grids!.remove(at: index!)
//          }
//        }
//        strategy.totalInvestmentAmount = strategy.calculateTotalInvestment()
//      }
//      modelContext.delete(grid)
//      try modelContext.save()
//    }
//
//    func deleteFlexibleTradeLog(tradeLog: FlexibleTradeLog, strategy: Strategy) throws {
//      let index = strategy.flexibleTradeLog!.firstIndex(of: tradeLog)
//      if index != nil {
//        strategy.flexibleTradeLog!.remove(at: index!)
//      }
//      modelContext.delete(tradeLog)
//      try modelContext.save()
//    }
//
//    func updateFlexibleTradeLog(tradePrice: Int, tradeShares: Int, tradeAt: Date, tradeLog: FlexibleTradeLog) throws {
//      tradeLog.tradePrice = tradePrice
//      tradeLog.tradeShares = tradeShares
//      tradeLog.tradeAt = tradeAt
//      try modelContext.save()
//    }
//
//    func sellGrid(tradeShares: Int, tradePrice: Int, tradeAt: Date, mood: String?, note: String?, grid: TradeGrid, strategy: Strategy) throws {
//        // 确保 grid 和 strategy 都在有效的上下文中
//        guard let contextGrid = modelContext.model(for: grid.persistentModelID) as? TradeGrid else { return }
//        
//        let sellTradeLog = TradeLog(grid: contextGrid, 
//                                   tradeType: TradeLog.TradeType.sell.rawValue,
//                                   tradeShares: tradeShares,
//                                   tradePrice: tradePrice,
//                                   tradeAt: tradeAt,
//                                   mood: mood,
//                                   note: note)
//        
//        contextGrid.tradeLogs!.append(sellTradeLog)
//        contextGrid.holdShares = contextGrid.buyTradeLog!.tradeShares - tradeShares
//        contextGrid.strategy!.totalInvestmentAmount = contextGrid.strategy!.calculateTotalInvestment()
//        contextGrid.cancelNotification()
//        
//        modelContext.insert(sellTradeLog)
//        try modelContext.save()
//    }
//
//    func updateSellTradeLog(grid: TradeGrid, tradePrice: Int, tradeShares: Int, tradeAt: Date, mood: String?, note: String?) throws {
//      guard let sellTradeLog = grid.sellTradeLog else { return }
//      sellTradeLog.tradePrice = tradePrice
//      sellTradeLog.tradeShares = tradeShares
//      sellTradeLog.tradeAt = tradeAt
//      sellTradeLog.mood = mood
//      sellTradeLog.note = note
//      grid.holdShares = grid.buyTradeLog!.tradeShares - tradeShares
//      try modelContext.save()
//    }
//    
}
