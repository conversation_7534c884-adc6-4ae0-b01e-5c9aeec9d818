//
//  Grid.swift
//  GridTradingToolbox
//
//  Created by 陈敏杰 on 2024/1/7.
//

import Foundation
import SwiftData
import UserNotifications

typealias TradeGrid = SchemaV1.TradeGrid

extension SchemaV1 {
    @Model
    class TradeGrid: Identifiable {
        enum GridType: Int {
            case small = 0, medium, large
        }
        
        var id: UUID = UUID()
        var strategy: Strategy? = nil
        var gridType: Int = 0
        var grade: Int = 0
        var holdShares: Int = 0
        var theoreticalBuyPrice: Int = 0
        var theoreticalBuyShares: Int = 0
        var theoreticalSellPrice: Int = 0
        var theoreticalSellShares: Int = 0
        var triggerAmount: Int = 0
        // 0: 持有；1: 已卖出
        var status: Int? = nil
        @Relationship(deleteRule: .cascade, inverse: \TradeLog.grid) var tradeLogs: [TradeLog]? = [TradeLog]()
        var notificationId : String? = nil
        var accountId: String? = nil
        var createAt: Date = Date.now
        var updateAt: Date = Date.now
        
        var displayTriggerAmount: String {
            get {
                let doubleValue = Double(triggerAmount) / 1000.0
                return Utils.trimTrailingZeros(from: String(format: "%.3f", doubleValue))
            }
        }
        
        var displayGrade: String {
            get {
                let doubleValue = Double(grade) / 1000.0
                return String(format: "%.3f", doubleValue)
            }
        }
        
        var displayGridType: String {
            switch gridType {
            case GridType.medium.rawValue:
                return "中网"
            case GridType.large.rawValue:
                return "大网"
            default:
                return "小网"
            }
        }
        
        var displayTheoreticalBuyPrice: String {
            get {
                let doubleValue = Double(theoreticalBuyPrice) / 1000.0
                return Utils.trimTrailingZeros(from: String(format: "%.3f", doubleValue))
            }
        }
        
        var displayTheoreticalBuyShares: String {
            get {
                return String(theoreticalBuyShares)
            }
        }
        
        var displayTheoreticalSellPrice: String {
            get {
                let doubleValue = Double(theoreticalSellPrice) / 1000.0
                return Utils.trimTrailingZeros(from: String(format: "%.3f", doubleValue))
            }
        }
        
        var displayTheoreticalSellShares: String {
            get {
                return String(theoreticalSellShares)
            }
        }
        
        var displayProfit: String {
            let buy = buyTradeLog!
            let sell = sellTradeLog!
            let profit = (sell.tradePrice - buy.tradePrice) * buy.tradeShares
            let doubleValue = Double(profit) / 1000.0
            // if doublevalue larger than 0, add a "+" prefix
            if doubleValue > 0 {
                return "+" + doubleValue.formattedWithSeparator
            }
            else if doubleValue < 0 {
                return doubleValue.formattedWithSeparator
            }
            else {
                return "0.00"
            }
        }
        
        var displayHoldDays: Int {
            let days = Utils.calcDaysBetweenDates(startDate: buyTradeLog!.tradeAt, endDate: sellTradeLog!.tradeAt)
            if days == 0 {
                return 1
            }
            return days
        }
        
        var sellTradeLog: TradeLog? {
            for tradeLog in tradeLogs! {
                if tradeLog.tradeType == TradeLog.TradeType.sell.rawValue {
                    return tradeLog
                }
            }
            return nil
        }
        
        var buyTradeLog: TradeLog? {
            for tradeLog in tradeLogs! {
                if tradeLog.tradeType == TradeLog.TradeType.buy.rawValue {
                    return tradeLog
                }
            }
            return nil
        }
        
        var isSold: Bool {
            guard let logs = tradeLogs else { return false}
            return logs.filter{ log in
                log.tradeType == TradeLog.TradeType.sell.rawValue
            }.count > 0
        }
        
        var sellPrice: Int {
            if !strategy!.calculateInterest {
                return theoreticalSellPrice
            }
            let today = Date()
            var times = 0
            let calendar = Calendar.current
            times = calendar.dateComponents([.month], from: buyTradeLog!.tradeAt, to: today).month ?? 0
            if (strategy!.interestPeriod! == Strategy.InterestPeriod.year.rawValue) {
                times = times / 12
            }
            
            return Int(Double(theoreticalSellPrice) * pow(Double(strategy!.interest! + 100000) / 100000.0, Double(times)))
        }
        
        init(id: UUID = UUID(), strategy: Strategy? = nil, gridType: Int, grade: Int, holdShares: Int, theoreticalBuyPrice: Int, theoreticalBuyShares: Int, theoreticalSellPrice: Int, theoreticalSellShares: Int, triggerAmount: Int, status: Int? = nil, createAt: Date = .now, updateAt: Date = .now) {
            self.id = id
            self.strategy = strategy
            self.gridType = gridType
            self.grade = grade
            self.holdShares = holdShares
            self.theoreticalBuyPrice = theoreticalBuyPrice
            self.theoreticalBuyShares = theoreticalBuyShares
            self.theoreticalSellPrice = theoreticalSellPrice
            self.theoreticalSellShares = theoreticalSellShares
            self.triggerAmount = triggerAmount
            self.status = status
            self.createAt = createAt
            self.updateAt = updateAt
        }
        
        init(id: UUID = UUID(), strategy: Strategy? = nil, gridType: Int, grade: Int, holdShares: Int, theoreticalBuyPrice: Int, theoreticalBuyShares: Int, theoreticalSellPrice: Int, theoreticalSellShares: Int, triggerAmount: Int, status: Int? = nil, notificationId: String? = nil, accountId: String? = nil, createAt: Date = .now, updateAt: Date = .now) {
            self.id = id
            self.strategy = strategy
            self.gridType = gridType
            self.grade = grade
            self.holdShares = holdShares
            self.theoreticalBuyPrice = theoreticalBuyPrice
            self.theoreticalBuyShares = theoreticalBuyShares
            self.theoreticalSellPrice = theoreticalSellPrice
            self.theoreticalSellShares = theoreticalSellShares
            self.triggerAmount = triggerAmount
            self.notificationId = notificationId
            self.accountId = accountId
            self.createAt = createAt
            self.updateAt = updateAt
            self.status = status
        }
        
        func getSellPriceWithInterest(calInterest: Bool, period: Int?, interest: Int?) -> String {
            if !calInterest {
                return displayTheoreticalSellPrice
            }
            
            let today = Date()
            var times = 0
            let calendar = Calendar.current
            times = calendar.dateComponents([.month], from: buyTradeLog!.tradeAt, to: today).month ?? 0
            if (strategy!.interestPeriod! == Strategy.InterestPeriod.year.rawValue) {
                times = times / 12
            }
            
            //        return String(format: "%.3f", Double(theoreticalSellPrice * (100000 + times * interest!)) / *********.0)
            return String(format: "%.3f", Double(theoreticalSellPrice) / 1000.0 * pow(Double(interest! + 100000) / 100000.0, Double(times)))
        }
        
        func getSellPrice() -> Int {
            if strategy!.calculateInterest {
                let today = Date()
                var times = 0
                let calendar = Calendar.current
                times = calendar.dateComponents([.month], from: buyTradeLog!.tradeAt, to: today).month ?? 0
                if (strategy!.interestPeriod! == Strategy.InterestPeriod.year.rawValue) {
                    times = times / 12
                }
                return Int(Double(theoreticalSellPrice) * pow(Double(strategy!.interest! + 100000) / 100000.0, Double(times)))
            }
            return theoreticalSellPrice
        }
        
        func isOverInterestPeriod() -> Bool {
            if !strategy!.calculateInterest {
                return false
            }
            
            let today = Date()
            var times = 0
            let calendar = Calendar.current
            times = calendar.dateComponents([.month], from: buyTradeLog!.tradeAt, to: today).month ?? 0
            if (strategy!.interestPeriod! == Strategy.InterestPeriod.year.rawValue) {
                times = times / 12
            }
            
            if times > 0 {
                return true
            }
            return false
        }
        
        func getProfitRatio() -> String {
            let buy = buyTradeLog!
            let sell = sellTradeLog!
            let profitRatio = Double(sell.tradePrice - buy.tradePrice) / Double(buy.tradePrice)
            // if profitRatio larger than 0, add a "+" prefix
            if profitRatio > 0 {
                return "+" + String(format: "%.2f", profitRatio * 100) + "%"
            }
            else if profitRatio < 0 {
                return String(format: "%.2f%", profitRatio * 100) + "%"
            }
            else {
                return "0.00%"
            }
        }
        
        // 是否盈利
        func isProfit() -> Bool {
            return sellTradeLog!.tradePrice > buyTradeLog!.tradePrice
        }
        
        func cancelNotification() {
            if notificationId != nil {
                UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: [notificationId!])
            }
        }
        
        func addNotification() {
            let content = UNMutableNotificationContent()
            content.title = "计息通知"
            content.subtitle = "「\(strategy!.name)」的「\(displayGrade)」-「\(displayGridType)」档位卖出价已更新"
            content.sound = UNNotificationSound.default
            
            var dateComponents = DateComponents()
            if strategy!.interestPeriod == Strategy.InterestPeriod.year.rawValue {
                dateComponents.month = Calendar.current.component(.month, from: buyTradeLog!.tradeAt)
            }
            dateComponents.day = Calendar.current.component(.day, from: buyTradeLog!.tradeAt)
            dateComponents.hour = 9
            dateComponents.minute = 0
            
            let trigger = UNCalendarNotificationTrigger(dateMatching: dateComponents, repeats: true)
            let notificationId = UUID().uuidString
            self.notificationId = notificationId
            let request = UNNotificationRequest(identifier: notificationId, content: content, trigger: trigger)
            UNUserNotificationCenter.current().add(request) { (error) in
                if let error = error  {
                    print("添加失败: \(error.localizedDescription)")
                } else {
                    print("添加成功")
                }
            }
        }
        
        static func from(syncGrid: SyncGrid, strategy: Strategy? = nil) -> TradeGrid {
            let grid = TradeGrid(strategy: strategy, gridType: syncGrid.gridType, grade: syncGrid.grade * 10, holdShares: syncGrid.holdShares, theoreticalBuyPrice: syncGrid.theoreticalBuyPrice, theoreticalBuyShares: syncGrid.theoreticalBuyShares, theoreticalSellPrice: syncGrid.theoreticalSellPrice, theoreticalSellShares: syncGrid.theoreticalSellShares, triggerAmount: syncGrid.triggerAmount)
            grid.createAt = syncGrid.createAt
            grid.updateAt = syncGrid.updateAt
            
            //        for log in syncGrid.tradeLogs {
            //            grid.tradeLogs.append(TradeLog.from(syncTradeLog: log, strategyId: strategyId, gridId: grid.id))
            //        }
            
            return grid
        }
        
        // 重新计算理论卖出份额
        func recalculateTheoreticalSellShares(buyPrice: Int, buyShares: Int) -> Int {
            var sellShares = buyShares;
            if strategy!.retainProfitSwitch {
                // let profit = (theoreticalSellPrice - theoreticalBuyPrice) * buyShares / theoreticalSellPrice * strategy!.retainProfit!;
                let profit = (theoreticalSellPrice - theoreticalBuyPrice) * buyShares
                let shares = Double(profit) / Double(theoreticalSellPrice) * Double(strategy!.retainProfit!) / 1000.0
                let ceilShares = ceil(shares / 100.0) * 100
                print("fuck profit: \(profit), shares: \(shares), ceilShares: \(ceilShares), theoreticalSellPrice: \(theoreticalSellPrice), theoreticalBuyPrice: \(theoreticalBuyPrice), strategy!.retainProfit!: \(strategy!.retainProfit!)")
                sellShares = sellShares - Int(ceilShares)
                if sellShares < 0 {
                    sellShares = 0
                }
            }
            return sellShares;
        }
        
    }
}
