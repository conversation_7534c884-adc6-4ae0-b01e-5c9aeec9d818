//
//  StockAccount.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/5/11.
//

import Foundation
import SwiftData

typealias StockAccount = SchemaV1.StockAccount

extension SchemaV1 {
    @Model
    class StockAccount: Identifiable {
        
        var id: String = UUID().uuidString
        var name: String = ""
        var transactionRate: Int = 0
        var minFee: Int = 0
        var createAt: Date = Date.now
        var updateAt: Date = Date.now
        
        init(name: String, transactionRate: Int, minFee: Int) {
            self.name = name
            self.transactionRate = transactionRate
            self.minFee = minFee
        }
        
        func calTrxFee(amount: Double) -> Double {
            let trxFee = Double(amount) * Double(transactionRate) / 1000000.0
            let minTrxFee = Double(minFee) / 1000.0
            if trxFee > minTrxFee {
                return trxFee
            }
            return minTrxFee
        }
    }
}
