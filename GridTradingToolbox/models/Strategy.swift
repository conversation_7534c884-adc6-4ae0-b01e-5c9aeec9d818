//
//  Strategy.swift
//  GridTradingToolbox
//
//  Created by 陈敏杰 on 2024/1/2.
//

import Foundation
import SwiftData
import SwiftUI

typealias Strategy = SchemaV1.Strategy

extension SchemaV1 {
    @Model
    class Strategy: Identifiable {
        enum GridType: Int {
            case arithmetic = 1, geometric
        }
        enum BuyStrategy: Int {
            case equal = 1, incremental, fixedShares
        }
        enum InterestPeriod: Int {
            case month = 1, year
        }
        enum StrategyStatus: Int {
            case active = 1, archive
        }
        
        var id: UUID = UUID()
        var name: String = ""
        var code: String? = nil
        var interval: Int = 0
        var targetPrice: Int = 0
        var amount: Int = 0
        var maxFall: Int = 0
        var buyStrategy: Int = 0
        var incrementalBuyRatio: Int? = nil
        // 买入策略-固定股数
        var fixedShares: Int? = nil
        var mediumLargeSwitch: Bool = false
        var mediumInterval: Int? = nil
        var largeInterval: Int? = nil
        var triggerNotify: Bool = false
        var calculateInterest: Bool = false
        var interestPeriod: Int? = nil
        var interest: Int? = nil
        var retainProfitSwitch: Bool = false
        // 预留利润比例
        var retainProfit: Int? = nil
        var status: Int? = nil
        @Relationship(deleteRule: .cascade, inverse: \TradeGrid.strategy) var grids: [TradeGrid]? = [TradeGrid]()
        @Relationship(deleteRule: .cascade, inverse: \FlexibleTradeLog.strategy) var flexibleTradeLog: [FlexibleTradeLog]? = [FlexibleTradeLog]()
        var nextSellPrice: Int? = nil
        var nextBuyPrice: Int? = nil
        // 策略总投入金额
        var totalInvestmentAmount: Int? = nil
        var totalProfitAmount: Int? = nil
        var realTotalProfitAmount: Int? = nil
        private(set) var retainShares: Int? = nil
        var createAt: Date = Date.now
        var updateAt: Date = Date.now
        var profit: Int? = nil
        
        
        // 新版中大网格配置
        var mediumGridEnabled: Bool? = nil
        var mediumRiseInterval: Int? = nil
        var mediumFallInterval: Int? = nil
        var largeGridEnabled: Bool? = nil
        var largeRiseInterval: Int? = nil
        var largeFallInterval: Int? = nil
        
        var gridType: Int = GridType.arithmetic.rawValue
        
        
        var holdGrids: [TradeGrid] {
            return grids!.filter({!$0.isSold})
        }
        
        var soldGrids: [TradeGrid] {
            return grids!.filter({$0.isSold})
        }
        
        var displayTargetPrice: String {
            let doubleValue = Double(targetPrice) / 1000.0
            return String(format: "%.3f", doubleValue)
        }
        
        var displayAmount: String {
            let doubleValue = Double(amount) / 1000.0
            return String(format: "%.3f", doubleValue)
        }
        
        var displayInterval: String {
            let doubleValue = Double(interval) / 1000.0
            return String(format: "%.3f", doubleValue)
        }
        
        var displayIncrementalBuyRatio: String {
            let doubleValue = Double(incrementalBuyRatio!) / 1000.0
            return String(format: "%.3f", doubleValue)
        }
        
        var displayRetainProfit: String {
            let doubleValue = Double(retainProfit!) / 1000.0
            return String(format: "%.3f", doubleValue)
        }
        
        var displayMediumInterval: String {
            let doubleValue = Double(mediumInterval!) / 1000.0
            return String(format: "%.3f", doubleValue)
        }
        
        var displayLargeInterval: String {
            let doubleValue = Double(largeInterval!) / 1000.0
            return String(format: "%.3f", doubleValue)
        }
        
        var displayInterest: String {
            let doubleValue = Double(interest!) / 1000.0
            return String(format: "%.3f", doubleValue)
        }
        
        var displayMaxFall: String {
            let doubleValue = Double(maxFall) / 1000.0
            return String(format: "%.3f", doubleValue)
        }
        
        var displayTotalInvestment: String {
            let doubleValue = Double(totalInvestment) / 1000.0
            return Utils.trimTrailingZeros(from: String(format: "%.3f", doubleValue))
        }
        
        var displayTotalProfit: String {
            var doubleValue = 0.0;
            if holdGrids.isEmpty && !flexibleTradeLog!.isEmpty {
                doubleValue = Double(realTotalProfit) / 1000.0
            } else {
                doubleValue = Double(totalProfit) / 1000.0
            }
            return Utils.trimTrailingZeros(from: String(format: "%.3f", doubleValue))
        }
        
        var smallGrids: [TradeGrid] {
            return grids!.filter { grid in
                return !grid.isSold && grid.gridType == TradeGrid.GridType.small.rawValue;
            }
        }
        
        var sortedSmallGrids: [TradeGrid] {
            return grids!.filter { grid in
                return !grid.isSold && grid.gridType == TradeGrid.GridType.small.rawValue;
            }.sorted { g1, g2 in
                return g1.grade < g2.grade;
            }
        }
        
        var mediumGrids: [TradeGrid] {
            return grids!.filter { grid in
                return !grid.isSold && grid.gridType == TradeGrid.GridType.medium.rawValue;
            }
        }
        
        var sortedMediumGrids: [TradeGrid] {
            return grids!.filter { grid in
                return !grid.isSold && grid.gridType == TradeGrid.GridType.medium.rawValue;
            }.sorted { g1, g2 in
                return g1.grade < g2.grade;
            }
        }
        
        var largeGrids: [TradeGrid] {
            return grids!.filter { grid in
                return !grid.isSold && grid.gridType == TradeGrid.GridType.large.rawValue;
            }
        }
        
        var sortedLargeGrids: [TradeGrid] {
            return grids!.filter { grid in
                return !grid.isSold && grid.gridType == TradeGrid.GridType.large.rawValue;
            }.sorted { g1, g2 in
                return g1.grade < g2.grade;
            }
        }
        
        var avgHoldDaysOfGrids: Double {
            var holdDays = 0
            for grid in grids! {
                if grid.isSold {
                    holdDays += Utils.calcDaysBetweenDates(startDate: grid.buyTradeLog!.tradeAt, endDate: grid.sellTradeLog!.tradeAt)
                } else {
                    holdDays += Utils.calcDaysBetweenDates(startDate: grid.buyTradeLog!.tradeAt, endDate: Date.now)
                }
            }
            if grids!.count > 0 {
                return Double(holdDays) / Double(grids!.count)
            }
            return 0
        }
        
        var holdShares: Int {
            var shares = 0
            for grid in grids! {
                shares += grid.holdShares
            }
            for log in flexibleTradeLog! {
                if log.tradeType == FlexibleTradeLog.TradeType.sell.rawValue {
                    shares -= log.tradeShares;
                }
            }
            return shares
        }
        
        // 历史遗留问题，totalInvestmentAmount 为空时，计算总投入金额
        var totalInvestment: Int {
            if let totalInvestmentAmount = totalInvestmentAmount {
                return totalInvestmentAmount
            }
            self.totalInvestmentAmount = calculateTotalInvestment()
            return totalInvestmentAmount!;
        }
        
        func calculateTotalInvestment() -> Int {
            var inverstment = 0
            for grid in holdGrids {
                for tradeLog in grid.tradeLogs! {
                    if tradeLog.tradeType == TradeLog.TradeType.buy.rawValue {
                        inverstment += tradeLog.tradeShares * tradeLog.tradePrice
                    }
                }
            }
            return inverstment
        }
        
        func getRetainShares() -> Int {
            if let shares = retainShares {
                return shares
            }
            self.retainShares = calculateRetainShares()
            return retainShares!
        }
        
        func setRetainShares(_ shares: Int) {
            self.retainShares = shares
        }
        
        func calculateRetainShares() -> Int {
            var shares = 0
            for grid in soldGrids {
                shares += grid.holdShares
            }
            for log in flexibleTradeLog! {
                if log.tradeType == FlexibleTradeLog.TradeType.sell.rawValue {
                    shares -= log.tradeShares;
                } else if log.tradeType == FlexibleTradeLog.TradeType.buy.rawValue {
                    shares += log.tradeShares;
                }
            }
            return shares
        }
        
        var totalProfit: Int {
            if let totalProfitAmount = totalProfitAmount {
                return totalProfitAmount
            }
            self.totalProfitAmount = calculateTotalProfit()
            return totalProfitAmount!;
        }
        
        func calculateTotalProfit() -> Int {
            var totalProfit = 0
            for grid in soldGrids {
                var gridBuyPrice = 0
                var gridSellPrice = 0
                var shares = 0
                for tradeLog in grid.tradeLogs ?? [] {
                    if tradeLog.tradeType == TradeLog.TradeType.sell.rawValue {
                        gridSellPrice = tradeLog.tradePrice
                    } else {
                        gridBuyPrice = tradeLog.tradePrice
                        shares = tradeLog.tradeShares
                    }
                }
                totalProfit += (gridSellPrice - gridBuyPrice) * shares
            }
            return totalProfit
        }
        
        var realTotalProfit: Int {
            if let realProfitAmount = realTotalProfitAmount {
                return realProfitAmount
            }
            self.realTotalProfitAmount = calculateRealTotalProfit()
            return realTotalProfitAmount!;
        }
        
        func calculateRealTotalProfit() -> Int {
            var buyCount = 0
            var sellCount = 0
            for grid in soldGrids {
                for tradeLog in grid.tradeLogs! {
                    if tradeLog.tradeType == TradeLog.TradeType.sell.rawValue {
                        sellCount += tradeLog.tradePrice * tradeLog.tradeShares;
                    } else {
                        buyCount += tradeLog.tradePrice * tradeLog.tradeShares;
                    }
                }
            }
            for log in flexibleTradeLog! {
                if log.tradeType == FlexibleTradeLog.TradeType.sell.rawValue {
                    sellCount += log.tradePrice * log.tradeShares;
                } else if log.tradeType == FlexibleTradeLog.TradeType.buy.rawValue {
                    buyCount += log.tradePrice * log.tradeShares;
                }
            }
            return sellCount - buyCount;
        }
        
        var avgHoldPrice: Double {
            var totalCost = totalInvestment
            totalCost -= realTotalProfit
            return Double(totalCost) / Double(holdShares)
        }
        
        // 持有成本 = 总投入 - 总利润
        var holdingCost: Int {
            var totalCost = totalInvestment
            totalCost -= realTotalProfit
            return totalCost
        }
        
        var maxInvestment: Int {
            let sGrids = stressTest()
            var total = 0
            for grid in sGrids {
                total += grid.buyPrice * grid.shares
            }
            return total
        }
        
        var costs: [CostData] {
            var data: [CostData] = []
            var logs: [TradeLog] = []
            for grid in grids! {
                for tradeLog in grid.tradeLogs! {
                    logs.append(tradeLog)
                }
            }
            logs = logs.sorted { l1, l2 in
                l1.tradeAt < l2.tradeAt
            }
            
            var shares = 0;
            var investment = 0;
            for log in logs {
                if log.tradeType == TradeLog.TradeType.buy.rawValue {
                    shares += log.tradeShares
                    investment += log.tradeShares * log.tradePrice
                } else {
                    shares -= log.tradeShares
                    investment -= log.tradeShares * log.tradePrice
                }
                data.append(CostData(tradeDay: log.tradeAt, price: Double(investment) / Double(shares) / 1000.0))
            }
            let result = data.reduce(into: [CostData]()) { (array, item) in
                if let last = array.last, Utils.isSameDay(last.tradeDay, item.tradeDay) {
                    array[array.count - 1] = item
                } else {
                    array.append(item)
                }
            }
            return result
        }
        
        init() {}
        
        init(id: UUID = UUID(), name: String, code: String? = nil, interval: String, targetPrice: String, amount: String, maxFall: String, buyStrategy: String, incrementalBuyRatio: String? = nil, mediumLargeSwitch: Bool, mediumInterval: String? = nil, largeInterval: String? = nil,  triggerNotify: Bool, calculateInterest: Bool, interestPeriod: String? = nil, interest: String? = nil, retainProfitSwitch: Bool, retainProfit: String? = nil, fixedShares: String? = nil, createAt: Date = .now, updateAt: Date = .now) {
            self.id = id
            self.name = name
            self.code = code
            self.interval = Utils.times1000Round(price: interval)
            self.targetPrice = Utils.times1000Round(price: targetPrice)
            self.amount = Utils.times1000Round(price: amount)
            self.maxFall = Utils.times1000Round(price: maxFall)
            self.buyStrategy = Int(buyStrategy)!
            self.incrementalBuyRatio = incrementalBuyRatio == nil ? nil : Utils.times1000Round(price: incrementalBuyRatio!)
            self.fixedShares = fixedShares == nil ? nil : (fixedShares! as NSString).integerValue
            self.mediumLargeSwitch = mediumLargeSwitch
            self.mediumInterval = mediumInterval == nil ? nil : Utils.times1000Round(price: mediumInterval!)
            self.largeInterval = largeInterval == nil ? nil : Utils.times1000Round(price: largeInterval!)
            self.triggerNotify = triggerNotify
            self.calculateInterest = calculateInterest
            self.interestPeriod = interestPeriod == nil ? nil : Int(interestPeriod!)
            self.interest = interest == nil ? nil: Utils.times1000Round(price: interest!)
            self.retainProfitSwitch = retainProfitSwitch
            self.retainProfit = retainProfit == nil ? nil : Utils.times1000Round(price: retainProfit!)
            self.createAt = createAt
            self.updateAt = updateAt
        }
        
        func addGrid(grid: TradeGrid) {
            grids!.append(grid)
            
            // 更新总投入金额
            if let totalInvestmentAmount = totalInvestmentAmount {
                self.totalInvestmentAmount = totalInvestmentAmount + grid.buyTradeLog!.tradeShares * grid.buyTradeLog!.tradePrice
            } else {
                self.totalInvestmentAmount = calculateTotalInvestment()
            }
            
            // 添加底仓计息通知
            if calculateInterest {
                grid.addNotification()
            }
        }
        
        func archive() -> Bool {
            if retainShares == 0 {
                status = StrategyStatus.archive.rawValue
                profit = realTotalProfit
                return true
            }
            return false
        }
        
        func activate() {
            status = Strategy.StrategyStatus.active.rawValue
        }
        
        func isArchived() -> Bool {
            if status == Strategy.StrategyStatus.archive.rawValue {
                return true
            }
            return false
        }
        
        func sellRetainShares(price: String, shares: Int, date: Date) -> (Bool, String) {
            if shares > getRetainShares() {
                return (false, "卖出股数大于持有股数");
            }
            
            let sellPrice = Utils.times1000Round(price: price)
            if sellPrice <= 0 {
                return (false, "卖出价格应大于 0")
            }
            
            let tradeLog = FlexibleTradeLog(strategy: self, tradeType: FlexibleTradeLog.TradeType.sell.rawValue, tradeShares: shares, tradePrice: sellPrice, tradeAt: date);
            self.flexibleTradeLog!.append(tradeLog);
            self.realTotalProfitAmount = self.calculateRealTotalProfit()
            self.setRetainShares(self.calculateRetainShares())
            return (true, "");
        }
        
        func getNextTradeGrid(gridType: TradeGrid.GridType) -> TradeGrid? {
            var nextGrade = 1000;
            var curInterval: Int;
            var triggerAmount = amount;
            var curGrids = self.sortedSmallGrids;
            
            switch gridType {
            case TradeGrid.GridType.medium:
                curInterval = mediumInterval!
                curGrids = self.sortedMediumGrids
            case TradeGrid.GridType.large:
                curInterval = largeInterval!
                curGrids = self.sortedLargeGrids
            default:
                curInterval = interval
            }
            
            var sellGrade = 1000 + curInterval/100;
            
            if !curGrids.isEmpty {
                nextGrade = curGrids[0].grade - curInterval/100;
                sellGrade = curGrids[0].grade;
            } else if gridType != TradeGrid.GridType.small {
                nextGrade = nextGrade - curInterval/100
                sellGrade = 1000
            }
            
            // return nil if grade smaller than max fall
            let min = 100000 - maxFall;
            if nextGrade * 100 < min {
                return nil;
            }
            
            if (buyStrategy == BuyStrategy.incremental.rawValue) {
                let times = (1000 - nextGrade) / (interval/100);
                triggerAmount = amount * (100000 + times * incrementalBuyRatio!) / 100000;
            }
            let theoreticalBuyPrice = targetPrice * nextGrade / 1000;
            let buyShares = buyStrategy == BuyStrategy.fixedShares.rawValue ? fixedShares! : triggerAmount / theoreticalBuyPrice / 100 * 100;
            let theoreticalSellPrice = targetPrice * sellGrade / 1000;
            if (buyStrategy == BuyStrategy.fixedShares.rawValue) {
                triggerAmount = buyShares * theoreticalBuyPrice
            }
            var sellShares = buyShares;
            if retainProfitSwitch {
                sellShares = sellShares - (theoreticalSellPrice - theoreticalBuyPrice) * buyShares / theoreticalSellPrice * retainProfit! / 1000 / 100 * 100;
                if sellShares < 0 {
                    sellShares = 0
                }
            }
            return TradeGrid(gridType: gridType.rawValue, grade: nextGrade, holdShares: 0, theoreticalBuyPrice: theoreticalBuyPrice, theoreticalBuyShares: buyShares, theoreticalSellPrice: theoreticalSellPrice, theoreticalSellShares: sellShares, triggerAmount: triggerAmount);
        }
        
        func isValid() -> (Bool, String) {
            if (name.isEmpty) {
                return (false, "策略名称未填写")
            }
            
            if (targetPrice <= 0) {
                return (false, "触发价格应大于 0")
            }
            
            if (buyStrategy != BuyStrategy.fixedShares.rawValue && amount < targetPrice * 100) {
                return (false, "网格金额应至少可以买入 100 股")
            }
            
            if (buyStrategy == BuyStrategy.incremental.rawValue && incrementalBuyRatio == nil ) {
                return (false, "递增比例未填写")
            }
            
            if (buyStrategy == BuyStrategy.fixedShares.rawValue) {
                if (fixedShares == nil) {
                    return (false, "固定股数未填写")
                } else if (fixedShares! < 100) {
                    return (false, "固定股数至少 100 股")
                }
            }
            
            if (interval <= 0) {
                return (false, "网格幅度应大于 0")
            }
            if (interval >= 100000) {
                return (false, "网格幅度应小于 100%")
            }
            
            if (maxFall >= 100000) {
                return (false, "最大跌幅应小于 100%")
            }
            
            if (retainProfitSwitch && retainProfit == nil) {
                return (false, "保留份数未填写")
            }
            
            if (mediumLargeSwitch) {
                if (mediumInterval == nil) {
                    return (false, "中网幅度未填写")
                } else if (mediumInterval! <= 0) {
                    return (false, "中网幅度应大于 0")
                }
                
                if (largeInterval == nil) {
                    return (false, "大网幅度未填写")
                } else if (largeInterval! <= 0) {
                    return (false, "大网幅度应大于 0")
                }
            }
            
            if (triggerNotify && (code == nil || code!.isEmpty)) {
                return (false, "股票代码未选择")
            }
            
            if (calculateInterest && interest == nil) {
                return (false, "每期利息未填写")
            }
            
            return (true, "");
        }
        
        func editFrom(source: Strategy) {
            name = source.name
            code = source.code
            interval = source.interval
            targetPrice = source.targetPrice
            amount = source.amount
            maxFall = source.maxFall
            buyStrategy = source.buyStrategy
            incrementalBuyRatio = source.incrementalBuyRatio
            fixedShares = source.fixedShares
            mediumLargeSwitch = source.mediumLargeSwitch
            mediumInterval = source.mediumInterval
            largeInterval = source.largeInterval
            triggerNotify = source.triggerNotify
            retainProfit = source.retainProfit
            retainProfitSwitch = source.retainProfitSwitch
            updateAt = .now
            
            if calculateInterest != source.calculateInterest {
                // cal to not cal, delete all notifications
                if calculateInterest {
                    for grid in holdGrids {
                        if grid.notificationId != nil {
                            UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: [grid.notificationId!])
                            grid.notificationId = nil
                        }
                    }
                } else {
                    // not cal to cal, set all notifications
                    for grid in holdGrids {
                        let content = UNMutableNotificationContent()
                        content.title = "计息通知"
                        content.subtitle = "「\(name)」的「\(grid.displayGrade)」-「\(grid.displayGridType)」档位卖出价已更新"
                        content.sound = UNNotificationSound.default
                        
                        var dateComponents = DateComponents()
                        if source.interestPeriod == Strategy.InterestPeriod.year.rawValue {
                            dateComponents.month = Calendar.current.component(.month, from: grid.buyTradeLog!.tradeAt)
                        }
                        dateComponents.day = Calendar.current.component(.day, from: grid.buyTradeLog!.tradeAt)
                        dateComponents.hour = 9
                        dateComponents.minute = 0
                        
                        let trigger = UNCalendarNotificationTrigger(dateMatching: dateComponents, repeats: true)
                        let notificationId = UUID().uuidString
                        grid.notificationId = notificationId
                        let request = UNNotificationRequest(identifier: notificationId, content: content, trigger: trigger)
                        UNUserNotificationCenter.current().add(request) { (error) in
                            if let error = error  {
                                print("添加失败: \(error.localizedDescription)")
                            } else {
                                print("添加成功")
                            }
                        }
                    }
                }
            }
            
            calculateInterest = source.calculateInterest
            interestPeriod = source.interestPeriod
            interest = source.interest
        }
        
        func stressTest() -> [StressGrid] {
            return Strategy.stressTest(triggerPrice: targetPrice, amount: amount, buyStrategy: buyStrategy, incrementalBuyRatio: incrementalBuyRatio, interval: interval, maxFall: maxFall, mediumLargeSwitch: mediumLargeSwitch, mediumInterval: mediumInterval, largeInterval: largeInterval, fixedShares: fixedShares);
        }
        
        /*
         there are two types of grid can delete:
         1. sold grid
         2. the lowest grade of bought grid
         */
        // func deleteGrid(grid: TradeGrid) -> Bool {
        //     if grid.isSold {
        //         let index = grids!.firstIndex(of: grid)
        //         if index != nil {
        //             grids!.remove(at: index!)
        //             return true
        //         }
        //     } else {
        //         if grid.gridType == TradeGrid.GridType.small.rawValue && sortedSmallGrids[0] == grid {
        //             let index = grids!.firstIndex(of: grid)
        //             if index != nil {
        //                 grids!.remove(at: index!)
        //                 return true
        //             }
        //         } else if grid.gridType == TradeGrid.GridType.medium.rawValue && sortedMediumGrids[0] == grid {
        //             let index = grids!.firstIndex(of: grid)
        //             if index != nil {
        //                 grids!.remove(at: index!)
        //                 return true
        //             }
        //         } else if grid.gridType == TradeGrid.GridType.large.rawValue && sortedLargeGrids[0] == grid {
        //             let index = grids!.firstIndex(of: grid)
        //             if index != nil {
        //                 grids!.remove(at: index!)
        //                 return true
        //             }
        //         }
        //     }
        //     return false
        // }
        
        // func deleteFlexibleTradeLog(tradeLog: FlexibleTradeLog) -> Bool {
        //     let index = flexibleTradeLog!.firstIndex(of: tradeLog)
        //     if index != nil {
        //         flexibleTradeLog!.remove(at: index!)
        //         return true
        //     }
        //     return false
        // }
        
        static func from(syncStrategy: SyncStrategy) -> Strategy {
            let strategy = Strategy()
            strategy.name = syncStrategy.name
            strategy.code = syncStrategy.code
            strategy.interval = syncStrategy.interval * 1000
            strategy.targetPrice = syncStrategy.targetPrice
            strategy.amount = syncStrategy.amount
            strategy.maxFall = syncStrategy.maxFall * 1000
            strategy.buyStrategy = syncStrategy.buyStrategy
            strategy.incrementalBuyRatio = syncStrategy.incrementalBuyRatio == nil ? nil: syncStrategy.incrementalBuyRatio! * 1000
            strategy.mediumLargeSwitch = syncStrategy.mediumLargeSwitch
            if (strategy.mediumLargeSwitch) {
                strategy.mediumInterval = syncStrategy.mediumInterval * 1000
                strategy.largeInterval = syncStrategy.largeInterval * 1000
            }
            strategy.triggerNotify = false
            strategy.calculateInterest = false
            strategy.retainProfitSwitch = true
            strategy.retainProfit = 1000
            
            //        for grid in syncStrategy.grids {
            //            strategy.grids.append(TradeGrid.from(syncGrid: grid, strategyId: strategy.id))
            //        }
            //
            //        for grid in syncStrategy.soldGrids {
            //            strategy.soldGrids.append(TradeGrid.from(syncGrid: grid, strategyId: strategy.id))
            //        }
            
            return strategy
        }
        
        static func stressTest(triggerPrice: Int, amount: Int, buyStrategy: Int, incrementalBuyRatio: Int?, interval: Int, maxFall: Int, mediumLargeSwitch: Bool = false, mediumInterval: Int? = nil, largeInterval: Int? = nil, fixedShares: Int? = nil) -> [StressGrid] {
            var result: [StressGrid] = []
            result.append(contentsOf: calStressGrid(triggerPrice: triggerPrice, amount: amount, buyStrategy: buyStrategy, incrementalBuyRatio: incrementalBuyRatio, interval: interval, maxFall: maxFall, gridType: .small, fixedShares: fixedShares))
            if mediumLargeSwitch {
                result.append(contentsOf: calStressGrid(triggerPrice: triggerPrice, amount: amount, buyStrategy: buyStrategy, incrementalBuyRatio: incrementalBuyRatio, interval: interval, maxFall: maxFall, mediumInterval: mediumInterval, gridType: .medium, fixedShares: fixedShares))
                result.append(contentsOf: calStressGrid(triggerPrice: triggerPrice, amount: amount, buyStrategy: buyStrategy, incrementalBuyRatio: incrementalBuyRatio, interval: interval, maxFall: maxFall, largeInterval: largeInterval, gridType: .large, fixedShares: fixedShares))
            }
            return result.sorted {
                if $0.grade != $1.grade {
                    return $1.grade < $0.grade
                }
                return $0.gridType < $1.gridType
            };
        }
        
        static func calStressGrid(triggerPrice: Int, amount: Int, buyStrategy: Int, incrementalBuyRatio: Int?, interval: Int, maxFall: Int, mediumInterval: Int? = nil, largeInterval: Int? = nil, gridType: TradeGrid.GridType, fixedShares: Int? = nil) -> [StressGrid] {
            var result: [StressGrid] = [];
            let startIndex = gridType == TradeGrid.GridType.small ? 0 : 1;
            var endIndex = 0;
            var curInterval = interval
            switch gridType {
            case .small:
                endIndex = maxFall / interval
            case .medium:
                endIndex = maxFall / mediumInterval!
                curInterval = mediumInterval!
            case .large:
                endIndex = maxFall / largeInterval!
                curInterval = largeInterval!
            }
            
            if startIndex > endIndex {
                return result;
            }
            
            for index in startIndex...endIndex {
                let grade = 100000 - index * curInterval
                let buyPrice = triggerPrice * grade / 100000
                let sellPrice = triggerPrice * (grade + curInterval) / 100000
                let incTimes = curInterval * index / interval
                var currentAmount = 0
                if buyStrategy == BuyStrategy.equal.rawValue {
                    currentAmount = amount
                } else if buyStrategy == BuyStrategy.incremental.rawValue {
                    currentAmount = amount * (100000 + incTimes * incrementalBuyRatio!) / 100000
                } else if buyStrategy == BuyStrategy.fixedShares.rawValue {
                    currentAmount = fixedShares! * buyPrice
                }
                let shares = buyStrategy == BuyStrategy.fixedShares.rawValue ? fixedShares! : currentAmount / buyPrice / 100 * 100
                let profit = (sellPrice - buyPrice) * shares
                let profitRatio = profit * 10000 / (shares * buyPrice)
                result.append(StressGrid(grade: grade, buyPrice: buyPrice, sellPrice: sellPrice, shares: shares, profit: profit, profitRatio: profitRatio, gridType: gridType.rawValue))
            }
            return result;
        }
        
        static func isValidForStressTest(triggerPrice: Int, amount: Int, buyStrategy: Int, incrementalBuyRatio: Int?, interval: Int, maxFall: Int, mediumLargeSwitch: Bool = false, mediumInterval: Int? = nil, largeInterval: Int? = nil, fixedShares: Int? = nil) -> (Bool, String) {
            if (triggerPrice <= 0) {
                return (false, "触发价格应大于 0")
            }
            
            if (buyStrategy != BuyStrategy.fixedShares.rawValue && amount < triggerPrice * 100) {
                return (false, "网格金额应至少可以买入 100 股")
            }
            
            if (buyStrategy == BuyStrategy.incremental.rawValue && incrementalBuyRatio == nil ) {
                return (false, "递增比例未填写")
            }
            
            if (buyStrategy == BuyStrategy.fixedShares.rawValue) {
                if (fixedShares == nil) {
                    return (false, "固定股数未填写")
                }
                if (fixedShares! < 100) {
                    return (false, "固定股数至少 100 股")
                }
            }
            
            if (interval <= 0) {
                return (false, "网格幅度应大于 0")
            }
            if (interval >= 100000) {
                return (false, "网格幅度应小于 100%")
            }
            
            if (maxFall >= 100000) {
                return (false, "最大跌幅应小于 100%")
            }
            
            if (mediumLargeSwitch) {
                if (mediumInterval == nil) {
                    return (false, "中网幅度未填写")
                } else if (mediumInterval! >= 100000) {
                    return (false, "中网幅度应小于 100%")
                }
                
                if (largeInterval == nil) {
                    return (false, "大网幅度未填写")
                } else if (largeInterval! >= 100000) {
                    return (false, "大网幅度应小于 100%")
                }
            }
            
            return (true, "")
        }
    }
}

struct CostData: Identifiable {
    let id = UUID()
    let tradeDay: Date
    let price: Double
    init(tradeDay: Date, price: Double) {
        self.tradeDay = tradeDay
        self.price = price
    }
}

class StressGrid: Identifiable {
    var grade: Int
    var buyPrice: Int
    var sellPrice: Int
    var shares: Int
    var profit: Int
    var profitRatio: Int
    var gridType: Int
    
    var displayGrade: String {
        get {
            let doubleValue = Double(grade) / 1000.0
            return String(format: "%.3f", doubleValue)
        }
    }
    
    var displayBuyPrice: String {
        get {
            let doubleValue = Double(buyPrice) / 1000.0
            return String(format: "%.3f", doubleValue)
        }
    }
    
    var displaySellPrice: String {
        get {
            let doubleValue = Double(sellPrice) / 1000.0
            return String(format: "%.3f", doubleValue)
        }
    }
    
    var displayShares: String {
        get {
            return String(shares)
        }
    }
    
    var displayProfit: String {
        get {
            let doubleValue = Double(profit) / 1000.0
            return String(format: "%.2f", doubleValue)
        }
    }
    
    var displayProfitRatio: String {
        get {
            let doubleValue = Double(profitRatio) / 100.0
            return String(format: "%.2f", doubleValue) + "%"
        }
    }
    
    init(grade: Int, buyPrice: Int, sellPrice: Int, shares: Int, profit: Int, profitRatio: Int, gridType: Int = 0) {
        self.grade = grade
        self.buyPrice = buyPrice
        self.sellPrice = sellPrice
        self.shares = shares
        self.profit = profit
        self.profitRatio = profitRatio
        self.gridType = gridType
    }
}
