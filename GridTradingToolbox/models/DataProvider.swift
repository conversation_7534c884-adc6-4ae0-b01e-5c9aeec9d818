//
//  DataProvider.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/11/19.
//

import Foundation
import SwiftData
import SwiftUI

public final class DataProvider: Sendable {
    public static let shared = DataProvider()
    
    
//    public func dataHandlerCreator(modelContainer: ModelContainer) -> @Sendable () async -> DataHandler {
//        return { DataHandler(modelContainer: modelContainer) }
//    }
    
}

public struct DataHandlerKey: EnvironmentKey {
  public static let defaultValue: @Sendable () async -> DataHandler? = { nil }
}

extension EnvironmentValues {
  public var dataHandler: @Sendable () async -> DataHandler? {
    get { self[DataHandlerKey.self] }
    set { self[DataHandlerKey.self] = newValue }
  }
}
