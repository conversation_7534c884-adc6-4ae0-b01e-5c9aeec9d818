//
//  StrategyService.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2025/3/3.
//

import Foundation

struct GridStressTest: Identifiable {
    let id = UUID()
    let grade: Double
    let buyPrice: Int
    let sellPrice: Int
    let shares: Int
    let amount: Int
    let gridSize: Int
    let profit: Int
    let profitRatio: Int
    
    var displayGrade: String {
        return Utils.trimTrailingZeros(from: String(format: "%.3f", grade))
    }
    
    var displayBuyPrice: String {
        let doubleValue = Double(buyPrice) / 1000.0
        return String(format: "%.3f", doubleValue)
    }
    
    var displaySellPrice: String {
        let doubleValue = Double(sellPrice) / 1000.0
        return String(format: "%.3f", doubleValue)
    }
    
    var displayShares: String {
        return String(shares)
    }
    
    var displayAmount: String {
        let doubleValue = Double(amount) / 1000.0
        return Utils.trimTrailingZeros(from: String(format: "%.2f", doubleValue))
    }
    
    var displayProfit: String {
        let doubleValue = Double(profit) / 1000.0
        return Utils.trimTrailingZeros(from: String(format: "%.2f", doubleValue))
    }
    
    var displayProfitRatio: String {
        let doubleValue = Double(profitRatio) / 100.0
        return String(format: "%.2f", doubleValue) + "%"
    }
    
    var displayGridSize: String {
        switch gridSize {
        case Strategy.GridSizeType.small.rawValue:
            return "小网格"
        case Strategy.GridSizeType.medium.rawValue:
            return "中网格"
        case Strategy.GridSizeType.large.rawValue:
            return "大网格"
        default:
            return "未知"
        }
    }
}

class StrategyService {
    // 压力测试主入口
    static func stressTest(_ strategy: Strategy) -> [GridStressTest] {
        var result = [GridStressTest]()
        
        // 计算小网格
        result.append(contentsOf: calculateGrids(
            strategy: strategy,
            gridSize: .small,
            riseInterval: strategy.getRiseInterval(),
            fallInterval: strategy.getFallInterval()
        ))
        
        // 计算中网格
        if strategy.isMediumGridEnabled {
            result.append(contentsOf: calculateGrids(
                strategy: strategy,
                gridSize: .medium,
                riseInterval: strategy.getMediumRiseInterval(),
                fallInterval: strategy.getMediumFallInterval(),
                sressTestResults: result
            ))
        }
        
        // 计算大网格
        if strategy.isLargeGridEnabled {
            result.append(contentsOf: calculateGrids(
                strategy: strategy,
                gridSize: .large,
                riseInterval: strategy.getLargeRiseInterval(),
                fallInterval: strategy.getLargeFallInterval(),
                sressTestResults: result
            ))
        }
        
        // 按买入价格降序排序
        return result.sorted { $0.buyPrice > $1.buyPrice }
    }
    
    // 计算指定类型的网格
    private static func calculateGrids(
        strategy: Strategy,
        gridSize: Strategy.GridSizeType,
        riseInterval: Int,
        fallInterval: Int,
        sressTestResults: [GridStressTest] = []
    ) -> [GridStressTest] {
        var result = [GridStressTest]()
        let basePrice = strategy.targetPrice
        let maxFall = strategy.maxFall
        
        // 计算网格数量
        let gridCount: Int
        if strategy.gridType == Strategy.GridType.geometric.rawValue {
            // 等比网格
            // 用对数计算: log(1-maxFall%) / log(1-fallInterval%)
            let maxFallRatio = Double(maxFall) / 100000.0
            let fallRatio = Double(fallInterval) / 100000.0
            gridCount = Int(log(1 - maxFallRatio) / log(1 - fallRatio))
        } else {
            // 等差网格
            gridCount = maxFall / fallInterval
        }
        if gridCount <= 0 {
            return result
        }
        
        // 遍历计算每个网格
        for i in 0...gridCount {
            var buyPrice: Int
            var sellPrice: Int
            var grade: Double
            
            if strategy.gridType == Strategy.GridType.geometric.rawValue {
                // 等比网格
                let fallRatio = Double(fallInterval) / 100000.0
                let riseRatio = Double(riseInterval) / 100000.0
                let multiplier = pow(1 - fallRatio, Double(i))
                grade = multiplier
                
                buyPrice = Int(Double(basePrice) * multiplier)
                sellPrice = Int(Double(buyPrice) / (1 - riseRatio))
            } else {
                // 等差网格
                buyPrice = basePrice - basePrice * fallInterval * i / 100000
                sellPrice = buyPrice + basePrice * riseInterval / 100000
                grade = 1 - Double(i * fallInterval) / 100000.0
            }
            
            
            // 计算买入数量和金额
            let (shares, amount) = calculateBuyAmount(
                strategy: strategy,
                buyPrice: buyPrice,
                gridIndex: i,
                fallInterval: fallInterval,
                stressTestResults: sressTestResults,
                gridSize: gridSize
            )
            
            // 计算预期收益
            let profit = (sellPrice - buyPrice) * shares
            let profitRatio = shares == 0 ? 0 :  profit * 10000 / (buyPrice * shares)
            
            result.append(GridStressTest(
                grade: grade,
                buyPrice: buyPrice,
                sellPrice: sellPrice,
                shares: shares,
                amount: amount,
                gridSize: gridSize.rawValue,
                profit: profit,
                profitRatio: profitRatio
            ))
        }
        
        // 移除中大网的第一个网格，不在基础价格买入
        if (gridSize != Strategy.GridSizeType.small && result.count > 0) {
            result.remove(at: 0)
        }
        return result
    }
    
    // 计算买入数量和金额
    private static func calculateBuyAmount(
        strategy: Strategy,
        buyPrice: Int,
        gridIndex: Int,
        fallInterval: Int,
        stressTestResults: [GridStressTest],
        gridSize: Strategy.GridSizeType
    ) -> (shares: Int, amount: Int) {
        var currentAmount = 0
        var shares = 0
        
        // 中大网格继承距离当前价格最近的小网格的买入金额
        if gridSize != Strategy.GridSizeType.small {
            // 过滤出小网格并找到价格最接近的一个
            if let closestSmallGrid = stressTestResults
                .filter({ $0.gridSize == Strategy.GridSizeType.small.rawValue })
                .min(by: { abs($0.buyPrice - buyPrice) < abs($1.buyPrice - buyPrice) }) {
                currentAmount = closestSmallGrid.amount
                if Strategy.BuyStrategy.fixedShares.rawValue == strategy.buyStrategy {
                    shares = strategy.fixedShares!
                } else {
                    shares = currentAmount / buyPrice / 100 * 100
                }
                return (shares, currentAmount)
            }
        }
        
        switch strategy.buyStrategy {
        case Strategy.BuyStrategy.fixedAmount.rawValue:
            currentAmount = strategy.amount
            shares = currentAmount / buyPrice / 100 * 100
            
        case Strategy.BuyStrategy.fixedShares.rawValue:
            shares = strategy.fixedShares!
            currentAmount = shares * buyPrice
            
        case Strategy.BuyStrategy.arithmetic.rawValue:
            // 等差递增
            let increment = Int(Double(strategy.amount * gridIndex * strategy.incrementalBuyRatio!) / 100000)
            currentAmount = strategy.amount + increment
            shares = currentAmount / buyPrice / 100 * 100
            
        case Strategy.BuyStrategy.geometricInc.rawValue:
            // 等比递增
            let ratio = 1.0 + Double(strategy.incrementalBuyRatio!) / 100000.0
            let multiplier = pow(ratio, Double(gridIndex))
            currentAmount = Int(Double(strategy.amount) * multiplier)
            shares = currentAmount / buyPrice / 100 * 100
            
        default:
            break
        }
        
        return (shares, currentAmount)
    }
    
    // 验证策略参数
    static func isValidForStressTest(_ strategy: Strategy) -> (Bool, String) {
        // 验证基准价格
        if strategy.targetPrice <= 0 {
            return (false, "基准价格应大于 0")
        }
        
        // 验证网格幅度
        if strategy.riseInterval! <= 0 || strategy.riseInterval! >= 100000 {
            return (false, "上涨网格幅度应在 0-100% 之间")
        }
        if strategy.fallInterval! <= 0 || strategy.fallInterval! >= 100000 {
            return (false, "下跌网格幅度应在 0-100% 之间")
        }
        
        // 验证最大跌幅
        if strategy.maxFall >= 100000 {
            return (false, "最大跌幅应小于 100%")
        }
        
        // 验证买入策略
        switch strategy.buyStrategy {
        case Strategy.BuyStrategy.fixedAmount.rawValue:
            // 验证基础买入金额
            if strategy.amount < strategy.targetPrice * 100 {
                return (false, "基础买入金额应至少可以买入 100 股")
            }
            
        case Strategy.BuyStrategy.fixedShares.rawValue:
            // 验证固定股数
            if let shares = strategy.fixedShares {
                if shares < 100 {
                    return (false, "固定买入股数应至少为 100 股")
                }
            } else {
                return (false, "固定买入股数未设置")
            }
            
        case Strategy.BuyStrategy.arithmetic.rawValue,
             Strategy.BuyStrategy.geometricInc.rawValue:
            // 验证基础买入金额
            if strategy.amount < strategy.targetPrice * 100 {
                return (false, "基础买入金额应至少可以买入 100 股")
            }
            // 验证递增比例
            if strategy.incrementalBuyRatio == nil {
                return (false, "递增比例未设置")
            }
            if strategy.incrementalBuyRatio! <= 0 {
                return (false, "递增比例应大于 0")
            }
            
        default:
            return (false, "无效的买入策略类型")
        }
        
        // 验证中网格参数
        if strategy.isMediumGridEnabled {
            if strategy.mediumRiseInterval == nil || strategy.mediumRiseInterval! <= 0 || strategy.mediumRiseInterval! >= 100000 {
                return (false, "中网格上涨幅度应在 0-100% 之间")
            }
            if strategy.mediumFallInterval == nil || strategy.mediumFallInterval! <= 0 || strategy.mediumFallInterval! >= 100000 {
                return (false, "中网格下跌幅度应在 0-100% 之间")
            }
        }
        
        // 验证大网格参数
        if strategy.isLargeGridEnabled {
            if strategy.largeRiseInterval == nil || strategy.largeRiseInterval! <= 0 || strategy.largeRiseInterval! >= 100000 {
                return (false, "大网格上涨幅度应在 0-100% 之间")
            }
            if strategy.largeFallInterval == nil || strategy.largeFallInterval! <= 0 || strategy.largeFallInterval! >= 100000 {
                return (false, "大网格下跌幅度应在 0-100% 之间")
            }
        }
        
        return (true, "")
    }
}
