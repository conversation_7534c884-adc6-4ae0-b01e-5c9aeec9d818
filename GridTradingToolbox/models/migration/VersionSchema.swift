//
//  VersionSchema.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2025/3/1.
//

import Foundation
import SwiftData

enum SchemaV1: VersionedSchema {
    static var versionIdentifier = Schema.Version(1, 0, 0)
    
    static var models: [any PersistentModel.Type] {
        [SchemaV1.Strategy.self, SchemaV1.TradeGrid.self, SchemaV1.FlexibleTradeLog.self, SchemaV1.TradeLog.self, SchemaV1.Stock.self, SchemaV1.FavoriteStock.self, SchemaV1.StockAccount.self]
    }
}
