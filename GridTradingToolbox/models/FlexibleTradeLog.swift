//
//  FlexibleTradeLog.swift
//  网格交易工具箱
//
//  Created by 陈敏杰 on 2024/3/31.
//

import Foundation
import SwiftData

typealias FlexibleTradeLog = SchemaV1.FlexibleTradeLog

extension SchemaV1 {
    @Model
    class FlexibleTradeLog: Identifiable, Equatable {
        enum TradeType: Int {
            case buy = 0, sell
        }
        
        var id: UUID = UUID();
        var strategy: Strategy? = nil;
        var tradeType: Int = TradeType.sell.rawValue;
        var tradeShares: Int = 0
        var tradePrice: Int = 0
        var tradeAt: Date = Date.now
        var createAt: Date = Date.now
        
        var displayTradePrice: String {
            get {
                let doubleValue = Double(tradePrice) / 1000.0
                return Utils.trimTrailingZeros(from: String(format: "%.3f", doubleValue))
            }
        }
        
        var displayTradeAmount: String {
            let price = Double(tradePrice) / 1000.0
            return Utils.trimTrailingZeros(from: String(format: "%.3f", price * Double(tradeShares)))
        }
        
        init(strategy: Strategy? = nil, tradeType: Int, tradeShares: Int, tradePrice: Int, tradeAt: Date, createAt: Date = .now) {
            self.strategy = strategy
            self.tradeType = tradeType
            self.tradeShares = tradeShares
            self.tradePrice = tradePrice
            self.tradeAt = tradeAt
            self.createAt = createAt
        }
    }
}
