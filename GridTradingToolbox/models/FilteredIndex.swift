import Foundation

struct FilteredIndex: Identifiable, Codable {
    let id: String
    let code: String
    let name: String
    let metricType: String
    let closePoint: Double?
    let peTtm5Y: Double
    let peTtmPercentile5Y: Double
    let pb5Y: Double
    let pbPercentile5Y: Double
    let peTtm10Y: Double
    let peTtmPercentile10Y: Double
    let pb10Y: Double
    let pbPercentile10Y: Double
    let updateTimestamp: Double
    
    init(from response: FilterResponse) {
        self.id = UUID().uuidString
        self.code = response.code
        self.name = response.name
        self.metricType = response.metricType
        self.closePoint = response.closePoint
        self.peTtm5Y = response.peTtm5Y
        self.peTtmPercentile5Y = response.peTtmPercentile5Y
        self.pb5Y = response.pb5Y
        self.pbPercentile5Y = response.pbPercentile5Y
        self.peTtm10Y = response.peTtm10Y
        self.peTtmPercentile10Y = response.peTtmPercentile10Y
        self.pb10Y = response.pb10Y
        self.pbPercentile10Y = response.pbPercentile10Y
        self.updateTimestamp = response.updateTimestamp
    }
}

struct FilterRequest: Codable {
    var pePercentile5Y: Double?
    var pePercentile10Y: Double?
    var pbPercentile5Y: Double?
    var pbPercentile10Y: Double?
    let weightingMethods: [String]
}

struct FilterResponse: Codable {
    let code: String
    let name: String
    let metricType: String
    let closePoint: Double?
    let peTtm5Y: Double
    let peTtmPercentile5Y: Double
    let pb5Y: Double
    let pbPercentile5Y: Double
    let peTtm10Y: Double
    let peTtmPercentile10Y: Double
    let pb10Y: Double
    let pbPercentile10Y: Double
    let updateTimestamp: Double
}

struct FilterResult: Codable {
    let success: [FilterResponse]
    let failed: [String]
}