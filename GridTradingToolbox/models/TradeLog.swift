//
//  TradeLog.swift
//  GridTradingToolbox
//
//  Created by 陈敏杰 on 2024/1/16.
//

import Foundation
import SwiftData

typealias TradeLog = SchemaV1.TradeLog

extension SchemaV1 {
    @Model
    class TradeLog {
        enum TradeType: Int {
            case buy = 0, sell, sellRetain
        }
        
        var id: UUID = UUID()
        var grid: TradeGrid? = nil
        var tradeType: Int = 0
        var tradeShares: Int = 0
        var tradePrice: Int = 0
        var tradeAt: Date = Date.now
        var createAt: Date = Date.now
        var mood: String? = nil
        var note: String? = nil
        
        var displayTradePrice: String {
            get {
                let doubleValue = Double(tradePrice) / 1000.0
                return Utils.trimTrailingZeros(from: String(format: "%.3f", doubleValue))
            }
        }
        
        var displayTradeAmount: String {
            let price = Double(tradePrice) / 1000.0
            return Utils.trimTrailingZeros(from: String(format: "%.3f", price * Double(tradeShares)))
        }
        
        var isBuy: Bool {
            return tradeType == TradeType.buy.rawValue
        }
        
        init(id: UUID = UUID(), grid: TradeGrid? = nil, tradeType: Int, tradeShares: Int, tradePrice: Int, tradeAt: Date = .now, createAt: Date = .now, mood: String? = nil, note: String? = nil) {
            self.id = id
            self.grid = grid
            self.tradeType = tradeType
            self.tradeShares = tradeShares
            self.tradePrice = tradePrice
            self.tradeAt = tradeAt
            self.createAt = createAt
            self.mood = mood
            self.note = note
        }
        
        static func from(syncTradeLog: SyncTradeLog, grid: TradeGrid? = nil) -> TradeLog {
            let tradeLog = TradeLog(grid: grid, tradeType: syncTradeLog.tradeType, tradeShares: syncTradeLog.tradeShares, tradePrice: syncTradeLog.tradePrice)
            tradeLog.tradeAt = syncTradeLog.tradeAt
            tradeLog.createAt = syncTradeLog.createAt
            return tradeLog;
        }
    }
}

