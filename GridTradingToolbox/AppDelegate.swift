//
//  AppDelegate.swift
//  GridTradingToolbox
//
//  Created by 陈敏杰 on 2024/2/3.
//

import Foundation
import UserNotifications
import UIKit

class AppDelegate: NSObject, UIApplicationDelegate {
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> <PERSON><PERSON> {
        // Override point for customization after application launch.
        UNUserNotificationCenter.current().delegate = self
        return true
    }

    // UNUserNotificationCenterDelegate methods

//    func userNotificationCenter(_ center: UNUserNotificationCenter,
//                                willPresent notification: UNNotification,
//                                withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
//
//        // If you want to show banner, sound and badge when app is in foreground
//        completionHandler([.banner, .sound, .badge])
//    }
}

extension AppDelegate: UNUserNotificationCenterDelegate {
    func userNotificationCenter(_ center: UNUserNotificationCenter,
                                willPresent notification: UNNotification,
                                withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {

        // If you want to show banner, sound and badge when app is in foreground
        completionHandler([.banner, .sound, .badge])
    }
}
